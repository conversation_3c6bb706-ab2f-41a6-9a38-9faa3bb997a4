const { pool } = require('./config/db');

async function showDiscussionsTable() {
  try {
    console.log('数据库配置:');
    console.log('Host: localhost');
    console.log('User: root');
    console.log('Database: elearning');
    console.log('');
    
    // 查询discussions表结构
    try {
      const [discussionColumns] = await pool.query('DESCRIBE discussions');
      console.log('\ndiscussions表结构:');
      discussionColumns.forEach(col => {
        console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
      });
    } catch (error) {
      console.log('\ndiscussions表不存在或无法访问');
      console.error(error);
    }
    
    // 查询discussion_replies表结构
    try {
      const [replyColumns] = await pool.query('DESCRIBE discussion_replies');
      console.log('\ndiscussion_replies表结构:');
      replyColumns.forEach(col => {
        console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
      });
    } catch (error) {
      console.log('\ndiscussion_replies表不存在或无法访问');
      console.error(error);
    }
    
    // 查询discussions表中的数据
    try {
      const [discussions] = await pool.query('SELECT * FROM discussions LIMIT 5');
      console.log('\ndiscussions表数据示例:');
      console.log(JSON.stringify(discussions, null, 2));
    } catch (error) {
      console.log('\n无法查询discussions表数据');
      console.error(error);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('查询失败:', error);
    process.exit(1);
  }
}

showDiscussionsTable();
