<template>
  <div class="test-submissions">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else>
      <!-- 测试信息 -->
      <div class="test-info-card">
        <div class="test-info">
          <h2>{{ test.title }}</h2>
          <p class="test-description">{{ test.description }}</p>
          <div class="test-meta">
            <span><i class="el-icon-date"></i> {{ formatDateTime(test.start_time) }} - {{ formatDateTime(test.end_time) }}</span>
            <span><i class="el-icon-time"></i> {{ test.time_limit ? `${test.time_limit}分钟` : '无限制' }}</span>
            <span><i class="el-icon-medal"></i> 总分: {{ test.total_score || 0 }}</span>
            <el-tag :type="getStatusType(test.status)">
              {{ getStatusText(test.status) }}
            </el-tag>
          </div>
        </div>
        <div class="test-actions">
          <el-button type="primary" @click="goToTestEditor">返回测试编辑</el-button>
        </div>
      </div>
      
      <!-- 提交列表 -->
      <div class="submissions-section">
        <div class="section-header">
          <h3>提交列表</h3>
          <div class="filter-bar">
            <el-select v-model="statusFilter" placeholder="状态筛选" clearable @change="fetchSubmissions">
              <el-option label="已提交" value="submitted"></el-option>
              <el-option label="已评分" value="graded"></el-option>
            </el-select>
            <el-button type="primary" @click="fetchSubmissions">刷新</el-button>
          </div>
        </div>
        
        <div v-if="submissions.length === 0" class="empty-data">
          <el-empty description="暂无提交记录"></el-empty>
        </div>
        
        <div v-else>
          <el-table :data="submissions" style="width: 100%" border>
            <el-table-column prop="student_name" label="学生" min-width="120"></el-table-column>
            <el-table-column prop="status" label="状态" width="120">
              <template slot-scope="scope">
                <el-tag :type="getSubmissionStatusType(scope.row.status)">
                  {{ getSubmissionStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="提交时间" width="170">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.submit_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_score" label="得分" width="100" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.is_fully_graded">
                  {{ scope.row.total_score }} / {{ test.total_score }}
                </span>
                <el-tag v-else type="warning" size="mini">未完全评分</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template slot-scope="scope">
                <el-button 
                  type="primary" 
                  size="mini" 
                  @click="gradeSubmission(scope.row)">
                  {{ scope.row.is_fully_graded ? '查看' : '评分' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              @current-change="handlePageChange"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              layout="total, prev, pager, next"
              :total="total">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestSubmissions',
  props: {
    id: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      testId: parseInt(this.id),
      test: {},
      submissions: [],
      loading: true,
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  methods: {
    async fetchTestDetails() {
      try {
        const response = await this.$http.get(`/api/tests/${this.testId}`)
        
        if (response.data.success) {
          this.test = response.data.data
        } else {
          throw new Error(response.data.message || '获取测试详情失败')
        }
      } catch (error) {
        console.error('获取测试详情失败:', error)
        this.$message.error('获取测试详情失败，请稍后再试')
      }
    },
    async fetchSubmissions() {
      this.loading = true
      try {
        await this.fetchTestDetails()
        
        const response = await this.$http.get(`/api/submissions/tests/${this.testId}/submissions`, {
          params: {
            status: this.statusFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })
        
        if (response.data.success) {
          this.submissions = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取提交列表失败')
        }
      } catch (error) {
        console.error('获取提交列表失败:', error)
        this.$message.error('获取提交列表失败，请稍后再试')
        
        // 使用模拟数据（仅在开发环境或API失败时）
        this.submissions = [
          {
            id: 1,
            student_id: 101,
            student_name: '张三',
            test_id: this.testId,
            status: 'graded',
            start_time: '2023-09-15 10:15:00',
            submit_time: '2023-09-15 10:45:00',
            total_score: 18,
            is_fully_graded: true
          },
          {
            id: 2,
            student_id: 102,
            student_name: '李四',
            test_id: this.testId,
            status: 'submitted',
            start_time: '2023-09-15 10:20:00',
            submit_time: '2023-09-15 10:50:00',
            total_score: 12,
            is_fully_graded: false
          }
        ]
        this.total = 2
        
        // 模拟测试数据
        if (!this.test.title) {
          this.test = {
            id: this.testId,
            title: '示例测试',
            description: '这是一个示例测试',
            status: 'published',
            total_score: 20,
            time_limit: 30,
            start_time: '2023-09-15 10:00:00',
            end_time: '2023-09-15 11:00:00'
          }
        }
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchSubmissions()
    },
    formatDateTime(dateTime) {
      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm')
    },
    getStatusType(status) {
      const types = {
        'draft': 'info',
        'published': 'success',
        'closed': 'warning'
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        'draft': '草稿',
        'published': '已发布',
        'closed': '已关闭'
      }
      return texts[status] || status
    },
    getSubmissionStatusType(status) {
      const types = {
        'in_progress': 'info',
        'submitted': 'warning',
        'graded': 'success'
      }
      return types[status] || 'info'
    },
    getSubmissionStatusText(status) {
      const texts = {
        'in_progress': '进行中',
        'submitted': '已提交',
        'graded': '已评分'
      }
      return texts[status] || status
    },
    goToTestEditor() {
      this.$router.push(`/teacher/tests/${this.testId}`)
    },
    gradeSubmission(submission) {
      this.$router.push(`/teacher/submissions/${submission.id}`)
    }
  },
  created() {
    this.fetchSubmissions()
  }
}
</script>

<style scoped>
.test-submissions {
  padding: 20px;
}

.loading-container {
  padding: 20px;
}

.test-info-card {
  display: flex;
  justify-content: space-between;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 30px;
}

.test-info {
  flex: 1;
}

.test-info h2 {
  margin-top: 0;
  margin-bottom: 10px;
}

.test-description {
  margin-bottom: 15px;
  color: #606266;
}

.test-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  color: #909399;
}

.test-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
}

.submissions-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
}

.filter-bar {
  display: flex;
  gap: 10px;
}

.empty-data {
  padding: 40px 0;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .test-info-card {
    flex-direction: column;
  }

  .test-actions {
    margin-top: 15px;
    flex-direction: row;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .filter-bar {
    width: 100%;
  }
}
</style>
