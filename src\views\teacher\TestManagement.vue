<template>
  <div class="test-management">
    <div class="page-header">
      <h1>课程测试管理</h1>
      <el-button type="primary" @click="openTestDialog()">
        <i class="el-icon-plus"></i> 创建测试
      </el-button>
    </div>

    <div class="filter-bar">
      <el-select v-model="courseId" placeholder="选择课程" @change="fetchTests">
        <el-option
          v-for="course in courses"
          :key="course.id"
          :label="course.title"
          :value="course.id">
        </el-option>
      </el-select>

      <el-select v-model="statusFilter" placeholder="测试状态" clearable @change="fetchTests">
        <el-option label="草稿" value="draft"></el-option>
        <el-option label="已发布" value="published"></el-option>
        <el-option label="已结束" value="ended"></el-option>
      </el-select>

      <el-input
        placeholder="搜索测试"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchTests"
        @keyup.enter.native="fetchTests">
      </el-input>

      <el-button type="primary" @click="fetchTests">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="!courseId" class="empty-data">
      <el-empty description="请选择一个课程"></el-empty>
    </div>

    <div v-else-if="tests.length === 0" class="empty-data">
      <el-empty description="暂无测试数据"></el-empty>
    </div>

    <el-table
      v-else
      :data="tests"
      style="width: 100%"
      border>
      <el-table-column
        prop="title"
        label="测试标题"
        min-width="200">
      </el-table-column>

      <el-table-column
        prop="description"
        label="描述"
        min-width="250">
        <template slot-scope="scope">
          <span v-if="scope.row.description">{{ scope.row.description }}</span>
          <span v-else class="text-muted">无描述</span>
        </template>
      </el-table-column>

      <el-table-column
        label="测试时间"
        width="300">
        <template slot-scope="scope">
          <div>开始: {{ formatDateTime(scope.row.start_time) }}</div>
          <div>结束: {{ formatDateTime(scope.row.end_time) }}</div>
          <div v-if="scope.row.duration">时长: {{ scope.row.duration }} 分钟</div>
        </template>
      </el-table-column>

      <el-table-column
        label="分数设置"
        width="120">
        <template slot-scope="scope">
          <div>总分: {{ scope.row.total_score }}</div>
          <div>及格: {{ scope.row.passing_score }}</div>
        </template>
      </el-table-column>

      <el-table-column
        prop="is_published"
        label="状态"
        width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row)">
            {{ getStatusText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="280">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="manageQuestions(scope.row)">
            题目
          </el-button>
          <el-button
            size="mini"
            @click="viewSubmissions(scope.row)">
            提交
          </el-button>
          <el-button
            size="mini"
            type="warning"
            @click="openTestDialog(scope.row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            :type="scope.row.is_published ? 'info' : 'success'"
            @click="togglePublishStatus(scope.row)">
            {{ scope.row.is_published ? '取消发布' : '发布' }}
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="deleteTest(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container" v-if="tests.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>

    <!-- 测试表单对话框 -->
    <el-dialog
      :title="testForm.id ? '编辑测试' : '创建测试'"
      :visible.sync="dialogVisible"
      width="700px"
      @close="resetForm">
      <el-form :model="testForm" :rules="rules" ref="testForm" label-width="100px">
        <el-form-item label="课程" prop="course_id" v-if="!testForm.id">
          <el-select v-model="testForm.course_id" placeholder="选择课程" style="width: 100%">
            <el-option
              v-for="course in courses"
              :key="course.id"
              :label="course.title"
              :value="course.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="测试标题" prop="title">
          <el-input v-model="testForm.title" placeholder="请输入测试标题"></el-input>
        </el-form-item>

        <el-form-item label="测试描述" prop="description">
          <el-input
            type="textarea"
            v-model="testForm.description"
            placeholder="请输入测试描述"
            :rows="3">
          </el-input>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="start_time">
              <el-date-picker
                v-model="testForm.start_time"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="end_time">
              <el-date-picker
                v-model="testForm.end_time"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="时长(分钟)" prop="duration">
              <el-input-number v-model="testForm.duration" :min="1" :max="300"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总分" prop="total_score">
              <el-input-number v-model="testForm.total_score" :min="1" :max="1000"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="及格分" prop="passing_score">
              <el-input-number v-model="testForm.passing_score" :min="1" :max="testForm.total_score"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="发布状态" prop="is_published">
          <el-switch
            v-model="testForm.is_published"
            active-text="已发布"
            inactive-text="草稿">
          </el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveTest" :loading="saving">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TestManagement',
  data() {
    return {
      courses: [],
      tests: [],
      loading: false,
      saving: false,
      courseId: '',
      searchQuery: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      testForm: {
        id: '',
        course_id: '',
        title: '',
        description: '',
        start_time: '',
        end_time: '',
        duration: 60,
        total_score: 100,
        passing_score: 60,
        is_published: false
      },
      rules: {
        course_id: [
          { required: true, message: '请选择课程', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入测试标题', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        start_time: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        end_time: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        duration: [
          { required: true, message: '请设置测试时长', trigger: 'blur' }
        ],
        total_score: [
          { required: true, message: '请设置总分', trigger: 'blur' }
        ],
        passing_score: [
          { required: true, message: '请设置及格分', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async fetchCourses() {
      try {
        const response = await this.$http.get('/api/courses')
        if (response.data.success) {
          this.courses = response.data.data

          // 如果有课程，默认选择第一个
          if (this.courses.length > 0 && !this.courseId) {
            this.courseId = this.courses[0].id
            this.fetchTests()
          }
        }
      } catch (error) {
        console.error('获取课程失败:', error)
        this.$message.error('获取课程列表失败，请稍后再试')
      }
    },
    async fetchTests() {
      if (!this.courseId) return

      this.loading = true
      try {
        const response = await this.$http.get(`/api/exams/course/${this.courseId}`, {
          params: {
            search: this.searchQuery,
            status: this.statusFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.tests = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取测试失败')
        }
      } catch (error) {
        console.error('获取测试失败:', error)
        this.$message.error('获取测试列表失败，请稍后再试')
        this.tests = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchTests()
    },
    openTestDialog(test = null) {
      if (test) {
        // 编辑现有测试
        this.testForm = {
          id: test.id,
          course_id: test.course_id,
          title: test.title,
          description: test.description,
          start_time: test.start_time,
          end_time: test.end_time,
          duration: test.duration,
          total_score: test.total_score,
          passing_score: test.passing_score,
          is_published: test.is_published
        }
      } else {
        // 创建新测试
        this.testForm = {
          id: '',
          course_id: this.courseId || '',
          title: '',
          description: '',
          start_time: '',
          end_time: '',
          duration: 60,
          total_score: 100,
          passing_score: 60,
          is_published: false
        }
      }

      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.testForm.clearValidate()
      })
    },
    resetForm() {
      this.testForm = {
        id: '',
        course_id: '',
        title: '',
        description: '',
        start_time: '',
        end_time: '',
        duration: 60,
        total_score: 100,
        passing_score: 60,
        is_published: false
      }
    },
    async saveTest() {
      this.$refs.testForm.validate(async valid => {
        if (valid) {
          // 验证开始时间和结束时间
          if (new Date(this.testForm.end_time) <= new Date(this.testForm.start_time)) {
            this.$message.error('结束时间必须晚于开始时间')
            return
          }

          // 验证及格分不能高于总分
          if (this.testForm.passing_score > this.testForm.total_score) {
            this.$message.error('及格分不能高于总分')
            return
          }

          this.saving = true

          try {
            let response

            // 准备数据
            const testData = {
              title: this.testForm.title,
              description: this.testForm.description,
              course_id: this.testForm.course_id,
              start_time: this.$moment(this.testForm.start_time).format('YYYY-MM-DD HH:mm:ss'),
              end_time: this.$moment(this.testForm.end_time).format('YYYY-MM-DD HH:mm:ss'),
              duration: this.testForm.duration,
              total_score: this.testForm.total_score,
              passing_score: this.testForm.passing_score,
              is_published: this.testForm.is_published
            }

            if (this.testForm.id) {
              // 更新现有测试
              response = await this.$http.put(`/api/exams/${this.testForm.id}`, testData)
            } else {
              // 创建新测试
              response = await this.$http.post('/api/exams', testData)
            }

            if (response.data.success) {
              this.$message.success(this.testForm.id ? '测试更新成功' : '测试创建成功')
              this.dialogVisible = false
              this.fetchTests()
            } else {
              throw new Error(response.data.message || '保存测试失败')
            }
          } catch (error) {
            console.error('保存测试失败:', error)
            this.$message.error(error.message || '保存测试失败，请稍后再试')
          } finally {
            this.saving = false
          }
        }
      })
    },
    async deleteTest(test) {
      try {
        await this.$confirm(`确定要删除测试 "${test.title}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await this.$http.delete(`/api/exams/${test.id}`)

        if (response.data.success) {
          this.$message.success('测试删除成功')
          this.fetchTests()
        } else {
          throw new Error(response.data.message || '删除测试失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除测试失败:', error)
          this.$message.error(error.message || '删除测试失败，请稍后再试')
        }
      }
    },
    async togglePublishStatus(test) {
      try {
        const newStatus = !test.is_published
        const actionText = newStatus ? '发布' : '取消发布'

        await this.$confirm(`确定要${actionText}测试 "${test.title}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })

        const response = await this.$http.patch(`/api/exams/${test.id}/status`, {
          is_published: newStatus
        })

        if (response.data.success) {
          this.$message.success(`测试${actionText}成功`)
          this.fetchTests()
        } else {
          throw new Error(response.data.message || `${actionText}测试失败`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('更改测试状态失败:', error)
          this.$message.error(error.message || '更改测试状态失败，请稍后再试')
        }
      }
    },
    manageQuestions(test) {
      this.$router.push(`/teacher/tests/${test.id}/questions`)
    },
    viewSubmissions(test) {
      this.$router.push(`/teacher/tests/${test.id}/submissions`)
    },
    formatDateTime(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    getStatusText(test) {
      if (!test.is_published) {
        return '草稿'
      }

      const now = new Date()
      const startTime = new Date(test.start_time)
      const endTime = new Date(test.end_time)

      if (now < startTime) {
        return '未开始'
      } else if (now > endTime) {
        return '已结束'
      } else {
        return '进行中'
      }
    },
    getStatusType(test) {
      if (!test.is_published) {
        return 'info'
      }

      const now = new Date()
      const startTime = new Date(test.start_time)
      const endTime = new Date(test.end_time)

      if (now < startTime) {
        return 'warning'
      } else if (now > endTime) {
        return ''
      } else {
        return 'success'
      }
    }
  },
  created() {
    this.fetchCourses()
  }
}
</script>

<style scoped>
.test-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-select {
  width: 180px;
}

.filter-bar .el-input {
  width: 300px;
}

.loading-container, .empty-data {
  padding: 20px;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.text-muted {
  color: #909399;
}
</style>
