const mysql = require('mysql2/promise');
require('dotenv').config();

// 打印环境变量（不包含密码）
console.log('数据库配置:');
console.log('Host:', process.env.DB_HOST || 'localhost');
console.log('User:', process.env.DB_USER || 'root');
console.log('Database:', process.env.DB_NAME || 'elearning');

// 创建数据库连接池 - 手动指定密码
const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  password: '123456', // 手动指定密码
  database: 'elearning',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 测试数据库连接
const testConnection = async () => {
  try {
    // 先尝试简单查询
    const [rows] = await pool.query('SELECT 1 + 1 AS solution');
    console.log('数据库连接成功！查询结果:', rows[0].solution);
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error.message);

    // 尝试不指定数据库名称连接
    try {
      console.log('尝试不指定数据库名称连接...');
      const rootPool = mysql.createPool({
        host: 'localhost',
        user: 'root',
        password: '123456', // 手动指定密码
        waitForConnections: true,
        connectionLimit: 1,
        queueLimit: 0
      });

      await rootPool.query('SELECT 1');
      console.log('成功连接到MySQL服务器！');

      // 检查数据库是否存在
      const [results] = await rootPool.query(`SHOW DATABASES LIKE '${process.env.DB_NAME || 'elearning'}'`);

      if (results.length === 0) {
        console.log(`数据库 '${process.env.DB_NAME || 'elearning'}' 不存在，需要创建。`);
        console.log('请运行 database/elearning_complete.sql 脚本创建数据库和表。');
      } else {
        console.log(`数据库 '${process.env.DB_NAME || 'elearning'}' 已存在。`);
        console.log('请确保您有正确的访问权限。');
      }

      await rootPool.end();
    } catch (rootError) {
      console.error('无法连接到MySQL服务器:', rootError.message);
    }

    return false;
  }
};

module.exports = {
  pool,
  testConnection
};
