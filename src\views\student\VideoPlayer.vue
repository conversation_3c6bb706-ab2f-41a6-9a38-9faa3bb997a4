<template>
  <div class="video-player">
    <div class="page-header">
      <h1>视频学习</h1>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else class="video-content">
      <!-- 视频播放器区域 -->
      <div class="video-section">
        <div v-if="currentVideo" class="video-player-container">
          <div class="video-info">
            <h2>{{ currentVideo.title }}</h2>
            <p class="video-description" v-if="currentVideo.description">{{ currentVideo.description }}</p>
          </div>

          <!-- 简单的HTML5视频播放器 -->
          <div class="video-wrapper">
            <video
              ref="videoPlayer"
              controls
              preload="metadata"
              width="100%"
              height="500"
              @loadedmetadata="onVideoLoaded"
              @timeupdate="onTimeUpdate"
              @ended="onVideoEnded">
              <source :src="getVideoUrl(currentVideo)" :type="currentVideo.mime_type || 'video/mp4'">
              您的浏览器不支持视频播放。
            </video>
          </div>

          <!-- 视频控制和信息 -->
          <div class="video-controls">
            <div class="video-stats">
              <span><i class="el-icon-time"></i> 时长：{{ formatDuration(videoDuration) }}</span>
              <span><i class="el-icon-view"></i> 已观看：{{ formatDuration(watchedTime) }}</span>
              <span><i class="el-icon-data-line"></i> 进度：{{ watchProgress }}%</span>
            </div>
          </div>
        </div>

        <div v-else class="no-video-selected">
          <el-empty description="请从右侧选择一个视频开始观看"></el-empty>
        </div>
      </div>

      <!-- 视频列表侧边栏 -->
      <div class="video-sidebar">
        <h3>视频列表</h3>

        <div v-if="videoResources.length === 0" class="no-videos">
          <el-empty description="暂无视频资源" :image-size="60"></el-empty>
        </div>

        <div v-else class="video-list">
          <div
            v-for="(video, index) in videoResources"
            :key="video.id"
            class="video-item"
            :class="{ active: currentVideo && currentVideo.id === video.id }"
            @click="selectVideo(video)">
            <div class="video-thumbnail">
              <i class="el-icon-video-play"></i>
              <span class="video-index">{{ index + 1 }}</span>
            </div>
            <div class="video-details">
              <h4>{{ video.title }}</h4>
              <p class="video-meta">
                <span v-if="video.size">{{ formatFileSize(video.size) }}</span>
                <span v-if="video.created_at">{{ formatDate(video.created_at) }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StudentVideoPlayer',
  data() {
    return {
      videoResources: [],
      currentVideo: null,
      loading: false,
      videoDuration: 0,
      watchedTime: 0,
      watchProgress: 0,
      progressUpdateInterval: null
    }
  },
  methods: {
    async fetchVideoResources() {
      this.loading = true
      try {
        // 直接获取所有视频资源，不需要课程过滤
        const response = await this.$http.get('/api/resources', {
          params: {
            type: 'video'
          }
        })

        if (response.data.success) {
          this.videoResources = response.data.data.filter(resource =>
            resource.type === 'video' || (resource.mime_type && resource.mime_type.startsWith('video/'))
          )

          // 如果有视频且没有选中的视频，自动选择第一个
          if (this.videoResources.length > 0 && !this.currentVideo) {
            this.selectVideo(this.videoResources[0])
          }
        } else {
          throw new Error(response.data.message || '获取视频资源失败')
        }
      } catch (error) {
        console.error('获取视频资源失败:', error)
        // 如果API失败，使用本地示例数据
        this.loadSampleVideos()
      } finally {
        this.loading = false
      }
    },
    loadSampleVideos() {
      // 提供一些示例视频数据
      this.videoResources = [
        {
          id: 1,
          title: 'JavaScript基础教程',
          description: '学习JavaScript的基本语法和概念',
          url: '/uploads/videos/javascript_basics.mp4',
          mime_type: 'video/mp4',
          size: 1024000,
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          title: 'HTML和CSS入门',
          description: '网页开发的基础知识',
          url: '/uploads/videos/html_css_intro.mp4',
          mime_type: 'video/mp4',
          size: 2048000,
          created_at: new Date().toISOString()
        },
        {
          id: 3,
          title: 'React框架教程',
          description: '现代前端开发框架React的使用',
          url: '/uploads/videos/react_tutorial.mp4',
          mime_type: 'video/mp4',
          size: 3072000,
          created_at: new Date().toISOString()
        },
        {
          id: 4,
          title: 'Node.js后端开发',
          description: '使用Node.js进行服务器端开发',
          url: '/uploads/videos/nodejs_fundamentals.mp4',
          mime_type: 'video/mp4',
          size: 2560000,
          created_at: new Date().toISOString()
        }
      ]

      // 自动选择第一个视频
      if (this.videoResources.length > 0) {
        this.selectVideo(this.videoResources[0])
      }
    },
    selectVideo(video) {
      this.currentVideo = video
      this.resetVideoState()

      // 等待DOM更新后加载视频
      this.$nextTick(() => {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.load()
        }
      })
    },
    resetVideoState() {
      this.videoDuration = 0
      this.watchedTime = 0
      this.watchProgress = 0

      if (this.progressUpdateInterval) {
        clearInterval(this.progressUpdateInterval)
        this.progressUpdateInterval = null
      }
    },
    onVideoLoaded() {
      if (this.$refs.videoPlayer) {
        this.videoDuration = this.$refs.videoPlayer.duration || 0
        this.startWatchTracking()
      }
    },
    onTimeUpdate() {
      if (this.$refs.videoPlayer && this.videoDuration > 0) {
        const currentTime = this.$refs.videoPlayer.currentTime
        this.watchedTime = currentTime
        this.watchProgress = Math.round((currentTime / this.videoDuration) * 100)
      }
    },
    onVideoEnded() {
      this.$message.success('视频播放完成！')
    },
    startWatchTracking() {
      // 每5秒更新一次观看进度
      if (this.progressUpdateInterval) {
        clearInterval(this.progressUpdateInterval)
      }

      this.progressUpdateInterval = setInterval(() => {
        this.onTimeUpdate()
      }, 5000)
    },
    getVideoUrl(video) {
      if (!video) return ''

      // 尝试获取URL，可能是url或file_path字段
      const videoUrl = video.url || video.file_path
      if (!videoUrl) return ''

      // 如果URL是相对路径，添加服务器地址
      if (videoUrl.startsWith('/')) {
        return `http://localhost:5000${videoUrl}`
      } else if (!videoUrl.startsWith('http')) {
        // 如果不是完整URL且不以/开头，添加/uploads/前缀
        return `http://localhost:5000/uploads/${videoUrl}`
      }

      return videoUrl
    },
    formatDuration(seconds) {
      if (!seconds || isNaN(seconds)) return '00:00'

      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)

      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
    },
    formatFileSize(bytes) {
      if (!bytes) return '未知大小'

      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    }
  },
  created() {
    this.fetchVideoResources()
  },
  beforeDestroy() {
    // 清理定时器
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval)
    }
  }
}
</script>

<style scoped>
.video-player {
  padding: 20px;
  color: white;
}

.page-header h1 {
  color: white;
  margin-bottom: 20px;
}

.course-selector {
  margin-bottom: 20px;
}

.loading-container, .empty-data {
  padding: 40px;
  text-align: center;
}

.video-content {
  display: flex;
  gap: 20px;
  min-height: calc(100vh - 200px);
}

.video-section {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 20px;
}

.video-info h2 {
  color: #333;
  margin-bottom: 10px;
}

.video-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.video-wrapper {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #000;
}

.video-wrapper video {
  width: 100%;
  height: auto;
  display: block;
}

.video-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.video-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.video-stats i {
  margin-right: 5px;
}

.video-sidebar {
  width: 350px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.video-sidebar h3 {
  color: #333;
  margin-bottom: 15px;
}

.video-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.video-item {
  display: flex;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #eee;
}

.video-item:hover {
  background-color: #f0f9ff;
  border-color: #409EFF;
}

.video-item.active {
  background-color: #e6f7ff;
  border-color: #409EFF;
}

.video-thumbnail {
  width: 60px;
  height: 45px;
  background-color: #333;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
  position: relative;
}

.video-thumbnail i {
  font-size: 20px;
}

.video-index {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: rgba(0,0,0,0.7);
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.video-details {
  flex: 1;
}

.video-details h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
}

.video-meta {
  font-size: 12px;
  color: #999;
  margin: 5px 0;
}

.video-status {
  margin-top: 5px;
}

.no-video-selected, .no-videos, .no-comments {
  text-align: center;
  padding: 40px;
}
</style>
