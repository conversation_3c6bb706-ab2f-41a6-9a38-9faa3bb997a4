<template>
  <div class="video-player">
    <div class="page-header">
      <h1>在线视频学习</h1>
    </div>

    <!-- 课程选择 -->
    <div class="course-selector">
      <el-select v-model="selectedCourse" placeholder="选择课程" @change="fetchCourseResources" style="width: 300px;">
        <el-option
          v-for="course in enrolledCourses"
          :key="course.id"
          :label="course.title"
          :value="course.id">
        </el-option>
      </el-select>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="!selectedCourse" class="empty-data">
      <el-empty description="请选择一个课程查看视频资源"></el-empty>
    </div>

    <div v-else class="video-content">
      <!-- 视频播放器区域 -->
      <div class="video-section">
        <div v-if="currentVideo" class="video-player-container">
          <div class="video-info">
            <h2>{{ currentVideo.title }}</h2>
            <p class="video-description">{{ currentVideo.description }}</p>
          </div>
          
          <!-- Video.js 播放器 -->
          <div class="video-wrapper">
            <video
              ref="videoPlayer"
              class="video-js vjs-default-skin"
              controls
              preload="auto"
              width="100%"
              height="500"
              data-setup="{}">
              <p class="vjs-no-js">
                要查看此视频，请启用 JavaScript，并考虑升级到
                <a href="https://videojs.com/html5-video-support/" target="_blank">
                  支持HTML5视频的浏览器
                </a>。
              </p>
            </video>
          </div>

          <!-- 视频控制和信息 -->
          <div class="video-controls">
            <div class="video-stats">
              <span><i class="el-icon-time"></i> 时长：{{ formatDuration(videoDuration) }}</span>
              <span><i class="el-icon-view"></i> 已观看：{{ formatDuration(watchedTime) }}</span>
              <span><i class="el-icon-data-line"></i> 进度：{{ watchProgress }}%</span>
            </div>
            <div class="video-actions">
              <el-button type="primary" @click="markAsCompleted" :disabled="watchProgress < 80">
                <i class="el-icon-check"></i> 标记为已完成
              </el-button>
              <el-button @click="toggleBookmark">
                <i :class="isBookmarked ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
                {{ isBookmarked ? '取消收藏' : '收藏视频' }}
              </el-button>
            </div>
          </div>

          <!-- 视频评论区 -->
          <div class="video-comments">
            <h3>视频评论</h3>
            
            <!-- 发表评论 -->
            <div class="comment-form">
              <el-input
                type="textarea"
                v-model="newComment"
                placeholder="写下你对这个视频的看法..."
                :rows="3">
              </el-input>
              <el-button type="primary" @click="submitComment" :loading="submittingComment" style="margin-top: 10px;">
                发表评论
              </el-button>
            </div>

            <!-- 评论列表 -->
            <div class="comments-list" v-if="videoComments.length > 0">
              <div
                v-for="comment in videoComments"
                :key="comment.id"
                class="comment-item">
                <div class="comment-header">
                  <span class="comment-author">{{ comment.user_name }}</span>
                  <span class="comment-time">{{ formatDate(comment.created_at) }}</span>
                </div>
                <div class="comment-content">{{ comment.content }}</div>
              </div>
            </div>
            
            <div v-else class="no-comments">
              <el-empty description="暂无评论，快来发表第一个评论吧！" :image-size="80"></el-empty>
            </div>
          </div>
        </div>

        <div v-else class="no-video-selected">
          <el-empty description="请从左侧选择一个视频开始学习"></el-empty>
        </div>
      </div>

      <!-- 视频列表侧边栏 -->
      <div class="video-sidebar">
        <h3>课程视频</h3>
        
        <div v-if="videoResources.length === 0" class="no-videos">
          <el-empty description="该课程暂无视频资源" :image-size="60"></el-empty>
        </div>
        
        <div v-else class="video-list">
          <div
            v-for="(video, index) in videoResources"
            :key="video.id"
            class="video-item"
            :class="{ active: currentVideo && currentVideo.id === video.id }"
            @click="selectVideo(video)">
            <div class="video-thumbnail">
              <i class="el-icon-video-play"></i>
              <span class="video-index">{{ index + 1 }}</span>
            </div>
            <div class="video-details">
              <h4>{{ video.title }}</h4>
              <p class="video-meta">
                <span v-if="video.file_size">{{ formatFileSize(video.file_size) }}</span>
                <span v-if="video.created_at">{{ formatDate(video.created_at) }}</span>
              </p>
              <div class="video-status">
                <el-tag v-if="video.is_completed" type="success" size="mini">已完成</el-tag>
                <el-tag v-else-if="video.watch_progress > 0" type="warning" size="mini">
                  进行中 {{ video.watch_progress }}%
                </el-tag>
                <el-tag v-else type="info" size="mini">未开始</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import videojs from 'video.js'
import 'video.js/dist/video-js.css'

export default {
  name: 'StudentVideoPlayer',
  data() {
    return {
      enrolledCourses: [],
      videoResources: [],
      videoComments: [],
      selectedCourse: '',
      currentVideo: null,
      player: null,
      loading: false,
      submittingComment: false,
      newComment: '',
      videoDuration: 0,
      watchedTime: 0,
      watchProgress: 0,
      isBookmarked: false,
      watchStartTime: null,
      progressUpdateInterval: null
    }
  },
  methods: {
    async fetchEnrolledCourses() {
      try {
        const response = await this.$http.get('/api/courses/enrolled')
        if (response.data.success) {
          this.enrolledCourses = response.data.data
          if (this.enrolledCourses.length > 0 && !this.selectedCourse) {
            this.selectedCourse = this.enrolledCourses[0].id
            this.fetchCourseResources()
          }
        }
      } catch (error) {
        console.error('获取已选课程失败:', error)
        this.$message.error('获取课程列表失败')
      }
    },
    async fetchCourseResources() {
      if (!this.selectedCourse) return
      
      this.loading = true
      try {
        const response = await this.$http.get('/api/resources', {
          params: {
            course_id: this.selectedCourse,
            type: 'video'
          }
        })
        
        if (response.data.success) {
          this.videoResources = response.data.data.filter(resource => 
            resource.file_type && resource.file_type.startsWith('video/')
          )
          
          // 如果有视频且没有选中的视频，选择第一个
          if (this.videoResources.length > 0 && !this.currentVideo) {
            this.selectVideo(this.videoResources[0])
          }
        } else {
          throw new Error(response.data.message || '获取视频资源失败')
        }
      } catch (error) {
        console.error('获取视频资源失败:', error)
        this.$message.error('获取视频资源失败')
        this.videoResources = []
      } finally {
        this.loading = false
      }
    },
    selectVideo(video) {
      this.currentVideo = video
      this.loadVideo()
      this.fetchVideoComments()
      this.checkBookmarkStatus()
    },
    loadVideo() {
      if (!this.currentVideo) return
      
      // 销毁之前的播放器
      if (this.player) {
        this.player.dispose()
        this.player = null
      }
      
      this.$nextTick(() => {
        // 初始化 Video.js 播放器
        this.player = videojs(this.$refs.videoPlayer, {
          fluid: true,
          responsive: true,
          playbackRates: [0.5, 1, 1.25, 1.5, 2],
          sources: [{
            src: `http://localhost:5000${this.currentVideo.file_path}`,
            type: this.currentVideo.file_type
          }]
        })
        
        // 监听播放器事件
        this.player.ready(() => {
          this.videoDuration = this.player.duration() || 0
          this.startWatchTracking()
        })
        
        this.player.on('loadedmetadata', () => {
          this.videoDuration = this.player.duration() || 0
        })
        
        this.player.on('timeupdate', () => {
          this.updateWatchProgress()
        })
        
        this.player.on('ended', () => {
          this.markAsCompleted()
        })
      })
    },
    startWatchTracking() {
      this.watchStartTime = Date.now()
      
      // 每5秒更新一次观看进度
      this.progressUpdateInterval = setInterval(() => {
        this.updateWatchProgress()
      }, 5000)
    },
    updateWatchProgress() {
      if (!this.player || !this.videoDuration) return
      
      const currentTime = this.player.currentTime()
      this.watchProgress = Math.round((currentTime / this.videoDuration) * 100)
      this.watchedTime = currentTime
    },
    async markAsCompleted() {
      if (!this.currentVideo) return
      
      try {
        const response = await this.$http.post('/api/resources/complete', {
          resource_id: this.currentVideo.id
        })
        
        if (response.data.success) {
          this.$message.success('视频已标记为完成')
          this.currentVideo.is_completed = true
        }
      } catch (error) {
        console.error('标记完成失败:', error)
        this.$message.error('标记完成失败')
      }
    },
    async toggleBookmark() {
      if (!this.currentVideo) return
      
      try {
        const response = await this.$http.post('/api/resources/bookmark', {
          resource_id: this.currentVideo.id,
          is_bookmarked: !this.isBookmarked
        })
        
        if (response.data.success) {
          this.isBookmarked = !this.isBookmarked
          this.$message.success(this.isBookmarked ? '收藏成功' : '取消收藏成功')
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        this.$message.error('收藏操作失败')
      }
    },
    async checkBookmarkStatus() {
      if (!this.currentVideo) return
      
      try {
        const response = await this.$http.get(`/api/resources/${this.currentVideo.id}/bookmark`)
        if (response.data.success) {
          this.isBookmarked = response.data.is_bookmarked
        }
      } catch (error) {
        console.error('获取收藏状态失败:', error)
      }
    },
    async fetchVideoComments() {
      if (!this.currentVideo) return
      
      try {
        const response = await this.$http.get('/api/comments', {
          params: {
            type: 'resource',
            target_id: this.currentVideo.id
          }
        })
        
        if (response.data.success) {
          this.videoComments = response.data.data
        }
      } catch (error) {
        console.error('获取视频评论失败:', error)
      }
    },
    async submitComment() {
      if (!this.newComment.trim()) {
        this.$message.warning('请输入评论内容')
        return
      }
      
      if (!this.currentVideo) {
        this.$message.warning('请先选择视频')
        return
      }
      
      this.submittingComment = true
      try {
        const response = await this.$http.post('/api/comments', {
          content: this.newComment,
          type: 'resource',
          target_id: this.currentVideo.id
        })
        
        if (response.data.success) {
          this.$message.success('评论发表成功')
          this.newComment = ''
          this.fetchVideoComments()
        } else {
          throw new Error(response.data.message || '发表评论失败')
        }
      } catch (error) {
        console.error('发表评论失败:', error)
        this.$message.error(error.message || '发表评论失败')
      } finally {
        this.submittingComment = false
      }
    },
    formatDuration(seconds) {
      if (!seconds || isNaN(seconds)) return '00:00'
      
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
    },
    formatFileSize(bytes) {
      if (!bytes) return '未知大小'
      
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    }
  },
  created() {
    this.fetchEnrolledCourses()
  },
  beforeDestroy() {
    // 清理播放器和定时器
    if (this.player) {
      this.player.dispose()
    }
    
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval)
    }
  }
}
</script>

<style scoped>
.video-player {
  padding: 20px;
  color: white;
}

.page-header h1 {
  color: white;
  margin-bottom: 20px;
}

.course-selector {
  margin-bottom: 20px;
}

.loading-container, .empty-data {
  padding: 40px;
  text-align: center;
}

.video-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.video-section {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
}

.video-info h2 {
  color: #333;
  margin-bottom: 10px;
}

.video-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.video-wrapper {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.video-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.video-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.video-stats i {
  margin-right: 5px;
}

.video-comments {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.video-comments h3 {
  color: #333;
  margin-bottom: 15px;
}

.comment-form {
  margin-bottom: 20px;
}

.comments-list {
  max-height: 300px;
  overflow-y: auto;
}

.comment-item {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.comment-author {
  font-weight: bold;
  color: #409EFF;
}

.comment-time {
  color: #999;
}

.comment-content {
  color: #333;
  line-height: 1.5;
}

.video-sidebar {
  width: 350px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
}

.video-sidebar h3 {
  color: #333;
  margin-bottom: 15px;
}

.video-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.video-item {
  display: flex;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #eee;
}

.video-item:hover {
  background-color: #f0f9ff;
  border-color: #409EFF;
}

.video-item.active {
  background-color: #e6f7ff;
  border-color: #409EFF;
}

.video-thumbnail {
  width: 60px;
  height: 45px;
  background-color: #333;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
  position: relative;
}

.video-thumbnail i {
  font-size: 20px;
}

.video-index {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: rgba(0,0,0,0.7);
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.video-details {
  flex: 1;
}

.video-details h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
}

.video-meta {
  font-size: 12px;
  color: #999;
  margin: 5px 0;
}

.video-status {
  margin-top: 5px;
}

.no-video-selected, .no-videos, .no-comments {
  text-align: center;
  padding: 40px;
}
</style>
