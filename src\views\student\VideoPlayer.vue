<template>
  <div class="video-player">
    <div class="page-header">
      <h1>视频学习</h1>
    </div>

    <!-- 课程选择 -->
    <div class="course-selector">
      <el-select v-model="selectedCourse" placeholder="选择课程" @change="fetchCourseResources" style="width: 300px;">
        <el-option
          v-for="course in enrolledCourses"
          :key="course.id"
          :label="course.title"
          :value="course.id">
        </el-option>
      </el-select>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="!selectedCourse" class="empty-data">
      <el-empty description="请选择一个课程查看视频资源"></el-empty>
    </div>

    <div v-else class="video-content">
      <!-- 视频播放器区域 -->
      <div class="video-section">
        <div v-if="currentVideo" class="video-player-container">
          <div class="video-info">
            <h2>{{ currentVideo.title }}</h2>
            <p class="video-description" v-if="currentVideo.description">{{ currentVideo.description }}</p>
          </div>

          <!-- 简单的HTML5视频播放器 -->
          <div class="video-wrapper">
            <video
              ref="videoPlayer"
              controls
              preload="metadata"
              width="100%"
              height="500"
              @loadedmetadata="onVideoLoaded"
              @timeupdate="onTimeUpdate"
              @ended="onVideoEnded">
              <source :src="getVideoUrl(currentVideo)" :type="currentVideo.mime_type || 'video/mp4'">
              您的浏览器不支持视频播放。
            </video>
          </div>

          <!-- 视频控制和信息 -->
          <div class="video-controls">
            <div class="video-stats">
              <span><i class="el-icon-time"></i> 时长：{{ formatDuration(videoDuration) }}</span>
              <span><i class="el-icon-view"></i> 已观看：{{ formatDuration(watchedTime) }}</span>
              <span><i class="el-icon-data-line"></i> 进度：{{ watchProgress }}%</span>
            </div>
            <div class="video-actions">
              <el-button type="primary" @click="markAsCompleted" :disabled="watchProgress < 80">
                <i class="el-icon-check"></i> 标记为已完成
              </el-button>
              <el-button @click="toggleBookmark">
                <i :class="isBookmarked ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
                {{ isBookmarked ? '取消收藏' : '收藏视频' }}
              </el-button>
            </div>
          </div>
        </div>

        <div v-else class="no-video-selected">
          <el-empty description="请从右侧选择一个视频开始学习"></el-empty>
        </div>
      </div>

      <!-- 视频列表侧边栏 -->
      <div class="video-sidebar">
        <h3>课程视频列表</h3>

        <div v-if="videoResources.length === 0" class="no-videos">
          <el-empty description="该课程暂无视频资源" :image-size="60"></el-empty>
        </div>

        <div v-else class="video-list">
          <div
            v-for="(video, index) in videoResources"
            :key="video.id"
            class="video-item"
            :class="{ active: currentVideo && currentVideo.id === video.id }"
            @click="selectVideo(video)">
            <div class="video-thumbnail">
              <i class="el-icon-video-play"></i>
              <span class="video-index">{{ index + 1 }}</span>
            </div>
            <div class="video-details">
              <h4>{{ video.title }}</h4>
              <p class="video-meta">
                <span v-if="video.size">{{ formatFileSize(video.size) }}</span>
                <span v-if="video.created_at">{{ formatDate(video.created_at) }}</span>
              </p>
              <div class="video-status">
                <el-tag v-if="video.is_completed" type="success" size="mini">已完成</el-tag>
                <el-tag v-else-if="video.watch_progress > 0" type="warning" size="mini">
                  进行中 {{ video.watch_progress }}%
                </el-tag>
                <el-tag v-else type="info" size="mini">未开始</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StudentVideoPlayer',
  data() {
    return {
      enrolledCourses: [],
      videoResources: [],
      selectedCourse: '',
      currentVideo: null,
      loading: false,
      videoDuration: 0,
      watchedTime: 0,
      watchProgress: 0,
      isBookmarked: false,
      progressUpdateInterval: null
    }
  },
  methods: {
    async fetchEnrolledCourses() {
      try {
        const response = await this.$http.get('/api/courses/enrolled')
        if (response.data.success) {
          this.enrolledCourses = response.data.data
          if (this.enrolledCourses.length > 0 && !this.selectedCourse) {
            this.selectedCourse = this.enrolledCourses[0].id
            this.fetchCourseResources()
          }
        }
      } catch (error) {
        console.error('获取已选课程失败:', error)
        this.$message.error('获取课程列表失败')
      }
    },
    async fetchCourseResources() {
      if (!this.selectedCourse) return

      this.loading = true
      try {
        const response = await this.$http.get('/api/resources', {
          params: {
            course_id: this.selectedCourse,
            type: 'video'
          }
        })

        if (response.data.success) {
          this.videoResources = response.data.data.filter(resource =>
            resource.type === 'video' || (resource.mime_type && resource.mime_type.startsWith('video/'))
          )

          // 清除当前视频选择
          this.currentVideo = null
          this.resetVideoState()
        } else {
          throw new Error(response.data.message || '获取视频资源失败')
        }
      } catch (error) {
        console.error('获取视频资源失败:', error)
        this.$message.error('获取视频资源失败')
        this.videoResources = []
      } finally {
        this.loading = false
      }
    },
    selectVideo(video) {
      this.currentVideo = video
      this.resetVideoState()
      this.checkBookmarkStatus()

      // 等待DOM更新后加载视频
      this.$nextTick(() => {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.load()
        }
      })
    },
    resetVideoState() {
      this.videoDuration = 0
      this.watchedTime = 0
      this.watchProgress = 0

      if (this.progressUpdateInterval) {
        clearInterval(this.progressUpdateInterval)
        this.progressUpdateInterval = null
      }
    },
    onVideoLoaded() {
      if (this.$refs.videoPlayer) {
        this.videoDuration = this.$refs.videoPlayer.duration || 0
        this.startWatchTracking()
      }
    },
    onTimeUpdate() {
      if (this.$refs.videoPlayer && this.videoDuration > 0) {
        const currentTime = this.$refs.videoPlayer.currentTime
        this.watchedTime = currentTime
        this.watchProgress = Math.round((currentTime / this.videoDuration) * 100)
      }
    },
    onVideoEnded() {
      this.markAsCompleted()
    },
    startWatchTracking() {
      // 每5秒更新一次观看进度
      if (this.progressUpdateInterval) {
        clearInterval(this.progressUpdateInterval)
      }

      this.progressUpdateInterval = setInterval(() => {
        this.onTimeUpdate()
      }, 5000)
    },
    getVideoUrl(video) {
      if (!video) return ''

      // 尝试获取URL，可能是url或file_path字段
      const videoUrl = video.url || video.file_path
      if (!videoUrl) return ''

      // 如果URL是相对路径，添加服务器地址
      if (videoUrl.startsWith('/')) {
        return `http://localhost:5000${videoUrl}`
      } else if (!videoUrl.startsWith('http')) {
        // 如果不是完整URL且不以/开头，添加/uploads/前缀
        return `http://localhost:5000/uploads/${videoUrl}`
      }

      return videoUrl
    },
    async markAsCompleted() {
      if (!this.currentVideo) return

      try {
        const response = await this.$http.post('/api/resources/complete', {
          resource_id: this.currentVideo.id
        })

        if (response.data.success) {
          this.$message.success('视频已标记为完成')
          this.currentVideo.is_completed = true
        }
      } catch (error) {
        console.error('标记完成失败:', error)
        this.$message.error('标记完成失败')
      }
    },
    async toggleBookmark() {
      if (!this.currentVideo) return

      try {
        const response = await this.$http.post('/api/resources/bookmark', {
          resource_id: this.currentVideo.id,
          is_bookmarked: !this.isBookmarked
        })

        if (response.data.success) {
          this.isBookmarked = !this.isBookmarked
          this.$message.success(this.isBookmarked ? '收藏成功' : '取消收藏成功')
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        this.$message.error('收藏操作失败')
      }
    },
    async checkBookmarkStatus() {
      if (!this.currentVideo) return

      try {
        const response = await this.$http.get(`/api/resources/${this.currentVideo.id}/bookmark`)
        if (response.data.success) {
          this.isBookmarked = response.data.is_bookmarked
        }
      } catch (error) {
        console.error('获取收藏状态失败:', error)
      }
    },
    formatDuration(seconds) {
      if (!seconds || isNaN(seconds)) return '00:00'

      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)

      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
    },
    formatFileSize(bytes) {
      if (!bytes) return '未知大小'

      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    }
  },
  created() {
    this.fetchEnrolledCourses()
  },
  beforeDestroy() {
    // 清理定时器
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval)
    }
  }
}
</script>

<style scoped>
.video-player {
  padding: 20px;
  color: white;
}

.page-header h1 {
  color: white;
  margin-bottom: 20px;
}

.course-selector {
  margin-bottom: 20px;
}

.loading-container, .empty-data {
  padding: 40px;
  text-align: center;
}

.video-content {
  display: flex;
  gap: 20px;
  min-height: calc(100vh - 200px);
}

.video-section {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 20px;
}

.video-info h2 {
  color: #333;
  margin-bottom: 10px;
}

.video-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.video-wrapper {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #000;
}

.video-wrapper video {
  width: 100%;
  height: auto;
  display: block;
}

.video-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.video-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.video-stats i {
  margin-right: 5px;
}

.video-sidebar {
  width: 350px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.video-sidebar h3 {
  color: #333;
  margin-bottom: 15px;
}

.video-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.video-item {
  display: flex;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #eee;
}

.video-item:hover {
  background-color: #f0f9ff;
  border-color: #409EFF;
}

.video-item.active {
  background-color: #e6f7ff;
  border-color: #409EFF;
}

.video-thumbnail {
  width: 60px;
  height: 45px;
  background-color: #333;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
  position: relative;
}

.video-thumbnail i {
  font-size: 20px;
}

.video-index {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: rgba(0,0,0,0.7);
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.video-details {
  flex: 1;
}

.video-details h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
}

.video-meta {
  font-size: 12px;
  color: #999;
  margin: 5px 0;
}

.video-status {
  margin-top: 5px;
}

.no-video-selected, .no-videos, .no-comments {
  text-align: center;
  padding: 40px;
}
</style>
