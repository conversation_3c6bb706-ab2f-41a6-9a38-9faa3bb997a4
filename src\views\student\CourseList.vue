<template>
  <div class="course-list">
    <div class="page-header">
      <h1>我的课程</h1>
    </div>

    <div class="filter-bar">
      <el-input
        placeholder="搜索课程"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchCourses">
      </el-input>
      <el-select v-model="categoryFilter" placeholder="课程类别" clearable @change="fetchCourses">
        <el-option label="计算机科学" value="计算机科学"></el-option>
        <el-option label="软件工程" value="软件工程"></el-option>
        <el-option label="数据科学" value="数据科学"></el-option>
        <el-option label="人工智能" value="人工智能"></el-option>
        <el-option label="网络安全" value="网络安全"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchCourses">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="courses.length === 0" class="empty-data">
      <el-empty description="暂无课程数据"></el-empty>
    </div>

    <div v-else class="course-grid">
      <el-card v-for="course in courses" :key="course.id" class="course-card" shadow="hover">
        <div class="course-cover" :style="{ backgroundImage: `url(${course.cover_image || '/img/default-course.jpg'})` }">
          <div class="course-category">{{ course.category }}</div>
        </div>
        <div class="course-info">
          <h3 class="course-title">{{ course.title }}</h3>
          <p class="course-description">{{ course.description }}</p>
          <div class="course-meta">
            <span><i class="el-icon-user"></i> {{ course.teacher_name }}</span>
            <span><i class="el-icon-date"></i> {{ formatDate(course.start_date) }}</span>
          </div>
          <div class="course-actions">
            <el-button type="primary" @click="navigateToCourse(course.id)">进入课程</el-button>
            <el-button v-if="isEnrolled(course.id)" type="danger" @click="unenrollCourse(course.id)">退选课程</el-button>
            <el-button v-else type="success" @click="enrollCourse(course.id)">选修课程</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="courses.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CourseList',
  data() {
    return {
      courses: [],
      enrolledCourses: [],
      loading: false,
      searchQuery: '',
      categoryFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  methods: {
    async fetchCourses() {
      this.loading = true
      try {
        // 从API获取数据
        const response = await this.$http.get('/api/courses', {
          params: {
            search: this.searchQuery,
            category: this.categoryFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.courses = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取课程失败')
        }
      } catch (error) {
        console.error('获取课程失败:', error)
        this.$message.error('获取课程失败，请稍后再试')

        // 使用模拟数据（仅在开发环境或API失败时）
        this.courses = [
          {
            id: 1,
            title: 'Web开发基础',
            description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
            category: '计算机科学',
            teacher_name: '张老师',
            start_date: '2023-09-01',
            end_date: '2024-01-15'
          },
          {
            id: 2,
            title: '数据库系统',
            description: '关系型数据库设计、SQL查询和数据库管理系统。',
            category: '计算机科学',
            teacher_name: '李老师',
            start_date: '2023-09-01',
            end_date: '2024-01-15'
          }
        ]
        this.total = 2
      } finally {
        this.loading = false
      }
    },
    async fetchEnrolledCourses() {
      try {
        // 获取已选课程列表
        const promises = this.courses.map(course =>
          this.$http.get(`/api/courses/${course.id}/enrollment-status`)
            .then(response => {
              if (response.data.success && response.data.isEnrolled) {
                return course.id
              }
              return null
            })
            .catch(error => {
              console.error(`获取课程 ${course.id} 选课状态失败:`, error)
              return null
            })
        )

        const results = await Promise.all(promises)
        this.enrolledCourses = results.filter(id => id !== null)
      } catch (error) {
        console.error('获取已选课程失败:', error)
        // 使用模拟数据（仅在开发环境或API失败时）
        this.enrolledCourses = [2, 3] // 假设已选修了ID为2和3的课程
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchCourses()
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD')
    },
    navigateToCourse(courseId) {
      this.$router.push(`/student/courses/${courseId}`)
    },
    isEnrolled(courseId) {
      return this.enrolledCourses.includes(courseId)
    },
    async enrollCourse(courseId) {
      try {
        // 调用API选课
        const response = await this.$http.post(`/api/courses/${courseId}/enroll`)

        if (response.data.success) {
          this.$message.success('选课成功')
          if (!this.enrolledCourses.includes(courseId)) {
            this.enrolledCourses.push(courseId)
          }
        } else {
          throw new Error(response.data.message || '选课失败')
        }
      } catch (error) {
        console.error('选课失败:', error)

        // 处理常见错误
        if (error.response && error.response.status === 400) {
          if (error.response.data && error.response.data.message) {
            this.$message.warning(error.response.data.message)

            // 如果错误是因为已经选修了课程，则更新选课状态
            if (error.response.data.message.includes('已经选修')) {
              if (!this.enrolledCourses.includes(courseId)) {
                this.enrolledCourses.push(courseId)
              }
            }
          } else {
            this.$message.warning('您可能已经选修了该课程')
          }
        } else {
          this.$message.error(error.message || '选课失败，请稍后再试')
        }
      }
    },
    async unenrollCourse(courseId) {
      try {
        // 确认操作
        await this.$confirm('确定要退选此课程吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用API退课
        const response = await this.$http.delete(`/api/courses/${courseId}/enroll`)

        if (response.data.success) {
          this.$message.success('退选成功')
          const index = this.enrolledCourses.indexOf(courseId)
          if (index !== -1) {
            this.enrolledCourses.splice(index, 1)
          }
        } else {
          throw new Error(response.data.message || '退选失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退选失败:', error)

          // 处理常见错误
          if (error.response && error.response.status === 400) {
            if (error.response.data && error.response.data.message) {
              this.$message.warning(error.response.data.message)

              // 如果错误是因为尚未选修课程，则更新选课状态
              if (error.response.data.message.includes('尚未选修')) {
                const index = this.enrolledCourses.indexOf(courseId)
                if (index !== -1) {
                  this.enrolledCourses.splice(index, 1)
                }
              }
            } else {
              this.$message.warning('您可能尚未选修该课程')
            }
          } else {
            this.$message.error(error.message || '退选失败，请稍后再试')
          }
        }
      }
    }
  },
  async created() {
    await this.fetchCourses()
    this.fetchEnrolledCourses()
  }
}
</script>

<style scoped>
.course-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.course-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.course-cover {
  height: 160px;
  background-size: cover;
  background-position: center;
  position: relative;
  border-radius: 4px 4px 0 0;
  background-color: #f5f7fa;
}

.course-category {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.course-info {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.course-title {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course-description {
  color: #606266;
  margin-bottom: 15px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 14px;
  margin-bottom: 15px;
}

.course-actions {
  display: flex;
  justify-content: space-between;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
  }

  .filter-bar .el-input {
    width: 100%;
  }

  .course-grid {
    grid-template-columns: 1fr;
  }
}
</style>
