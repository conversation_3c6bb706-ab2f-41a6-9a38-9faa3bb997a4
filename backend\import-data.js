const fs = require('fs');
const mysql = require('mysql2/promise');
const path = require('path');

async function importData() {
  try {
    // 读取SQL文件
    const testDataSql = fs.readFileSync(path.join(__dirname, 'import-test-data.sql'), 'utf8');
    const resourceDataSql = fs.readFileSync(path.join(__dirname, 'import-resource-data.sql'), 'utf8');

    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'elearning',
      multipleStatements: true // 允许执行多条SQL语句
    });

    console.log('数据库连接成功');

    // 执行测试数据导入
    console.log('开始导入测试数据...');
    await connection.query(testDataSql);
    console.log('测试数据导入成功');

    // 执行资源数据导入
    console.log('开始导入资源数据...');
    await connection.query(resourceDataSql);
    console.log('资源数据导入成功');

    // 关闭连接
    await connection.end();
    console.log('数据导入完成');
  } catch (error) {
    console.error('导入数据失败:', error);
  }
}

importData();
