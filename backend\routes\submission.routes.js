const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect } = require('../middleware/auth');

// 授权中间件 - 检查用户是否为教师或管理员
const authorizeTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'teacher' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: '权限不足，需要教师权限'
    });
  }
};

// 获取测试提交列表
router.get('/tests/:id/submissions', protect, authorizeTeacher, async (req, res) => {
  try {
    const testId = req.params.id;
    const { page = 1, limit = 10 } = req.query;
    
    // 检查测试是否存在
    const [testRows] = await pool.query(
      'SELECT * FROM tests WHERE id = ?',
      [testId]
    );
    
    if (testRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }
    
    // 检查权限
    if (req.user.role === 'teacher' && testRows[0].creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权查看提交'
      });
    }
    
    // 获取提交列表
    const offset = (page - 1) * limit;
    const [submissions] = await pool.query(
      `SELECT s.*, u.username as student_name
       FROM test_submissions s
       JOIN users u ON s.student_id = u.id
       WHERE s.test_id = ?
       ORDER BY s.submit_time DESC
       LIMIT ? OFFSET ?`,
      [testId, parseInt(limit), offset]
    );
    
    // 获取总数
    const [countResult] = await pool.query(
      'SELECT COUNT(*) as total FROM test_submissions WHERE test_id = ?',
      [testId]
    );
    const total = countResult[0].total;
    
    res.status(200).json({
      success: true,
      count: submissions.length,
      total,
      data: submissions
    });
  } catch (error) {
    console.error('获取测试提交列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取测试提交列表失败',
      error: error.message
    });
  }
});

// 获取单个提交详情
router.get('/submissions/:id', protect, async (req, res) => {
  try {
    const submissionId = req.params.id;
    
    // 获取提交信息
    const [submissions] = await pool.query(
      `SELECT s.*, u.username as student_name, t.title as test_title, t.creator_id
       FROM test_submissions s
       JOIN users u ON s.student_id = u.id
       JOIN tests t ON s.test_id = t.id
       WHERE s.id = ?`,
      [submissionId]
    );
    
    if (submissions.length === 0) {
      return res.status(404).json({
        success: false,
        message: '提交不存在'
      });
    }
    
    const submission = submissions[0];
    
    // 检查权限
    if (
      req.user.role === 'student' && submission.student_id !== req.user.id ||
      req.user.role === 'teacher' && submission.creator_id !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: '您无权查看此提交'
      });
    }
    
    // 获取答案
    const [answers] = await pool.query(
      `SELECT a.*, q.content as question_content, q.type as question_type
       FROM test_answers a
       JOIN test_questions q ON a.question_id = q.id
       WHERE a.submission_id = ?
       ORDER BY q.sort_order`,
      [submissionId]
    );
    
    submission.answers = answers;
    
    res.status(200).json({
      success: true,
      data: submission
    });
  } catch (error) {
    console.error('获取提交详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取提交详情失败',
      error: error.message
    });
  }
});

// 学生提交测试
router.post('/tests/:id/submit', protect, async (req, res) => {
  try {
    const testId = req.params.id;
    const studentId = req.user.id;
    const { answers } = req.body;
    
    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: '只有学生可以提交测试'
      });
    }
    
    // 检查测试是否存在
    const [testRows] = await pool.query(
      'SELECT * FROM tests WHERE id = ?',
      [testId]
    );
    
    if (testRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }
    
    const test = testRows[0];
    
    // 检查测试是否已发布
    if (test.status !== 'published') {
      return res.status(400).json({
        success: false,
        message: '该测试尚未发布，无法提交'
      });
    }
    
    // 检查测试时间
    const now = new Date();
    const startTime = new Date(test.start_time);
    const endTime = new Date(test.end_time);
    
    if (now < startTime) {
      return res.status(400).json({
        success: false,
        message: '测试尚未开始'
      });
    }
    
    if (now > endTime) {
      return res.status(400).json({
        success: false,
        message: '测试已结束'
      });
    }
    
    // 检查是否已提交
    const [submissionRows] = await pool.query(
      'SELECT * FROM test_submissions WHERE test_id = ? AND student_id = ?',
      [testId, studentId]
    );
    
    if (submissionRows.length > 0 && submissionRows[0].status !== 'in_progress') {
      return res.status(400).json({
        success: false,
        message: '您已提交过该测试'
      });
    }
    
    // 开始事务
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    
    try {
      let submissionId;
      
      // 如果已有进行中的提交，则更新
      if (submissionRows.length > 0) {
        submissionId = submissionRows[0].id;
        
        await connection.query(
          `UPDATE test_submissions 
           SET submit_time = NOW(), status = 'submitted', updated_at = NOW()
           WHERE id = ?`,
          [submissionId]
        );
      } else {
        // 创建新提交
        const [result] = await connection.query(
          `INSERT INTO test_submissions (test_id, student_id, start_time, submit_time, status)
           VALUES (?, ?, NOW(), NOW(), 'submitted')`,
          [testId, studentId]
        );
        
        submissionId = result.insertId;
      }
      
      // 获取测试问题
      const [questions] = await connection.query(
        'SELECT * FROM test_questions WHERE test_id = ?',
        [testId]
      );
      
      // 自动评分选择题
      let totalScore = 0;
      let isFullyGraded = true;
      
      for (const answer of answers) {
        const { questionId, answer: studentAnswer } = answer;
        
        // 查找对应的问题
        const question = questions.find(q => q.id === questionId);
        
        if (!question) {
          continue;
        }
        
        let score = 0;
        let isGraded = false;
        
        // 自动评分选择题
        if (['single', 'multiple', 'truefalse'].includes(question.type)) {
          const correctAnswer = JSON.parse(question.answer);
          
          if (question.type === 'single' || question.type === 'truefalse') {
            // 单选题和判断题
            if (studentAnswer === correctAnswer.correct) {
              score = question.score;
            }
            isGraded = true;
          } else if (question.type === 'multiple') {
            // 多选题
            const correctOptions = correctAnswer.correct;
            const studentOptions = studentAnswer;
            
            if (Array.isArray(correctOptions) && Array.isArray(studentOptions)) {
              // 完全匹配才得分
              if (
                correctOptions.length === studentOptions.length &&
                correctOptions.every(opt => studentOptions.includes(opt))
              ) {
                score = question.score;
              }
            }
            isGraded = true;
          }
        } else {
          // 简答题需要手动评分
          isFullyGraded = false;
          isGraded = false;
        }
        
        totalScore += score;
        
        // 保存答案
        await connection.query(
          `INSERT INTO test_answers (submission_id, question_id, answer, score, is_graded)
           VALUES (?, ?, ?, ?, ?)`,
          [submissionId, questionId, JSON.stringify(studentAnswer), score, isGraded ? 1 : 0]
        );
      }
      
      // 更新提交总分和评分状态
      await connection.query(
        `UPDATE test_submissions 
         SET total_score = ?, is_fully_graded = ?, status = ?
         WHERE id = ?`,
        [
          totalScore, 
          isFullyGraded ? 1 : 0, 
          isFullyGraded ? 'graded' : 'submitted',
          submissionId
        ]
      );
      
      // 提交事务
      await connection.commit();
      
      res.status(200).json({
        success: true,
        message: '测试提交成功',
        data: {
          id: submissionId,
          totalScore,
          isFullyGraded
        }
      });
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('提交测试失败:', error);
    res.status(500).json({
      success: false,
      message: '提交测试失败',
      error: error.message
    });
  }
});

// 教师评分测试提交
router.put('/submissions/:id/grade', protect, authorizeTeacher, async (req, res) => {
  try {
    const submissionId = req.params.id;
    const { answers, feedback } = req.body;
    
    // 检查提交是否存在
    const [submissionRows] = await pool.query(
      `SELECT s.*, t.creator_id
       FROM test_submissions s
       JOIN tests t ON s.test_id = t.id
       WHERE s.id = ?`,
      [submissionId]
    );
    
    if (submissionRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '提交不存在'
      });
    }
    
    const submission = submissionRows[0];
    
    // 检查权限
    if (req.user.role === 'teacher' && submission.creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权评分'
      });
    }
    
    // 开始事务
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    
    try {
      let totalScore = 0;
      
      // 更新每个答案的评分
      for (const answer of answers) {
        const { id, score, comment } = answer;
        
        await connection.query(
          `UPDATE test_answers 
           SET score = ?, comment = ?, is_graded = 1, updated_at = NOW()
           WHERE id = ? AND submission_id = ?`,
          [score, comment, id, submissionId]
        );
        
        totalScore += score;
      }
      
      // 检查是否所有答案都已评分
      const [answersResult] = await connection.query(
        'SELECT COUNT(*) as total, SUM(is_graded) as graded FROM test_answers WHERE submission_id = ?',
        [submissionId]
      );
      
      const isFullyGraded = answersResult[0].total === answersResult[0].graded;
      
      // 更新提交总分和评分状态
      await connection.query(
        `UPDATE test_submissions 
         SET total_score = ?, is_fully_graded = ?, status = ?, feedback = ?, updated_at = NOW()
         WHERE id = ?`,
        [
          totalScore, 
          isFullyGraded ? 1 : 0, 
          isFullyGraded ? 'graded' : 'submitted',
          feedback,
          submissionId
        ]
      );
      
      // 提交事务
      await connection.commit();
      
      res.status(200).json({
        success: true,
        message: '评分成功',
        data: {
          id: submissionId,
          totalScore,
          isFullyGraded
        }
      });
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('评分失败:', error);
    res.status(500).json({
      success: false,
      message: '评分失败',
      error: error.message
    });
  }
});

module.exports = router;
