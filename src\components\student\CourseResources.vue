<template>
  <div class="course-resources">
    <div class="filter-bar">
      <el-input
        placeholder="搜索资源"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchResources">
      </el-input>
      <el-select v-model="typeFilter" placeholder="资源类型" clearable @change="fetchResources">
        <el-option label="文档" value="document"></el-option>
        <el-option label="视频" value="video"></el-option>
        <el-option label="链接" value="link"></el-option>
        <el-option label="其他" value="other"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchResources">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="resources.length === 0" class="empty-data">
      <el-empty description="暂无课程资源"></el-empty>
    </div>

    <div v-else>
      <!-- 资源列表 -->
      <el-table :data="resources" style="width: 100%">
        <el-table-column prop="title" label="资源名称" min-width="200"></el-table-column>
        <el-table-column prop="description" label="描述" min-width="300">
          <template slot-scope="scope">
            <span v-if="scope.row.description">{{ scope.row.description }}</span>
            <span v-else class="no-data">无描述</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getResourceTypeType(scope.row.type)">
              {{ getResourceTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.size">{{ formatFileSize(scope.row.size) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="上传时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="previewResource(scope.row)"
              v-if="canPreview(scope.row)">
              预览
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="downloadResource(scope.row)">
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange">
        </el-pagination>
      </div>
    </div>

    <!-- 资源预览对话框 -->
    <el-dialog :title="previewResource.title" :visible.sync="previewVisible" width="70%">
      <div v-if="previewLoading" class="preview-loading">
        <el-skeleton :rows="10" animated />
      </div>
      <div v-else>
        <!-- 文档预览 -->
        <div v-if="previewResource.type === 'document'" class="document-preview">
          <iframe :src="previewResource.url" width="100%" height="500"></iframe>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="previewResource.type === 'video'" class="video-preview">
          <video controls width="100%" :src="previewResource.url"></video>
        </div>

        <!-- 链接预览 -->
        <div v-else-if="previewResource.type === 'link'" class="link-preview">
          <p>外部链接: <a :href="previewResource.url" target="_blank">{{ previewResource.url }}</a></p>
        </div>

        <!-- 其他类型 -->
        <div v-else class="other-preview">
          <p>无法预览此类型的资源，请下载后查看。</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadResource(previewResource)">下载</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CourseResources',
  props: {
    courseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      resources: [],
      loading: false,
      searchQuery: '',
      typeFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      previewVisible: false,
      previewLoading: false,
      previewResource: {}
    }
  },
  methods: {
    async fetchResources() {
      this.loading = true
      try {
        // 从API获取数据
        const response = await this.$http.get(`/api/resources/courses/${this.courseId}/resources`, {
          params: {
            search: this.searchQuery,
            type: this.typeFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.resources = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取资源失败')
        }
      } catch (error) {
        console.error('获取资源失败:', error)
        this.$message.error('获取资源失败，请稍后再试')
        this.resources = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchResources()
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + ' MB'
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
      }
    },
    getResourceTypeType(type) {
      const types = {
        'document': 'primary',
        'video': 'success',
        'link': 'info',
        'other': 'warning'
      }
      return types[type] || 'info'
    },
    getResourceTypeText(type) {
      const texts = {
        'document': '文档',
        'video': '视频',
        'link': '链接',
        'other': '其他'
      }
      return texts[type] || type
    },
    canPreview(resource) {
      return ['document', 'video', 'link'].includes(resource.type)
    },
    async previewResource(resource) {
      this.previewResource = resource
      this.previewVisible = true
      this.previewLoading = true

      // 模拟加载预览内容
      setTimeout(() => {
        this.previewLoading = false
      }, 1000)
    },
    downloadResource(resource) {
      try {
        this.$message.success(`开始下载资源: ${resource.title}`)

        // 如果是链接类型，直接在新窗口打开
        if (resource.type === 'link') {
          window.open(resource.url, '_blank')
        } else {
          // 其他类型通过API下载
          window.open(`${this.$http.defaults.baseURL}/api/resources/${resource.id}/download`, '_blank')
        }
      } catch (error) {
        console.error('下载资源失败:', error)
        this.$message.error('下载资源失败，请稍后再试')
      }
    }
  },
  created() {
    this.fetchResources()
  }
}
</script>

<style scoped>
.course-resources {
  padding: 10px 0;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.no-data {
  color: #909399;
  font-style: italic;
}

.preview-loading {
  padding: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
  }

  .filter-bar .el-input {
    width: 100%;
  }
}
</style>
