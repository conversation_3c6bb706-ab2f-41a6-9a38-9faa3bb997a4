const mongoose = require('mongoose');

const CourseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, '请提供课程标题'],
    trim: true,
    maxlength: [100, '课程标题不能超过100个字符']
  },
  description: {
    type: String,
    required: [true, '请提供课程描述']
  },
  coverImage: {
    type: String,
    default: 'default-course.jpg'
  },
  teacher: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定课程教师']
  },
  students: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  // 课程状态：draft(草稿), published(已发布), archived(已归档)
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  // 课程类别
  category: {
    type: String,
    required: [true, '请提供课程类别']
  },
  // 课程标签
  tags: [String],
  // 开始和结束日期
  startDate: {
    type: Date
  },
  endDate: {
    type: Date
  },
  // 课程代码（用于学生加入课程）
  courseCode: {
    type: String,
    unique: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虚拟字段：资源数量
CourseSchema.virtual('resourceCount', {
  ref: 'Resource',
  localField: '_id',
  foreignField: 'course',
  count: true
});

// 虚拟字段：测试数量
CourseSchema.virtual('testCount', {
  ref: 'Test',
  localField: '_id',
  foreignField: 'course',
  count: true
});

// 虚拟字段：学生数量
CourseSchema.virtual('studentCount', {
  get() {
    return this.students.length;
  }
});

// 生成唯一的课程代码
CourseSchema.pre('save', async function(next) {
  if (!this.courseCode) {
    // 生成6位随机字母数字组合
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 6; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    this.courseCode = code;
  }
  next();
});

module.exports = mongoose.model('Course', CourseSchema);
