-- 为E-learning平台添加公告和讨论数据
USE elearning;

-- 检查表是否存在
SET @announcements_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'elearning' AND table_name = 'announcements');
SET @discussions_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'elearning' AND table_name = 'discussions');
SET @discussion_replies_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'elearning' AND table_name = 'discussion_replies');

-- 如果表不存在，创建它们
SET @sql = IF(@announcements_exists = 0,
'CREATE TABLE announcements (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    type ENUM("system", "course") DEFAULT "system",
    course_id BIGINT UNSIGNED,
    author_id BIGINT UNSIGNED NOT NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_important BOOLEAN DEFAULT FALSE,
    publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_type (type),
    INDEX idx_publish_date (publish_date)
) ENGINE=InnoDB;', 'SELECT "announcements table already exists"');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@discussions_exists = 0,
'CREATE TABLE discussions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    course_id BIGINT UNSIGNED NOT NULL,
    author_id BIGINT UNSIGNED NOT NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_answered BOOLEAN DEFAULT FALSE,
    view_count INT UNSIGNED DEFAULT 0,
    reply_count INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_author (author_id),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB;', 'SELECT "discussions table already exists"');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@discussion_replies_exists = 0,
'CREATE TABLE discussion_replies (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    discussion_id BIGINT UNSIGNED NOT NULL,
    author_id BIGINT UNSIGNED NOT NULL,
    content TEXT NOT NULL,
    is_answer BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (discussion_id) REFERENCES discussions(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_discussion (discussion_id),
    INDEX idx_author (author_id)
) ENGINE=InnoDB;', 'SELECT "discussion_replies table already exists"');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE discussion_replies;
-- TRUNCATE TABLE discussions;
-- TRUNCATE TABLE announcements;

-- 插入系统公告数据
INSERT INTO announcements (title, content, type, course_id, author_id, is_pinned, is_important, publish_date) VALUES
('欢迎使用在线学习平台', '欢迎所有师生使用我们的在线学习平台！在这里，您可以参与课程学习、完成测试、参与讨论等。如有任何问题，请联系管理员。',
 'system', NULL,
 1, -- 假设admin用户ID为1
 TRUE, TRUE,
 '2025-05-01 09:00:00'),

('系统维护通知', '系统将于2025年5月15日凌晨2:00-4:00进行例行维护，届时系统将暂停服务。请提前做好准备，不要在维护时间段内使用系统。',
 'system', NULL,
 1, -- 假设admin用户ID为1
 TRUE, TRUE,
 '2025-05-05 10:30:00'),

('关于期末考试安排', '各位同学请注意，期末考试将于2025年6月初开始，请做好复习准备。具体考试时间表将在下周公布，请密切关注系统通知。',
 'system', NULL,
 1, -- 假设admin用户ID为1
 FALSE, FALSE,
 '2025-05-10 14:00:00'),

('新功能上线通知', '我们很高兴地通知大家，平台新增了视频会议功能，支持在线实时教学。请在"工具"菜单中查看使用方法。',
 'system', NULL,
 1, -- 假设admin用户ID为1
 FALSE, FALSE,
 '2025-05-12 11:00:00'),

('教师培训通知', '我们将于2025年5月20日举办教师在线培训，主题为"如何有效利用平台进行混合式教学"，欢迎所有教师参加。',
 'system', NULL,
 1, -- 假设admin用户ID为1
 FALSE, TRUE,
 '2025-05-15 09:30:00');

-- 插入课程公告数据
INSERT INTO announcements (title, content, type, course_id, author_id, is_pinned, is_important, publish_date) VALUES
('Web开发课程项目展示', 'Web开发课程的期末项目展示将在2025年5月25日进行，请各小组做好准备。展示顺序将在当天随机决定。',
 'course',
 2, -- Web开发基础课程ID
 2, -- 假设teacher1用户ID为2
 TRUE, FALSE,
 '2025-05-08 15:00:00'),

('数据库课程作业提交', '请各位同学在2025年5月20日前提交数据库设计作业。作业要求：1. ER图 2. 关系模式 3. SQL建表语句 4. 至少5条测试数据。',
 'course',
 3, -- 数据库系统课程ID
 2, -- 假设teacher1用户ID为2
 FALSE, TRUE,
 '2025-05-10 16:30:00'),

('人工智能课程讲座', '我们邀请了行业专家将于2025年5月18日进行人工智能前沿技术讲座，地点：线上会议室，时间：下午2:00-4:00。',
 'course',
 1, -- 人工智能导论课程ID
 3, -- 假设teacher2用户ID为3
 TRUE, TRUE,
 '2025-05-12 10:00:00'),

('移动应用开发资源更新', '课程资源已更新，新增了Flutter框架的学习材料和示例代码，请大家查看并尝试完成示例项目。',
 'course',
 5, -- 移动应用开发课程ID
 3, -- 假设teacher2用户ID为3
 FALSE, FALSE,
 '2025-05-14 11:30:00'),

('高级Web开发课程调整', '由于技术更新，我们将在课程中增加Vue 3和React Hooks的内容，相应地减少jQuery的内容。请大家提前了解这两个框架。',
 'course',
 4, -- 高级Web开发课程ID
 2, -- 假设teacher1用户ID为2
 TRUE, FALSE,
 '2025-05-16 09:00:00');

-- 插入讨论数据
INSERT INTO discussions (title, content, course_id, author_id, is_pinned, is_answered, view_count, reply_count, created_at, last_activity) VALUES
('关于Web开发课程的讨论', '大家好，我想了解一下Web开发课程中JavaScript部分的学习方法，有什么好的资源推荐吗？',
 2, -- Web开发基础课程ID
 4, -- 假设student1用户ID为4
 FALSE, TRUE, 25, 3,
 '2025-05-05 14:30:00', '2025-05-06 10:15:00'),

('数据库设计原则讨论', '我在设计数据库时遇到了一些问题，特别是关于第三范式的应用，有人能解释一下吗？',
 3, -- 数据库系统课程ID
 5, -- 假设student2用户ID为5
 FALSE, FALSE, 18, 2,
 '2025-05-08 09:45:00', '2025-05-08 16:20:00'),

('人工智能实验讨论', '在做神经网络实验时，我的模型准确率一直很低，有没有同学遇到类似问题？',
 1, -- 人工智能导论课程ID
 6, -- 假设student3用户ID为6
 FALSE, TRUE, 32, 4,
 '2025-05-10 11:00:00', '2025-05-11 14:30:00'),

('React Hooks使用问题', '我在使用React Hooks时遇到了一些问题，特别是useEffect的依赖数组，谁能帮我解释一下？',
 4, -- 高级Web开发课程ID
 7, -- 假设student4用户ID为7
 FALSE, TRUE, 15, 2,
 '2025-05-12 16:30:00', '2025-05-13 09:45:00'),

('期末项目提交讨论', '老师，我的项目已经提交，但是遇到了一些技术难题，能否安排时间讨论一下？',
 5, -- 移动应用开发课程ID
 8, -- 假设student5用户ID为8
 FALSE, FALSE, 8, 1,
 '2025-05-15 10:20:00', '2025-05-15 14:10:00');

-- 插入讨论回复数据
INSERT INTO discussion_replies (discussion_id, author_id, content, is_answer, created_at) VALUES
-- 回复"关于Web开发课程的讨论"
(1, -- 假设第一个讨论ID为1
 2, -- 假设teacher1用户ID为2
 '我推荐MDN Web文档和JavaScript.info，这两个资源对JavaScript的讲解非常详细和易懂。',
 TRUE, '2025-05-05 15:45:00'),

(1, -- 假设第一个讨论ID为1
 6, -- 假设student3用户ID为6
 '我发现FreeCodeCamp的JavaScript课程也很不错，有很多实践练习。',
 FALSE, '2025-05-05 16:30:00'),

(1, -- 假设第一个讨论ID为1
 4, -- 假设student1用户ID为4
 '谢谢老师和同学的推荐，我会去看看这些资源。',
 FALSE, '2025-05-06 10:15:00'),

-- 回复"数据库设计原则讨论"
(2, -- 假设第二个讨论ID为2
 2, -- 假设teacher1用户ID为2
 '第三范式要求消除传递依赖，即非主属性不依赖于其他非主属性。你可以通过分解表来实现这一点。',
 FALSE, '2025-05-08 10:30:00'),

(2, -- 假设第二个讨论ID为2
 5, -- 假设student2用户ID为5
 '我明白了，但在实际应用中，有时为了性能考虑，可能会适当违反第三范式，对吗？',
 FALSE, '2025-05-08 16:20:00'),

-- 回复"人工智能实验讨论"
(3, -- 假设第三个讨论ID为3
 8, -- 假设student5用户ID为8
 '我也遇到过这个问题，后来发现是数据预处理不够充分，特别是数据标准化。',
 FALSE, '2025-05-10 13:15:00'),

(3, -- 假设第三个讨论ID为3
 3, -- 假设teacher2用户ID为3
 '除了数据预处理，还要注意模型复杂度和学习率的设置，这些都会影响模型的准确率。',
 TRUE, '2025-05-10 15:45:00'),

(3, -- 假设第三个讨论ID为3
 6, -- 假设student3用户ID为6
 '谢谢老师和同学的建议，我会调整这些参数再试试。',
 FALSE, '2025-05-11 09:30:00'),

(3, -- 假设第三个讨论ID为3
 4, -- 假设student1用户ID为4
 '我建议你也可以尝试不同的激活函数，有时这也会有显著影响。',
 FALSE, '2025-05-11 14:30:00'),

-- 回复"React Hooks使用问题"
(4, -- 假设第四个讨论ID为4
 2, -- 假设teacher1用户ID为2
 'useEffect的依赖数组决定了effect何时重新运行。空数组表示只在组件挂载和卸载时运行，包含变量的数组表示这些变量变化时重新运行。',
 TRUE, '2025-05-12 17:45:00'),

(4, -- 假设第四个讨论ID为4
 7, -- 假设student4用户ID为7
 '谢谢老师的解释，我现在明白了。还有一个问题，如果依赖是一个对象，每次渲染都会创建新对象，这会导致effect一直重新运行，应该怎么处理？',
 FALSE, '2025-05-13 09:45:00'),

-- 回复"期末项目提交讨论"
(5, -- 假设第五个讨论ID为5
 3, -- 假设teacher2用户ID为3
 '可以，我们可以安排在下周三下午3点进行线上讨论，你把具体问题提前发给我，我会做些准备。',
 FALSE, '2025-05-15 14:10:00');

-- 显示结果
SELECT 'Announcements and discussions data imported successfully!' AS message;
