const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 上传目录路径
const uploadPath = process.env.UPLOAD_PATH || path.join(__dirname, '..', 'uploads');

// 需要创建的子目录
const subDirs = [
  'images',
  'videos',
  'documents/pdf',
  'documents/other'
];

// 创建目录函数
function createDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`创建目录: ${dir}`);
  } else {
    console.log(`目录已存在: ${dir}`);
  }
}

// 创建主上传目录
createDir(uploadPath);

// 创建子目录
subDirs.forEach(subDir => {
  const fullPath = path.join(uploadPath, subDir);
  createDir(fullPath);
});

console.log('上传目录初始化完成！');

// 导出函数以便在其他地方使用
module.exports = {
  initUploadDirs: () => {
    createDir(uploadPath);
    subDirs.forEach(subDir => {
      const fullPath = path.join(uploadPath, subDir);
      createDir(fullPath);
    });
  }
};
