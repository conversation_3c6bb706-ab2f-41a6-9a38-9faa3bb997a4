const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect } = require('../middleware/auth');
// const upload = require('../middleware/upload'); // 我们将在下面重新定义
const path = require('path');
const fs = require('fs');

// 授权中间件 - 检查用户是否为教师或管理员
const authorizeTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'teacher' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: '权限不足，需要教师权限'
    });
  }
};

// 配置文件上传
const multer = require('multer');

// 确保上传目录存在
const ensureUploadDirs = () => {
  const dirs = [
    path.join(__dirname, '../uploads'),
    path.join(__dirname, '../uploads/images'),
    path.join(__dirname, '../uploads/videos'),
    path.join(__dirname, '../uploads/documents'),
    path.join(__dirname, '../uploads/other')
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
};

// 配置存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    ensureUploadDirs();

    let uploadPath = '';
    if (file.mimetype.startsWith('image/')) {
      uploadPath = path.join(__dirname, '../uploads/images');
    } else if (file.mimetype.startsWith('video/')) {
      uploadPath = path.join(__dirname, '../uploads/videos');
    } else if (file.mimetype === 'application/pdf' || file.mimetype.includes('document')) {
      uploadPath = path.join(__dirname, '../uploads/documents');
    } else {
      uploadPath = path.join(__dirname, '../uploads/other');
    }

    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/webm', 'video/avi',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

// 配置上传限制
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  }
});

// 获取课程资源列表 (新的路由格式)
router.get('/course/:courseId', protect, async (req, res) => {
  try {
    const courseId = req.params.courseId;
    const { search, type, page = 1, limit = 10 } = req.query;

    // 验证课程存在
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    // 检查用户是否有权限访问该课程
    const course = courses[0];
    const isTeacher = req.user.role === 'teacher' && course.teacher_id === req.user.id;
    const isAdmin = req.user.role === 'admin';
    const isStudent = req.user.role === 'student';

    // 如果是学生，检查课程是否已发布且学生是否已选修
    if (isStudent) {
      if (course.status !== 'published') {
        return res.status(403).json({
          success: false,
          message: '该课程尚未发布'
        });
      }

      // 检查学生是否已选修该课程
      const [enrollments] = await pool.query(
        'SELECT * FROM enrollments WHERE course_id = ? AND student_id = ?',
        [courseId, req.user.id]
      );

      if (enrollments.length === 0) {
        return res.status(403).json({
          success: false,
          message: '您尚未选修该课程'
        });
      }
    }

    // 构建查询条件
    let query = `
      SELECT r.*, u.username as teacher_name,
             r.type as file_type, r.url as file_path, r.size as file_size
      FROM resources r
      JOIN users u ON r.uploader_id = u.id
      WHERE r.course_id = ?
    `;
    const queryParams = [courseId];

    // 如果是学生，只显示公开资源
    if (isStudent) {
      query += ' AND r.is_public = TRUE';
    }

    // 添加搜索条件
    if (search) {
      query += ' AND (r.title LIKE ? OR r.description LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加类型过滤
    if (type) {
      query += ' AND r.type = ?';
      queryParams.push(type);
    }

    // 添加排序和分页
    const offset = (page - 1) * limit;
    query += ' ORDER BY r.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [resources] = await pool.query(query, queryParams);

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM resources r
      WHERE r.course_id = ?
    `;
    const countParams = [courseId];

    // 如果是学生，只计算公开资源
    if (isStudent) {
      countQuery += ' AND r.is_public = TRUE';
    }

    // 添加搜索条件
    if (search) {
      countQuery += ' AND (r.title LIKE ? OR r.description LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加类型过滤
    if (type) {
      countQuery += ' AND r.type = ?';
      countParams.push(type);
    }

    const [countResult] = await pool.query(countQuery, countParams);
    const total = countResult[0].total;

    res.status(200).json({
      success: true,
      count: resources.length,
      total,
      data: resources
    });
  } catch (error) {
    console.error('获取课程资源失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程资源失败',
      error: error.message
    });
  }
});

// 获取课程资源列表 (旧的路由格式，保持兼容性)
router.get('/courses/:id/resources', protect, async (req, res) => {
  try {
    const courseId = req.params.id;
    const { search, type, page = 1, limit = 10 } = req.query;

    // 构建查询条件
    let query = 'SELECT r.*, u.username as uploader_name FROM resources r JOIN users u ON r.uploader_id = u.id WHERE r.course_id = ?';
    const queryParams = [courseId];

    // 添加搜索条件
    if (search) {
      query += ' AND (r.title LIKE ? OR r.description LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加类型过滤
    if (type) {
      query += ' AND r.type = ?';
      queryParams.push(type);
    }

    // 添加排序和分页
    const offset = (page - 1) * limit;
    query += ' ORDER BY r.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [resources] = await pool.query(query, queryParams);

    // 获取总数
    const [countResult] = await pool.query(
      'SELECT COUNT(*) as total FROM resources WHERE course_id = ?',
      [courseId]
    );
    const total = countResult[0].total;

    res.status(200).json({
      success: true,
      count: resources.length,
      total,
      data: resources
    });
  } catch (error) {
    console.error('获取课程资源失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程资源失败',
      error: error.message
    });
  }
});

// 上传新资源
router.post('/courses/:id/resources', protect, authorizeTeacher, upload.single('file'), async (req, res) => {
  try {
    const courseId = req.params.id;
    const { title, description, type, url } = req.body;

    // 检查课程是否存在
    const [courseRows] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    // 检查当前用户是否为课程教师
    if (req.user.role === 'teacher' && courseRows[0].teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该课程的教师，无权上传资源'
      });
    }

    let resourceUrl = '';
    let fileSize = null;
    let mimeType = null;

    // 处理文件上传或外部链接
    if (type === 'link') {
      // 外部链接资源
      resourceUrl = url;
    } else if (req.file) {
      // 上传的文件资源
      resourceUrl = `/uploads/${path.relative(process.env.UPLOAD_PATH || 'uploads', req.file.path).replace(/\\/g, '/')}`;
      fileSize = req.file.size;
      mimeType = req.file.mimetype;
    } else {
      return res.status(400).json({
        success: false,
        message: '请提供文件或链接'
      });
    }

    // 插入资源记录
    const [result] = await pool.query(
      `INSERT INTO resources (title, description, type, url, size, mime_type, course_id, uploader_id, is_public)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [title, description, type, resourceUrl, fileSize, mimeType, courseId, req.user.id, true]
    );

    // 获取新创建的资源
    const [newResource] = await pool.query(
      'SELECT r.*, u.username as uploader_name FROM resources r JOIN users u ON r.uploader_id = u.id WHERE r.id = ?',
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '资源上传成功',
      data: newResource[0]
    });
  } catch (error) {
    console.error('上传资源失败:', error);
    res.status(500).json({
      success: false,
      message: '上传资源失败',
      error: error.message
    });
  }
});



// 上传资源文件
router.post('/upload', protect, authorizeTeacher, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    // 构建相对路径
    const relativePath = req.file.path.replace(/\\/g, '/').split('backend/')[1];

    res.status(200).json({
      success: true,
      message: '文件上传成功',
      data: {
        originalname: req.file.originalname,
        filename: req.file.filename,
        path: relativePath,
        mimetype: req.file.mimetype,
        size: req.file.size
      }
    });
  } catch (error) {
    console.error('文件上传失败:', error);
    res.status(500).json({
      success: false,
      message: '文件上传失败',
      error: error.message
    });
  }
});

// 创建新资源
router.post('/', protect, authorizeTeacher, async (req, res) => {
  try {
    const {
      title,
      description,
      file_path,
      file_type,
      file_size,
      course_id,
      is_public = true
    } = req.body;

    // 验证课程存在且教师有权限
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [course_id]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    const course = courses[0];

    // 检查教师是否有权限操作该课程
    if (req.user.role === 'teacher' && course.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该课程的教师，无权添加资源'
      });
    }

    // 插入资源记录
    const [result] = await pool.query(
      `INSERT INTO resources (
        title, description, url, type, size,
        course_id, uploader_id, is_public
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        title,
        description,
        file_path,
        file_type,
        file_size,
        course_id,
        req.user.id,
        is_public
      ]
    );

    // 获取新创建的资源
    const [newResource] = await pool.query(
      `SELECT r.*, u.username as teacher_name,
              r.type as file_type, r.url as file_path, r.size as file_size
       FROM resources r
       JOIN users u ON r.uploader_id = u.id
       WHERE r.id = ?`,
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '资源创建成功',
      data: newResource[0]
    });
  } catch (error) {
    console.error('创建资源失败:', error);
    res.status(500).json({
      success: false,
      message: '创建资源失败',
      error: error.message
    });
  }
});

// 获取单个资源详情
router.get('/:id', protect, async (req, res) => {
  try {
    const resourceId = req.params.id;

    const [resources] = await pool.query(
      `SELECT r.*, u.username as teacher_name,
              r.type as file_type, r.url as file_path, r.size as file_size
       FROM resources r
       JOIN users u ON r.uploader_id = u.id
       WHERE r.id = ?`,
      [resourceId]
    );

    if (resources.length === 0) {
      return res.status(404).json({
        success: false,
        message: '资源不存在'
      });
    }

    res.status(200).json({
      success: true,
      data: resources[0]
    });
  } catch (error) {
    console.error('获取资源详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取资源详情失败',
      error: error.message
    });
  }
});

// 更新资源信息
router.put('/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const resourceId = req.params.id;
    const { title, description, type, url, is_public } = req.body;

    // 获取资源信息
    const [resources] = await pool.query(
      'SELECT * FROM resources WHERE id = ?',
      [resourceId]
    );

    if (resources.length === 0) {
      return res.status(404).json({
        success: false,
        message: '资源不存在'
      });
    }

    const resource = resources[0];

    // 检查权限
    if (req.user.role === 'teacher' && resource.uploader_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该资源的上传者，无权修改'
      });
    }

    // 更新资源
    await pool.query(
      `UPDATE resources
       SET title = ?, description = ?, type = ?, url = ?, is_public = ?, updated_at = NOW()
       WHERE id = ?`,
      [title, description, type, url, is_public, resourceId]
    );

    // 获取更新后的资源
    const [updatedResource] = await pool.query(
      `SELECT r.*, u.username as uploader_name
       FROM resources r
       JOIN users u ON r.uploader_id = u.id
       WHERE r.id = ?`,
      [resourceId]
    );

    res.status(200).json({
      success: true,
      message: '资源更新成功',
      data: updatedResource[0]
    });
  } catch (error) {
    console.error('更新资源失败:', error);
    res.status(500).json({
      success: false,
      message: '更新资源失败',
      error: error.message
    });
  }
});

// 删除资源
router.delete('/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const resourceId = req.params.id;

    // 获取资源信息
    const [resources] = await pool.query(
      'SELECT * FROM resources WHERE id = ?',
      [resourceId]
    );

    if (resources.length === 0) {
      return res.status(404).json({
        success: false,
        message: '资源不存在'
      });
    }

    const resource = resources[0];

    // 检查权限
    if (req.user.role === 'teacher' && resource.uploader_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该资源的上传者，无权删除'
      });
    }

    // 如果是文件类型资源，删除文件
    if (resource.type !== 'link' && resource.url.startsWith('/uploads/')) {
      const filePath = path.join(process.env.UPLOAD_PATH || 'uploads', resource.url.replace('/uploads/', ''));
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    // 删除资源记录
    await pool.query('DELETE FROM resources WHERE id = ?', [resourceId]);

    res.status(200).json({
      success: true,
      message: '资源删除成功'
    });
  } catch (error) {
    console.error('删除资源失败:', error);
    res.status(500).json({
      success: false,
      message: '删除资源失败',
      error: error.message
    });
  }
});

// 下载资源
router.get('/:id/download', protect, async (req, res) => {
  try {
    const resourceId = req.params.id;

    // 获取资源信息
    const [resources] = await pool.query(
      'SELECT * FROM resources WHERE id = ?',
      [resourceId]
    );

    if (resources.length === 0) {
      return res.status(404).json({
        success: false,
        message: '资源不存在'
      });
    }

    const resource = resources[0];

    // 如果是链接类型，重定向到链接
    if (resource.type === 'link') {
      return res.redirect(resource.url);
    }

    // 构建文件路径
    const filePath = path.join(process.env.UPLOAD_PATH || 'uploads', resource.url.replace('/uploads/', ''));

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 更新下载计数
    await pool.query(
      'UPDATE resources SET download_count = download_count + 1 WHERE id = ?',
      [resourceId]
    );

    // 发送文件
    res.download(filePath, path.basename(filePath));
  } catch (error) {
    console.error('下载资源失败:', error);
    res.status(500).json({
      success: false,
      message: '下载资源失败',
      error: error.message
    });
  }
});

module.exports = router;
