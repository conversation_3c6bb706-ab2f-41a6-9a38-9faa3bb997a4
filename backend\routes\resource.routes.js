const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect } = require('../middleware/auth');
const upload = require('../middleware/upload');
const path = require('path');
const fs = require('fs');

// 授权中间件 - 检查用户是否为教师或管理员
const authorizeTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'teacher' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: '权限不足，需要教师权限'
    });
  }
};

// 获取课程资源列表
router.get('/courses/:id/resources', protect, async (req, res) => {
  try {
    const courseId = req.params.id;
    const { search, type, page = 1, limit = 10 } = req.query;
    
    // 构建查询条件
    let query = 'SELECT r.*, u.username as uploader_name FROM resources r JOIN users u ON r.uploader_id = u.id WHERE r.course_id = ?';
    const queryParams = [courseId];
    
    // 添加搜索条件
    if (search) {
      query += ' AND (r.title LIKE ? OR r.description LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }
    
    // 添加类型过滤
    if (type) {
      query += ' AND r.type = ?';
      queryParams.push(type);
    }
    
    // 添加排序和分页
    const offset = (page - 1) * limit;
    query += ' ORDER BY r.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);
    
    // 执行查询
    const [resources] = await pool.query(query, queryParams);
    
    // 获取总数
    const [countResult] = await pool.query(
      'SELECT COUNT(*) as total FROM resources WHERE course_id = ?',
      [courseId]
    );
    const total = countResult[0].total;
    
    res.status(200).json({
      success: true,
      count: resources.length,
      total,
      data: resources
    });
  } catch (error) {
    console.error('获取课程资源失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程资源失败',
      error: error.message
    });
  }
});

// 上传新资源
router.post('/courses/:id/resources', protect, authorizeTeacher, upload.single('file'), async (req, res) => {
  try {
    const courseId = req.params.id;
    const { title, description, type, url } = req.body;
    
    // 检查课程是否存在
    const [courseRows] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );
    
    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }
    
    // 检查当前用户是否为课程教师
    if (req.user.role === 'teacher' && courseRows[0].teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该课程的教师，无权上传资源'
      });
    }
    
    let resourceUrl = '';
    let fileSize = null;
    let mimeType = null;
    
    // 处理文件上传或外部链接
    if (type === 'link') {
      // 外部链接资源
      resourceUrl = url;
    } else if (req.file) {
      // 上传的文件资源
      resourceUrl = `/uploads/${path.relative(process.env.UPLOAD_PATH || 'uploads', req.file.path).replace(/\\/g, '/')}`;
      fileSize = req.file.size;
      mimeType = req.file.mimetype;
    } else {
      return res.status(400).json({
        success: false,
        message: '请提供文件或链接'
      });
    }
    
    // 插入资源记录
    const [result] = await pool.query(
      `INSERT INTO resources (title, description, type, url, size, mime_type, course_id, uploader_id, is_public)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [title, description, type, resourceUrl, fileSize, mimeType, courseId, req.user.id, true]
    );
    
    // 获取新创建的资源
    const [newResource] = await pool.query(
      'SELECT r.*, u.username as uploader_name FROM resources r JOIN users u ON r.uploader_id = u.id WHERE r.id = ?',
      [result.insertId]
    );
    
    res.status(201).json({
      success: true,
      message: '资源上传成功',
      data: newResource[0]
    });
  } catch (error) {
    console.error('上传资源失败:', error);
    res.status(500).json({
      success: false,
      message: '上传资源失败',
      error: error.message
    });
  }
});

// 获取单个资源详情
router.get('/:id', protect, async (req, res) => {
  try {
    const resourceId = req.params.id;
    
    const [resources] = await pool.query(
      `SELECT r.*, u.username as uploader_name 
       FROM resources r 
       JOIN users u ON r.uploader_id = u.id 
       WHERE r.id = ?`,
      [resourceId]
    );
    
    if (resources.length === 0) {
      return res.status(404).json({
        success: false,
        message: '资源不存在'
      });
    }
    
    res.status(200).json({
      success: true,
      data: resources[0]
    });
  } catch (error) {
    console.error('获取资源详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取资源详情失败',
      error: error.message
    });
  }
});

// 更新资源信息
router.put('/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const resourceId = req.params.id;
    const { title, description, type, url, is_public } = req.body;
    
    // 获取资源信息
    const [resources] = await pool.query(
      'SELECT * FROM resources WHERE id = ?',
      [resourceId]
    );
    
    if (resources.length === 0) {
      return res.status(404).json({
        success: false,
        message: '资源不存在'
      });
    }
    
    const resource = resources[0];
    
    // 检查权限
    if (req.user.role === 'teacher' && resource.uploader_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该资源的上传者，无权修改'
      });
    }
    
    // 更新资源
    await pool.query(
      `UPDATE resources 
       SET title = ?, description = ?, type = ?, url = ?, is_public = ?, updated_at = NOW()
       WHERE id = ?`,
      [title, description, type, url, is_public, resourceId]
    );
    
    // 获取更新后的资源
    const [updatedResource] = await pool.query(
      `SELECT r.*, u.username as uploader_name 
       FROM resources r 
       JOIN users u ON r.uploader_id = u.id 
       WHERE r.id = ?`,
      [resourceId]
    );
    
    res.status(200).json({
      success: true,
      message: '资源更新成功',
      data: updatedResource[0]
    });
  } catch (error) {
    console.error('更新资源失败:', error);
    res.status(500).json({
      success: false,
      message: '更新资源失败',
      error: error.message
    });
  }
});

// 删除资源
router.delete('/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const resourceId = req.params.id;
    
    // 获取资源信息
    const [resources] = await pool.query(
      'SELECT * FROM resources WHERE id = ?',
      [resourceId]
    );
    
    if (resources.length === 0) {
      return res.status(404).json({
        success: false,
        message: '资源不存在'
      });
    }
    
    const resource = resources[0];
    
    // 检查权限
    if (req.user.role === 'teacher' && resource.uploader_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该资源的上传者，无权删除'
      });
    }
    
    // 如果是文件类型资源，删除文件
    if (resource.type !== 'link' && resource.url.startsWith('/uploads/')) {
      const filePath = path.join(process.env.UPLOAD_PATH || 'uploads', resource.url.replace('/uploads/', ''));
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }
    
    // 删除资源记录
    await pool.query('DELETE FROM resources WHERE id = ?', [resourceId]);
    
    res.status(200).json({
      success: true,
      message: '资源删除成功'
    });
  } catch (error) {
    console.error('删除资源失败:', error);
    res.status(500).json({
      success: false,
      message: '删除资源失败',
      error: error.message
    });
  }
});

// 下载资源
router.get('/:id/download', protect, async (req, res) => {
  try {
    const resourceId = req.params.id;
    
    // 获取资源信息
    const [resources] = await pool.query(
      'SELECT * FROM resources WHERE id = ?',
      [resourceId]
    );
    
    if (resources.length === 0) {
      return res.status(404).json({
        success: false,
        message: '资源不存在'
      });
    }
    
    const resource = resources[0];
    
    // 如果是链接类型，重定向到链接
    if (resource.type === 'link') {
      return res.redirect(resource.url);
    }
    
    // 构建文件路径
    const filePath = path.join(process.env.UPLOAD_PATH || 'uploads', resource.url.replace('/uploads/', ''));
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }
    
    // 更新下载计数
    await pool.query(
      'UPDATE resources SET download_count = download_count + 1 WHERE id = ?',
      [resourceId]
    );
    
    // 发送文件
    res.download(filePath, path.basename(filePath));
  } catch (error) {
    console.error('下载资源失败:', error);
    res.status(500).json({
      success: false,
      message: '下载资源失败',
      error: error.message
    });
  }
});

module.exports = router;
