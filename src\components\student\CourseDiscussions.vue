<template>
  <div class="course-discussions">
    <div class="discussions-header">
      <div class="filter-bar">
        <el-input
          placeholder="搜索讨论"
          v-model="searchQuery"
          prefix-icon="el-icon-search"
          clearable
          @clear="fetchDiscussions">
        </el-input>
        <el-button type="primary" @click="fetchDiscussions">搜索</el-button>
      </div>
      <el-button type="success" @click="showNewDiscussionDialog">发起讨论</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="discussions.length === 0" class="empty-data">
      <el-empty description="暂无讨论">
        <el-button type="primary" @click="showNewDiscussionDialog">发起第一个讨论</el-button>
      </el-empty>
    </div>

    <div v-else class="discussions-list">
      <el-card v-for="discussion in discussions" :key="discussion.id" class="discussion-card" shadow="hover">
        <div class="discussion-header">
          <div class="discussion-author">
            <el-avatar :size="40" :src="discussion.author_avatar"></el-avatar>
            <div class="author-info">
              <div class="author-name">{{ discussion.author_name }}</div>
              <div class="post-time">{{ formatDate(discussion.created_at) }}</div>
            </div>
          </div>
          <div class="discussion-stats">
            <span class="stat-item">
              <i class="el-icon-view"></i> {{ discussion.view_count }}
            </span>
            <span class="stat-item">
              <i class="el-icon-chat-line-square"></i> {{ discussion.reply_count }}
            </span>
          </div>
        </div>
        <div class="discussion-content">
          <h3 class="discussion-title" @click="viewDiscussion(discussion)">{{ discussion.title }}</h3>
          <p class="discussion-summary">{{ discussion.content }}</p>
        </div>
        <div class="discussion-footer">
          <el-tag v-if="discussion.is_pinned" type="warning">置顶</el-tag>
          <el-tag v-if="discussion.is_answered" type="success">已解决</el-tag>
          <el-button type="text" @click="viewDiscussion(discussion)">查看详情</el-button>
        </div>
      </el-card>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange">
        </el-pagination>
      </div>
    </div>

    <!-- 讨论详情对话框 -->
    <el-dialog :title="currentDiscussion.title" :visible.sync="discussionVisible" width="70%">
      <div v-if="discussionLoading" class="discussion-loading">
        <el-skeleton :rows="10" animated />
      </div>
      <div v-else class="discussion-detail">
        <!-- 原始帖子 -->
        <div class="post-item original-post">
          <div class="post-header">
            <div class="post-author">
              <el-avatar :size="40" :src="currentDiscussion.author_avatar"></el-avatar>
              <div class="author-info">
                <div class="author-name">{{ currentDiscussion.author_name }}</div>
                <div class="post-time">{{ formatDate(currentDiscussion.created_at) }}</div>
              </div>
            </div>
            <div class="post-actions">
              <el-button
                v-if="isCurrentUserAuthor(currentDiscussion)"
                type="text"
                size="mini"
                @click="editDiscussion(currentDiscussion)">
                编辑
              </el-button>
            </div>
          </div>
          <div class="post-content" v-html="currentDiscussion.content"></div>
        </div>

        <!-- 回复列表 -->
        <div class="replies-section">
          <div class="replies-header">
            <h3>回复 ({{ replies.length }})</h3>
          </div>

          <div v-if="replies.length === 0" class="empty-replies">
            <p>暂无回复，成为第一个回复的人吧！</p>
          </div>

          <div v-else class="replies-list">
            <div v-for="reply in replies" :key="reply.id" class="post-item reply-post">
              <div class="post-header">
                <div class="post-author">
                  <el-avatar :size="40" :src="reply.author_avatar"></el-avatar>
                  <div class="author-info">
                    <div class="author-name">{{ reply.author_name }}</div>
                    <div class="post-time">{{ formatDate(reply.created_at) }}</div>
                  </div>
                </div>
                <div class="post-actions">
                  <el-button
                    v-if="isCurrentUserAuthor(reply)"
                    type="text"
                    size="mini"
                    @click="editReply(reply)">
                    编辑
                  </el-button>
                  <el-button
                    v-if="isCurrentUserAuthor(currentDiscussion) && !currentDiscussion.is_answered"
                    type="text"
                    size="mini"
                    @click="markAsAnswer(reply)">
                    标记为答案
                  </el-button>
                </div>
              </div>
              <div class="post-content" v-html="reply.content"></div>
              <div v-if="reply.is_answer" class="answer-badge">
                <i class="el-icon-check"></i> 最佳答案
              </div>
            </div>
          </div>
        </div>

        <!-- 添加回复 -->
        <div class="add-reply-section">
          <h3>添加回复</h3>
          <el-form :model="replyForm" :rules="replyRules" ref="replyForm">
            <el-form-item prop="content">
              <el-input
                type="textarea"
                v-model="replyForm.content"
                :rows="4"
                placeholder="请输入回复内容">
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitReply" :loading="submittingReply">提交回复</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>

    <!-- 新建讨论对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="newDiscussionVisible" width="50%">
      <el-form :model="discussionForm" :rules="discussionRules" ref="discussionForm" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="discussionForm.title" placeholder="请输入讨论标题"></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            type="textarea"
            v-model="discussionForm.content"
            :rows="6"
            placeholder="请输入讨论内容">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newDiscussionVisible = false">取消</el-button>
        <el-button type="primary" @click="submitDiscussion" :loading="submittingDiscussion">发布</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CourseDiscussions',
  props: {
    courseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      discussions: [],
      loading: false,
      searchQuery: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      discussionVisible: false,
      discussionLoading: false,
      currentDiscussion: {},
      replies: [],
      replyForm: {
        content: ''
      },
      replyRules: {
        content: [
          { required: true, message: '请输入回复内容', trigger: 'blur' },
          { min: 5, message: '回复内容至少5个字符', trigger: 'blur' }
        ]
      },
      submittingReply: false,
      newDiscussionVisible: false,
      dialogTitle: '发起新讨论',
      discussionForm: {
        id: null,
        title: '',
        content: ''
      },
      discussionRules: {
        title: [
          { required: true, message: '请输入讨论标题', trigger: 'blur' },
          { min: 5, max: 100, message: '标题长度在5到100个字符之间', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入讨论内容', trigger: 'blur' },
          { min: 10, message: '讨论内容至少10个字符', trigger: 'blur' }
        ]
      },
      submittingDiscussion: false
    }
  },
  methods: {
    async fetchDiscussions() {
      this.loading = true
      try {
        // 从API获取数据
        const response = await this.$http.get(`/api/discussions/course/${this.courseId}`, {
          params: {
            search: this.searchQuery,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.discussions = response.data.data || []
          this.total = response.data.total || 0
        } else {
          throw new Error('获取讨论失败')
        }

        this.loading = false
      } catch (error) {
        console.error('获取讨论失败:', error)
        this.$message.error('获取讨论失败，请稍后再试')
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchDiscussions()
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    async viewDiscussion(discussion) {
      this.currentDiscussion = discussion
      this.discussionVisible = true
      this.discussionLoading = true

      try {
        // 从API获取讨论详情
        const response = await this.$http.get(`/api/discussions/${discussion.id}`)

        if (response.data.success) {
          // 更新当前讨论和回复
          this.currentDiscussion = response.data.discussion || discussion
          this.replies = response.data.replies || []
        } else {
          throw new Error('获取讨论详情失败')
        }

        this.discussionLoading = false
      } catch (error) {
        console.error('获取讨论详情失败:', error)
        this.$message.error('获取讨论详情失败，请稍后再试')
        this.discussionLoading = false
      }
    },
    isCurrentUserAuthor(post) {
      // 实际应该比较当前用户ID和帖子作者ID
      // return this.$store.state.user.id === post.author_id

      // 模拟：假设当前用户是张三（ID为3）
      return post.author_id === 3
    },
    async submitReply() {
      this.$refs.replyForm.validate(async valid => {
        if (valid) {
          this.submittingReply = true
          try {
            // 调用API提交回复
            const response = await this.$http.post(`/api/discussions/${this.currentDiscussion.id}/replies`, {
              content: this.replyForm.content
            })

            if (response.data.success) {
              // 如果API返回了新回复，使用它
              let newReply = response.data.data

              // 如果API没有返回完整的回复对象，构造一个
              if (!newReply) {
                newReply = {
                  id: this.replies.length + 1,
                  discussion_id: this.currentDiscussion.id,
                  content: this.replyForm.content,
                  author_id: this.$store.state.user?.id,
                  author_name: this.$store.state.user?.username || '当前用户',
                  author_avatar: null,
                  created_at: new Date(),
                  is_answer: false
                }
              }

              // 添加新回复到列表
              this.replies.push(newReply)

              // 更新讨论的回复数
              this.currentDiscussion.reply_count = (this.currentDiscussion.reply_count || 0) + 1

              // 更新原始讨论列表中的回复数
              const index = this.discussions.findIndex(d => d.id === this.currentDiscussion.id)
              if (index !== -1) {
                this.discussions[index].reply_count = (this.discussions[index].reply_count || 0) + 1
              }

              this.$message.success('回复发布成功')
              this.replyForm.content = ''
            } else {
              throw new Error(response.data.message || '发布回复失败')
            }
          } catch (error) {
            console.error('发布回复失败:', error)
            this.$message.error('发布回复失败，请稍后再试')
          } finally {
            this.submittingReply = false
          }
        }
      })
    },
    showNewDiscussionDialog() {
      this.dialogTitle = '发起新讨论'
      this.discussionForm = {
        id: null,
        title: '',
        content: ''
      }
      this.newDiscussionVisible = true
      this.$nextTick(() => {
        this.$refs.discussionForm.clearValidate()
      })
    },
    editDiscussion(discussion) {
      this.dialogTitle = '编辑讨论'
      this.discussionForm = {
        id: discussion.id,
        title: discussion.title,
        content: discussion.content
      }
      this.newDiscussionVisible = true
      this.$nextTick(() => {
        this.$refs.discussionForm.clearValidate()
      })
    },
    editReply(reply) {
      this.replyForm.content = reply.content
      // 实际应用中可能需要更复杂的编辑逻辑
      this.$message.info('编辑回复功能暂未实现')
    },
    async submitDiscussion() {
      this.$refs.discussionForm.validate(async valid => {
        if (valid) {
          this.submittingDiscussion = true
          try {
            if (this.discussionForm.id) {
              // 编辑现有讨论
              const response = await this.$http.put(`/api/discussions/${this.discussionForm.id}`, {
                title: this.discussionForm.title,
                content: this.discussionForm.content
              })

              if (response.data.success) {
                // 更新讨论列表
                const index = this.discussions.findIndex(d => d.id === this.discussionForm.id)
                if (index !== -1) {
                  this.discussions[index].title = this.discussionForm.title
                  this.discussions[index].content = this.discussionForm.content
                  this.discussions[index].updated_at = new Date()
                }

                this.$message.success('讨论更新成功')
              } else {
                throw new Error(response.data.message || '更新讨论失败')
              }
            } else {
              // 创建新讨论
              const response = await this.$http.post(`/api/discussions`, {
                title: this.discussionForm.title,
                content: this.discussionForm.content,
                course_id: this.courseId
              })

              if (response.data.success) {
                // 如果API返回了新讨论，使用它
                let newDiscussion = response.data.data

                // 如果API没有返回完整的讨论对象，构造一个
                if (!newDiscussion) {
                  newDiscussion = {
                    id: this.discussions.length + 1,
                    title: this.discussionForm.title,
                    content: this.discussionForm.content,
                    author_id: this.$store.state.user?.id,
                    author_name: this.$store.state.user?.username || '当前用户',
                    author_avatar: null,
                    created_at: new Date(),
                    updated_at: null,
                    view_count: 0,
                    reply_count: 0,
                    is_pinned: false,
                    is_answered: false
                  }
                }

                this.discussions.unshift(newDiscussion)
                this.total++

                this.$message.success('讨论发布成功')
              } else {
                throw new Error(response.data.message || '发布讨论失败')
              }
            }

            this.newDiscussionVisible = false
          } catch (error) {
            console.error('保存讨论失败:', error)
            this.$message.error('保存讨论失败，请稍后再试')
          } finally {
            this.submittingDiscussion = false
          }
        }
      })
    },
    async markAsAnswer(reply) {
      try {
        // 实际应该调用API
        // await this.$http.post(`/student/discussions/${this.currentDiscussion.id}/mark-answer`, { reply_id: reply.id })

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 更新回复状态
        this.replies.forEach(r => {
          r.is_answer = r.id === reply.id
        })

        // 更新讨论状态
        this.currentDiscussion.is_answered = true

        // 更新原始讨论列表
        const index = this.discussions.findIndex(d => d.id === this.currentDiscussion.id)
        if (index !== -1) {
          this.discussions[index].is_answered = true
        }

        this.$message.success('已将回复标记为最佳答案')
      } catch (error) {
        console.error('标记答案失败:', error)
        this.$message.error('标记答案失败，请稍后再试')
      }
    }
  },
  created() {
    this.fetchDiscussions()
  }
}
</script>

<style scoped>
.course-discussions {
  padding: 10px 0;
}

.discussions-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container, .discussion-loading {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.discussion-card {
  margin-bottom: 20px;
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.discussion-author {
  display: flex;
  align-items: center;
}

.author-info {
  margin-left: 10px;
}

.author-name {
  font-weight: bold;
}

.post-time {
  font-size: 12px;
  color: #909399;
}

.discussion-stats {
  display: flex;
  gap: 15px;
  color: #909399;
}

.discussion-title {
  margin-top: 0;
  margin-bottom: 10px;
  cursor: pointer;
  color: #409EFF;
}

.discussion-title:hover {
  text-decoration: underline;
}

.discussion-summary {
  color: #606266;
  margin-bottom: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.discussion-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.post-item {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.post-item.original-post {
  background-color: #ecf5ff;
}

.post-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.post-author {
  display: flex;
  align-items: center;
}

.post-content {
  line-height: 1.6;
}

.post-content pre {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.post-content code {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
}

.replies-header {
  margin: 20px 0;
}

.empty-replies {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.answer-badge {
  display: inline-block;
  margin-top: 10px;
  padding: 5px 10px;
  background-color: #67c23a;
  color: white;
  border-radius: 4px;
}

.add-reply-section {
  margin-top: 30px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .discussions-header {
    flex-direction: column;
    gap: 10px;
  }

  .filter-bar {
    width: 100%;
  }

  .filter-bar .el-input {
    width: 100%;
  }

  .discussion-header {
    flex-direction: column;
  }

  .discussion-author {
    margin-bottom: 10px;
  }
}
</style>
