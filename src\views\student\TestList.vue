<template>
  <div class="test-list-container">
    <div class="page-header">
      <h1>我的测试</h1>
    </div>

    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="待完成测试" name="pending">
        <div class="filter-bar">
          <el-input
            placeholder="搜索测试名称"
            v-model="searchQuery"
            prefix-icon="el-icon-search"
            clearable
            @clear="fetchTests"
            @keyup.enter.native="fetchTests">
          </el-input>
          <el-select v-model="courseFilter" placeholder="选择课程" clearable @change="fetchTests">
            <el-option
              v-for="course in courses"
              :key="course.id"
              :label="course.title"
              :value="course.id">
            </el-option>
          </el-select>
          <el-button type="primary" @click="fetchTests">搜索</el-button>
        </div>

        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>

        <div v-else-if="pendingTests.length === 0" class="empty-data">
          <el-empty description="暂无待完成测试"></el-empty>
        </div>

        <el-table
          v-else
          :data="pendingTests"
          style="width: 100%"
          border>
          <el-table-column
            prop="title"
            label="测试名称"
            min-width="200">
          </el-table-column>
          <el-table-column
            prop="course_title"
            label="所属课程"
            width="180">
          </el-table-column>
          <el-table-column
            prop="teacher_name"
            label="发布教师"
            width="120">
          </el-table-column>
          <el-table-column
            label="截止时间"
            width="180">
            <template slot-scope="scope">
              <div>{{ formatDate(scope.row.end_time) }}</div>
              <div>
                <el-tag
                  :type="getTimeRemainingType(scope.row.end_time)"
                  size="mini">
                  {{ getTimeRemaining(scope.row.end_time) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            align="center">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                @click="startTest(scope.row)">
                开始测试
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container" v-if="pendingTests.length > 0">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="pendingTotal"
            :page-size="pageSize"
            :current-page.sync="pendingPage"
            @current-change="handlePendingPageChange">
          </el-pagination>
        </div>
      </el-tab-pane>

      <el-tab-pane label="已完成测试" name="completed">
        <div class="filter-bar">
          <el-input
            placeholder="搜索测试名称"
            v-model="completedSearchQuery"
            prefix-icon="el-icon-search"
            clearable
            @clear="fetchCompletedTests"
            @keyup.enter.native="fetchCompletedTests">
          </el-input>
          <el-select v-model="completedCourseFilter" placeholder="选择课程" clearable @change="fetchCompletedTests">
            <el-option
              v-for="course in courses"
              :key="course.id"
              :label="course.title"
              :value="course.id">
            </el-option>
          </el-select>
          <el-button type="primary" @click="fetchCompletedTests">搜索</el-button>
        </div>

        <div v-if="completedLoading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>

        <div v-else-if="completedTests.length === 0" class="empty-data">
          <el-empty description="暂无已完成测试"></el-empty>
        </div>

        <el-table
          v-else
          :data="completedTests"
          style="width: 100%"
          border>
          <el-table-column
            prop="title"
            label="测试名称"
            min-width="200">
          </el-table-column>
          <el-table-column
            prop="course_title"
            label="所属课程"
            width="180">
          </el-table-column>
          <el-table-column
            prop="score"
            label="得分"
            width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.score || '未评分' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="提交时间"
            width="180">
            <template slot-scope="scope">
              {{ formatDate(scope.row.submit_time) }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            align="center">
            <template slot-scope="scope">
              <el-button
                type="info"
                size="mini"
                @click="viewResult(scope.row)">
                查看结果
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container" v-if="completedTests.length > 0">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="completedTotal"
            :page-size="pageSize"
            :current-page.sync="completedPage"
            @current-change="handleCompletedPageChange">
          </el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'TestList',
  data() {
    return {
      activeTab: 'pending',
      // 待完成测试
      pendingTests: [],
      pendingTotal: 0,
      pendingPage: 1,
      searchQuery: '',
      courseFilter: '',
      loading: false,
      // 已完成测试
      completedTests: [],
      completedTotal: 0,
      completedPage: 1,
      completedSearchQuery: '',
      completedCourseFilter: '',
      completedLoading: false,
      // 通用
      courses: [],
      pageSize: 10
    }
  },
  methods: {
    async fetchTests() {
      this.loading = true
      try {
        // 从API获取待完成测试数据
        const response = await this.$http.get('/api/tests/pending', {
          params: {
            search: this.searchQuery,
            course_id: this.courseFilter,
            page: this.pendingPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.pendingTests = response.data.data || []
          this.pendingTotal = response.data.total || 0
        } else {
          throw new Error('获取待完成测试失败')
        }
      } catch (error) {
        console.error('获取待完成测试失败:', error)
        // 使用模拟数据
        this.pendingTests = [
          {
            id: 1,
            title: 'JavaScript基础测试',
            course_id: 1,
            course_title: 'Web开发基础',
            teacher_name: '张教授',
            end_time: new Date(Date.now() + 86400000 * 3) // 3天后
          },
          {
            id: 2,
            title: 'CSS布局测试',
            course_id: 1,
            course_title: 'Web开发基础',
            teacher_name: '张教授',
            end_time: new Date(Date.now() + 86400000 * 5) // 5天后
          }
        ]
        this.pendingTotal = this.pendingTests.length
      } finally {
        this.loading = false
      }
    },
    async fetchCompletedTests() {
      this.completedLoading = true
      try {
        // 从API获取已完成测试数据
        const response = await this.$http.get('/api/tests/completed', {
          params: {
            search: this.completedSearchQuery,
            course_id: this.completedCourseFilter,
            page: this.completedPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.completedTests = response.data.data || []
          this.completedTotal = response.data.total || 0
        } else {
          throw new Error('获取已完成测试失败')
        }
      } catch (error) {
        console.error('获取已完成测试失败:', error)
        // 使用模拟数据
        this.completedTests = [
          {
            id: 3,
            title: 'HTML基础测试',
            course_id: 1,
            course_title: 'Web开发基础',
            score: 85,
            submit_time: new Date(Date.now() - 86400000 * 2) // 2天前
          },
          {
            id: 4,
            title: '网络协议测试',
            course_id: 2,
            course_title: '计算机网络',
            score: 92,
            submit_time: new Date(Date.now() - 86400000 * 5) // 5天前
          }
        ]
        this.completedTotal = this.completedTests.length
      } finally {
        this.completedLoading = false
      }
    },
    async fetchCourses() {
      try {
        // 从API获取课程数据
        const response = await this.$http.get('/api/courses/enrolled')

        if (response.data.success) {
          this.courses = response.data.data || []
        } else {
          throw new Error('获取课程失败')
        }
      } catch (error) {
        console.error('获取课程失败:', error)
        // 使用模拟数据
        this.courses = [
          { id: 1, title: 'Web开发基础' },
          { id: 2, title: '计算机网络' }
        ]
      }
    },
    handlePendingPageChange(page) {
      this.pendingPage = page
      this.fetchTests()
    },
    handleCompletedPageChange(page) {
      this.completedPage = page
      this.fetchCompletedTests()
    },
    startTest(test) {
      this.$router.push(`/student/courses/${test.course_id}/test/${test.id}`)
    },
    viewResult(test) {
      this.$router.push(`/student/tests/${test.id}/result`)
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    getTimeRemainingType(endTime) {
      const now = new Date()
      const end = new Date(endTime)
      const diffHours = (end - now) / (1000 * 60 * 60)

      if (diffHours < 24) {
        return 'danger'
      } else if (diffHours < 72) {
        return 'warning'
      } else {
        return 'primary'
      }
    },
    getTimeRemaining(endTime) {
      const now = new Date()
      const end = new Date(endTime)
      const diffMs = end - now

      if (diffMs < 0) {
        return '已截止'
      }

      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

      if (diffDays > 0) {
        return `剩余 ${diffDays} 天 ${diffHours} 小时`
      } else {
        return `剩余 ${diffHours} 小时`
      }
    }
  },
  created() {
    this.fetchTests()
    this.fetchCompletedTests()
    this.fetchCourses()
  }
}
</script>

<style scoped>
.test-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.loading-container, .empty-data {
  padding: 20px;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}
</style>
