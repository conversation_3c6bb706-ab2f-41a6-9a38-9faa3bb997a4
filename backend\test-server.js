const axios = require('axios');

async function testServer() {
  try {
    console.log('🔍 测试后端服务器连接...\n');
    
    // 1. 测试服务器是否运行
    console.log('1. 测试服务器基本连接:');
    try {
      const response = await axios.get('http://localhost:5000/');
      console.log(`   ✅ 服务器响应: ${response.data}`);
    } catch (error) {
      console.log('   ❌ 服务器未运行或无法连接');
      console.log('   💡 请确保后端服务器正在运行: npm start 或 node server.js');
      return;
    }
    
    // 2. 测试公告API端点
    console.log('\n2. 测试公告API端点:');
    try {
      // 先尝试不带认证的请求（应该返回401）
      const response = await axios.get('http://localhost:5000/api/announcements');
      console.log('   ⚠️ 意外：API没有要求认证');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('   ✅ API正确要求认证 (401 Unauthorized)');
      } else {
        console.log(`   ❌ API错误: ${error.message}`);
      }
    }
    
    // 3. 测试管理员登录
    console.log('\n3. 测试管理员登录:');
    try {
      const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
        username: 'admin',
        password: '123456'
      });
      
      if (loginResponse.data.success) {
        console.log('   ✅ 管理员登录成功');
        const token = loginResponse.data.token;
        
        // 4. 使用token测试公告API
        console.log('\n4. 使用token测试公告API:');
        const announcementsResponse = await axios.get('http://localhost:5000/api/announcements', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (announcementsResponse.data.success) {
          console.log(`   ✅ 获取公告成功，共 ${announcementsResponse.data.data.length} 条`);
          
          // 5. 测试创建公告
          console.log('\n5. 测试创建公告:');
          const createResponse = await axios.post('http://localhost:5000/api/announcements', {
            title: '前后端连接测试公告',
            content: '这是一个测试前后端连接的公告',
            type: 'system',
            is_pinned: false,
            is_important: false
          }, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (createResponse.data.success) {
            console.log('   ✅ 创建公告成功');
            const newAnnouncementId = createResponse.data.data.id;
            
            // 6. 删除测试公告
            console.log('\n6. 清理测试数据:');
            await axios.delete(`http://localhost:5000/api/announcements/${newAnnouncementId}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });
            console.log('   ✅ 测试公告已删除');
          } else {
            console.log('   ❌ 创建公告失败:', createResponse.data.message);
          }
        } else {
          console.log('   ❌ 获取公告失败:', announcementsResponse.data.message);
        }
      } else {
        console.log('   ❌ 管理员登录失败:', loginResponse.data.message);
      }
    } catch (error) {
      console.log('   ❌ 登录请求失败:', error.message);
    }
    
    console.log('\n✅ 前后端连接测试完成！');
    console.log('\n💡 如果所有测试都通过，说明：');
    console.log('   - 后端服务器正常运行');
    console.log('   - 公告API功能正常');
    console.log('   - 认证系统工作正常');
    console.log('   - 前端应该能够正常连接后端');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }
}

testServer();
