<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-learning API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .btn-blue {
            background-color: #2196F3;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <h1>E-learning API 测试</h1>
    
    <div class="card">
        <h2>测试数据库连接</h2>
        <button id="testDbBtn" class="btn">测试连接</button>
        <div id="dbResult" class="result"></div>
    </div>
    
    <div class="card">
        <h2>获取所有用户</h2>
        <button id="getUsersBtn" class="btn">获取用户</button>
        <div id="usersResult" class="result"></div>
    </div>
    
    <div class="card">
        <h2>获取所有课程</h2>
        <button id="getCoursesBtn" class="btn">获取课程</button>
        <div id="coursesResult" class="result"></div>
    </div>
    
    <div class="card">
        <h2>创建公告</h2>
        <div class="form-group">
            <label for="title">标题:</label>
            <input type="text" id="title" placeholder="输入公告标题">
        </div>
        <div class="form-group">
            <label for="content">内容:</label>
            <textarea id="content" rows="4" placeholder="输入公告内容"></textarea>
        </div>
        <button id="createAnnouncementBtn" class="btn btn-blue">创建公告</button>
        <div id="announcementResult" class="result"></div>
    </div>
    
    <div class="card">
        <h2>学生选课</h2>
        <div class="form-group">
            <label for="studentId">学生ID:</label>
            <input type="text" id="studentId" placeholder="输入学生ID">
        </div>
        <div class="form-group">
            <label for="courseCode">课程代码:</label>
            <input type="text" id="courseCode" placeholder="输入课程代码">
        </div>
        <button id="enrollBtn" class="btn btn-blue">选课</button>
        <div id="enrollResult" class="result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:5000/api/test';
        
        // 测试数据库连接
        document.getElementById('testDbBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.textContent = '正在测试连接...';
            
            try {
                const response = await fetch(`${API_URL}/db-connection`);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        });
        
        // 获取所有用户
        document.getElementById('getUsersBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('usersResult');
            resultDiv.textContent = '正在获取用户...';
            
            try {
                const response = await fetch(`${API_URL}/users`);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        });
        
        // 获取所有课程
        document.getElementById('getCoursesBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('coursesResult');
            resultDiv.textContent = '正在获取课程...';
            
            try {
                const response = await fetch(`${API_URL}/courses`);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        });
        
        // 创建公告
        document.getElementById('createAnnouncementBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('announcementResult');
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            
            if (!title || !content) {
                resultDiv.textContent = '错误: 标题和内容不能为空';
                return;
            }
            
            resultDiv.textContent = '正在创建公告...';
            
            try {
                const response = await fetch(`${API_URL}/announcement`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ title, content })
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        });
        
        // 学生选课
        document.getElementById('enrollBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('enrollResult');
            const studentId = document.getElementById('studentId').value;
            const courseCode = document.getElementById('courseCode').value;
            
            if (!studentId || !courseCode) {
                resultDiv.textContent = '错误: 学生ID和课程代码不能为空';
                return;
            }
            
            resultDiv.textContent = '正在选课...';
            
            try {
                const response = await fetch(`${API_URL}/enroll`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ studentId, courseCode })
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
            }
        });
    </script>
</body>
</html>
