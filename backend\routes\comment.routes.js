const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect, authorize } = require('../middleware/auth');

// 获取评论列表
router.get('/', protect, async (req, res) => {
  try {
    const { search, type, target_id, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let query = `
      SELECT c.*, u.username as user_name, u.role as user_role
      FROM comments c
      JOIN users u ON c.user_id = u.id
    `;
    const queryParams = [];

    // 添加搜索条件
    const conditions = [];
    if (search) {
      conditions.push('c.content LIKE ?');
      queryParams.push(`%${search}%`);
    }

    if (type) {
      conditions.push('c.type = ?');
      queryParams.push(type);
    }

    if (target_id) {
      conditions.push('c.target_id = ?');
      queryParams.push(target_id);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // 添加排序和分页
    query += ' ORDER BY c.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [rows] = await pool.query(query, queryParams);

    // 获取相关信息
    for (let i = 0; i < rows.length; i++) {
      const comment = rows[i];

      // 根据类型获取目标信息
      if (comment.type === 'course') {
        const [courseRows] = await pool.query(
          'SELECT id, title FROM courses WHERE id = ?',
          [comment.target_id]
        );
        if (courseRows.length > 0) {
          comment.target_info = courseRows[0];
        }
      } else if (comment.type === 'announcement') {
        const [announcementRows] = await pool.query(
          'SELECT id, title FROM announcements WHERE id = ?',
          [comment.target_id]
        );
        if (announcementRows.length > 0) {
          comment.target_info = announcementRows[0];
        }
      } else if (comment.type === 'resource') {
        const [resourceRows] = await pool.query(
          'SELECT id, title FROM resources WHERE id = ?',
          [comment.target_id]
        );
        if (resourceRows.length > 0) {
          comment.target_info = resourceRows[0];
        }
      }

      // 获取父评论信息
      if (comment.parent_id) {
        const [parentRows] = await pool.query(
          'SELECT c.content, u.username FROM comments c JOIN users u ON c.user_id = u.id WHERE c.id = ?',
          [comment.parent_id]
        );
        if (parentRows.length > 0) {
          comment.parent_comment = parentRows[0];
        }
      }
    }

    // 获取总数
    let countQuery = 'SELECT COUNT(*) as total FROM comments c';
    const countParams = [];

    if (conditions.length > 0) {
      countQuery += ' WHERE ' + conditions.join(' AND ').replace(/c\./g, '');
      countParams.push(...queryParams.slice(0, -2)); // 排除limit和offset参数
    }

    const [countRows] = await pool.query(countQuery, countParams);
    const total = countRows[0].total;

    res.status(200).json({
      success: true,
      count: rows.length,
      total,
      data: rows
    });
  } catch (error) {
    console.error('获取评论失败:', error);
    res.status(500).json({
      success: false,
      message: '获取评论失败',
      error: error.message
    });
  }
});

// 获取单个评论
router.get('/:id', protect, async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT c.*, u.username as user_name, u.role as user_role
       FROM comments c
       JOIN users u ON c.user_id = u.id
       WHERE c.id = ?`,
      [req.params.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    const comment = rows[0];

    // 获取目标信息
    if (comment.type === 'course') {
      const [courseRows] = await pool.query(
        'SELECT id, title FROM courses WHERE id = ?',
        [comment.target_id]
      );
      if (courseRows.length > 0) {
        comment.target_info = courseRows[0];
      }
    }

    // 获取父评论信息
    if (comment.parent_id) {
      const [parentRows] = await pool.query(
        'SELECT c.content, u.username FROM comments c JOIN users u ON c.user_id = u.id WHERE c.id = ?',
        [comment.parent_id]
      );
      if (parentRows.length > 0) {
        comment.parent_comment = parentRows[0];
      }
    }

    res.status(200).json({
      success: true,
      data: comment
    });
  } catch (error) {
    console.error('获取评论失败:', error);
    res.status(500).json({
      success: false,
      message: '获取评论失败',
      error: error.message
    });
  }
});

// 创建评论
router.post('/', protect, async (req, res) => {
  try {
    const { content, type, target_id, parent_id } = req.body;

    // 验证必要字段
    if (!content || !type || !target_id) {
      return res.status(400).json({
        success: false,
        message: '请提供评论内容、类型和目标ID'
      });
    }

    // 验证目标存在
    let targetExists = false;
    if (type === 'course') {
      const [courseRows] = await pool.query('SELECT id FROM courses WHERE id = ?', [target_id]);
      targetExists = courseRows.length > 0;
    } else if (type === 'announcement') {
      const [announcementRows] = await pool.query('SELECT id FROM announcements WHERE id = ?', [target_id]);
      targetExists = announcementRows.length > 0;
    } else if (type === 'resource') {
      const [resourceRows] = await pool.query('SELECT id FROM resources WHERE id = ?', [target_id]);
      targetExists = resourceRows.length > 0;
    }

    if (!targetExists) {
      return res.status(404).json({
        success: false,
        message: '目标不存在'
      });
    }

    // 如果有父评论，验证父评论存在
    if (parent_id) {
      const [parentRows] = await pool.query('SELECT id FROM comments WHERE id = ?', [parent_id]);
      if (parentRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '父评论不存在'
        });
      }
    }

    // 插入评论
    const [result] = await pool.query(
      `INSERT INTO comments (content, type, target_id, user_id, parent_id, is_approved)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [content, type, target_id, req.user.id, parent_id || null, true]
    );

    // 获取新创建的评论
    const [commentRows] = await pool.query(
      `SELECT c.*, u.username as user_name, u.role as user_role
       FROM comments c
       JOIN users u ON c.user_id = u.id
       WHERE c.id = ?`,
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '评论创建成功',
      data: commentRows[0]
    });
  } catch (error) {
    console.error('创建评论失败:', error);
    res.status(500).json({
      success: false,
      message: '创建评论失败',
      error: error.message
    });
  }
});

// 更新评论 (仅管理员和原作者)
router.put('/:id', protect, async (req, res) => {
  try {
    const { content, is_approved } = req.body;
    const commentId = req.params.id;

    // 验证评论存在
    const [commentRows] = await pool.query(
      'SELECT * FROM comments WHERE id = ?',
      [commentId]
    );

    if (commentRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    const comment = commentRows[0];

    // 验证权限 (仅管理员和原作者可以更新)
    if (req.user.role !== 'admin' && comment.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您没有权限更新此评论'
      });
    }

    // 更新评论
    const updateFields = [];
    const updateValues = [];

    if (content !== undefined) {
      updateFields.push('content = ?');
      updateValues.push(content);
    }

    if (is_approved !== undefined && req.user.role === 'admin') {
      updateFields.push('is_approved = ?');
      updateValues.push(is_approved);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有要更新的字段'
      });
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(commentId);

    await pool.query(
      `UPDATE comments SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 获取更新后的评论
    const [updatedRows] = await pool.query(
      `SELECT c.*, u.username as user_name, u.role as user_role
       FROM comments c
       JOIN users u ON c.user_id = u.id
       WHERE c.id = ?`,
      [commentId]
    );

    res.status(200).json({
      success: true,
      message: '评论更新成功',
      data: updatedRows[0]
    });
  } catch (error) {
    console.error('更新评论失败:', error);
    res.status(500).json({
      success: false,
      message: '更新评论失败',
      error: error.message
    });
  }
});

// 删除评论 (仅管理员和原作者)
router.delete('/:id', protect, async (req, res) => {
  try {
    const commentId = req.params.id;

    // 验证评论存在
    const [commentRows] = await pool.query(
      'SELECT * FROM comments WHERE id = ?',
      [commentId]
    );

    if (commentRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    const comment = commentRows[0];

    // 验证权限 (仅管理员和原作者可以删除)
    if (req.user.role !== 'admin' && comment.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您没有权限删除此评论'
      });
    }

    // 删除评论 (级联删除会自动删除子评论)
    await pool.query('DELETE FROM comments WHERE id = ?', [commentId]);

    res.status(200).json({
      success: true,
      message: '评论删除成功'
    });
  } catch (error) {
    console.error('删除评论失败:', error);
    res.status(500).json({
      success: false,
      message: '删除评论失败',
      error: error.message
    });
  }
});

// 批量审核评论 (仅管理员)
router.patch('/batch-approve', protect, authorize('admin'), async (req, res) => {
  try {
    const { comment_ids, is_approved } = req.body;

    if (!Array.isArray(comment_ids) || comment_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要审核的评论ID列表'
      });
    }

    // 批量更新评论审核状态
    const placeholders = comment_ids.map(() => '?').join(',');
    await pool.query(
      `UPDATE comments SET is_approved = ?, updated_at = NOW() WHERE id IN (${placeholders})`,
      [is_approved, ...comment_ids]
    );

    res.status(200).json({
      success: true,
      message: `批量${is_approved ? '通过' : '拒绝'}评论成功`
    });
  } catch (error) {
    console.error('批量审核评论失败:', error);
    res.status(500).json({
      success: false,
      message: '批量审核评论失败',
      error: error.message
    });
  }
});

module.exports = router;
