<template>
  <div class="take-test">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else>
      <!-- 测试头部信息 -->
      <div class="test-header">
        <div class="test-info">
          <h1>{{ test.title }}</h1>
          <div class="test-meta">
            <span><i class="el-icon-time"></i> 时长: {{ test.duration }} 分钟</span>
            <span><i class="el-icon-trophy"></i> 总分: {{ test.total_points }} 分</span>
            <span><i class="el-icon-alarm-clock"></i> 截止时间: {{ formatDateTime(test.end_time) }}</span>
          </div>
        </div>
        <div class="test-timer" v-if="!isSubmitted">
          <div class="timer-label">剩余时间</div>
          <div class="timer-value" :class="{'timer-warning': remainingTime <= 300}">
            {{ formatTime(remainingTime) }}
          </div>
        </div>
      </div>

      <!-- 测试已提交提示 -->
      <div v-if="isSubmitted" class="test-submitted">
        <el-result
          icon="success"
          title="测试已提交"
          sub-title="您的测试已成功提交，请等待教师评阅。">
          <template #extra>
            <el-button type="primary" @click="goBack">返回课程</el-button>
          </template>
        </el-result>
      </div>

      <!-- 测试内容 -->
      <div v-else class="test-content">
        <el-steps :active="currentQuestionIndex + 1" finish-status="success" simple style="margin-bottom: 20px">
          <el-step
            v-for="(question, index) in questions"
            :key="index"
            :title="`问题 ${index + 1}`"
            @click.native="goToQuestion(index)">
          </el-step>
        </el-steps>

        <div class="question-container">
          <div class="question-header">
            <div class="question-number">问题 {{ currentQuestionIndex + 1 }} / {{ questions.length }}</div>
            <div class="question-points">{{ currentQuestion.points }} 分</div>
          </div>

          <div class="question-content">{{ currentQuestion.content }}</div>

          <!-- 单选题 -->
          <div v-if="currentQuestion.type === 'single_choice'" class="question-options">
            <el-radio-group v-model="answers[currentQuestionIndex]">
              <el-radio
                v-for="(option, index) in currentQuestion.options"
                :key="index"
                :label="option.id"
                class="option-item">
                {{ option.content }}
              </el-radio>
            </el-radio-group>
          </div>

          <!-- 多选题 -->
          <div v-else-if="currentQuestion.type === 'multiple_choice'" class="question-options">
            <el-checkbox-group v-model="answers[currentQuestionIndex]">
              <el-checkbox
                v-for="(option, index) in currentQuestion.options"
                :key="index"
                :label="option.id"
                class="option-item">
                {{ option.content }}
              </el-checkbox>
            </el-checkbox-group>
          </div>

          <!-- 填空题 -->
          <div v-else-if="currentQuestion.type === 'fill_blank'" class="question-fill-blank">
            <el-input
              v-model="answers[currentQuestionIndex]"
              placeholder="请输入答案"
              type="text">
            </el-input>
          </div>

          <!-- 简答题 -->
          <div v-else-if="currentQuestion.type === 'text'" class="question-text">
            <el-input
              v-model="answers[currentQuestionIndex]"
              placeholder="请输入答案"
              type="textarea"
              :rows="6">
            </el-input>
          </div>

          <div class="question-actions">
            <el-button
              v-if="currentQuestionIndex > 0"
              @click="prevQuestion">
              上一题
            </el-button>
            <el-button
              v-if="currentQuestionIndex < questions.length - 1"
              type="primary"
              @click="nextQuestion">
              下一题
            </el-button>
            <el-button
              v-else
              type="success"
              @click="showSubmitConfirm">
              提交测试
            </el-button>
          </div>
        </div>

        <div class="question-navigation">
          <div class="navigation-title">问题导航</div>
          <div class="navigation-buttons">
            <el-button
              v-for="(question, index) in questions"
              :key="index"
              size="mini"
              :type="isQuestionAnswered(index) ? 'success' : 'info'"
              @click="goToQuestion(index)">
              {{ index + 1 }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TakeTest',
  props: {
    id: {
      type: [Number, String],
      required: true
    },
    testId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      courseId: parseInt(this.id),
      test: {},
      questions: [],
      answers: [],
      currentQuestionIndex: 0,
      loading: true,
      isSubmitted: false,
      remainingTime: 0,
      timer: null
    }
  },
  computed: {
    currentQuestion() {
      return this.questions[this.currentQuestionIndex] || {}
    }
  },
  methods: {
    async fetchTest() {
      this.loading = true
      try {
        // 从API获取测试数据
        const response = await this.$http.get(`/api/tests/${this.testId}`)

        if (response.data.success) {
          this.test = response.data.test || {}
          this.questions = response.data.questions || []

          // 设置剩余时间
          if (this.test.time_limit) {
            this.remainingTime = this.test.time_limit * 60 // 转换为秒
            this.startTimer()
          }

        } else {
          throw new Error(response.data.message || '获取测试失败')
        }

        // 初始化答案数组
        this.answers = this.questions.map(q => {
          if (q.type === 'multiple_choice') {
            return []
          } else if (q.type === 'single_choice') {
            return null
          } else {
            return ''
          }
        })

      } catch (error) {
        console.error('获取测试失败:', error)
        this.$message.error('获取测试失败，请稍后再试')
        this.$router.push(`/student/courses/${this.courseId}`)
      } finally {
        this.loading = false
      }
    },
    formatDateTime(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    },
    startTimer() {
      this.timer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--
        } else {
          this.submitTest()
        }
      }, 1000)
    },
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    prevQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--
      }
    },
    nextQuestion() {
      if (this.currentQuestionIndex < this.questions.length - 1) {
        this.currentQuestionIndex++
      }
    },
    goToQuestion(index) {
      if (index >= 0 && index < this.questions.length) {
        this.currentQuestionIndex = index
      }
    },
    isQuestionAnswered(index) {
      const answer = this.answers[index]
      if (Array.isArray(answer)) {
        return answer.length > 0
      } else if (answer === null || answer === undefined) {
        return false
      } else {
        return answer.trim() !== ''
      }
    },
    showSubmitConfirm() {
      // 检查是否有未回答的问题
      const unansweredCount = this.answers.filter((answer, index) => !this.isQuestionAnswered(index)).length

      let message = '确定要提交测试吗？'
      if (unansweredCount > 0) {
        message = `您还有 ${unansweredCount} 道题未回答，确定要提交吗？`
      }

      this.$confirm(message, '提交确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitTest()
      }).catch(() => {
        // 取消提交
      })
    },
    async submitTest() {
      try {
        // 停止计时器
        this.stopTimer()

        // 调用API提交测试
        const response = await this.$http.post(`/api/tests/${this.testId}/submit`, {
          answers: this.answers
        })

        if (response.data.success) {
          this.isSubmitted = true
          this.$message.success('测试提交成功')
        } else {
          throw new Error(response.data.message || '提交测试失败')
        }
      } catch (error) {
        console.error('提交测试失败:', error)
        this.$message.error('提交测试失败，请稍后再试')

        // 重新启动计时器
        this.startTimer()
      }
    },
    goBack() {
      this.$router.push(`/student/courses/${this.courseId}`)
    }
  },
  created() {
    this.fetchTest()
  },
  beforeDestroy() {
    this.stopTimer()
  }
}
</script>

<style scoped>
.take-test {
  padding: 20px;
}

.loading-container {
  padding: 20px;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
}

.test-info {
  flex: 1;
}

.test-info h1 {
  margin-top: 0;
  margin-bottom: 10px;
}

.test-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #606266;
}

.test-timer {
  text-align: center;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.timer-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.timer-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.timer-warning {
  color: #E6A23C;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.question-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.question-number {
  font-weight: bold;
}

.question-points {
  color: #909399;
}

.question-content {
  font-size: 16px;
  margin-bottom: 20px;
}

.question-options {
  margin-bottom: 20px;
}

.option-item {
  display: block;
  margin-bottom: 10px;
}

.question-fill-blank, .question-text {
  margin-bottom: 20px;
}

.question-actions {
  display: flex;
  justify-content: space-between;
}

.question-navigation {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.navigation-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.navigation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .test-header {
    flex-direction: column;
  }

  .test-timer {
    margin-top: 15px;
    width: 100%;
  }

  .question-actions {
    flex-direction: column;
    gap: 10px;
  }

  .question-actions .el-button {
    width: 100%;
  }
}
</style>
