<template>
  <div class="courses-container">
    <h1>我的课程</h1>
    
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="我的课程" name="my-courses">
        <div class="search-bar">
          <el-input
            placeholder="搜索课程"
            v-model="searchQuery"
            prefix-icon="el-icon-search"
            clearable
            @clear="fetchMyCourses">
          </el-input>
          <el-button type="primary" @click="fetchMyCourses">搜索</el-button>
        </div>
        
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="myCourses.length === 0" class="empty-data">
          <el-empty description="您还没有选修任何课程"></el-empty>
        </div>
        
        <el-row :gutter="20" v-else>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" v-for="course in myCourses" :key="course.id">
            <el-card class="course-card" shadow="hover" @click.native="viewCourseDetail(course.id)">
              <div class="course-image">
                <img :src="getCourseImage(course)" alt="课程封面">
              </div>
              <div class="course-info">
                <h3>{{ course.title }}</h3>
                <p class="teacher">教师: {{ course.teacher_name }}</p>
                <p class="category">
                  <el-tag size="mini">{{ course.category }}</el-tag>
                </p>
                <div class="course-footer">
                  <span class="date">{{ formatDate(course.start_date) }} 开始</span>
                  <el-button type="text" @click.stop="viewCourseDetail(course.id)">查看详情</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      
      <el-tab-pane label="可选课程" name="available-courses">
        <div class="search-bar">
          <el-input
            placeholder="搜索课程"
            v-model="searchQuery"
            prefix-icon="el-icon-search"
            clearable
            @clear="fetchAvailableCourses">
          </el-input>
          <el-select v-model="categoryFilter" placeholder="课程类别" clearable @change="fetchAvailableCourses">
            <el-option
              v-for="item in categories"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-button type="primary" @click="fetchAvailableCourses">搜索</el-button>
        </div>
        
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="availableCourses.length === 0" class="empty-data">
          <el-empty description="暂无可选课程"></el-empty>
        </div>
        
        <el-row :gutter="20" v-else>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" v-for="course in availableCourses" :key="course.id">
            <el-card class="course-card" shadow="hover">
              <div class="course-image">
                <img :src="getCourseImage(course)" alt="课程封面">
              </div>
              <div class="course-info">
                <h3>{{ course.title }}</h3>
                <p class="teacher">教师: {{ course.teacher_name }}</p>
                <p class="category">
                  <el-tag size="mini">{{ course.category }}</el-tag>
                </p>
                <div class="course-footer">
                  <span class="date">{{ formatDate(course.start_date) }} 开始</span>
                  <el-button type="primary" size="mini" @click="enrollCourse(course)">选修课程</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      
      <el-tab-pane label="加入课程" name="join-course">
        <div class="join-course-container">
          <el-card class="join-course-card">
            <div slot="header">
              <span>通过课程代码加入</span>
            </div>
            <el-form :model="joinForm" :rules="joinRules" ref="joinForm" label-width="100px">
              <el-form-item label="课程代码" prop="courseCode">
                <el-input v-model="joinForm.courseCode" placeholder="请输入6位课程代码"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="joinCourse" :loading="joining">加入课程</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'StudentCourses',
  data() {
    return {
      activeTab: 'my-courses',
      searchQuery: '',
      categoryFilter: '',
      myCourses: [],
      availableCourses: [],
      loading: false,
      joining: false,
      joinForm: {
        courseCode: ''
      },
      joinRules: {
        courseCode: [
          { required: true, message: '请输入课程代码', trigger: 'blur' },
          { min: 6, max: 6, message: '课程代码长度为6个字符', trigger: 'blur' }
        ]
      },
      categories: [
        { value: '计算机科学', label: '计算机科学' },
        { value: '软件工程', label: '软件工程' },
        { value: '数据科学', label: '数据科学' },
        { value: '人工智能', label: '人工智能' },
        { value: '网络安全', label: '网络安全' }
      ]
    }
  },
  methods: {
    async fetchMyCourses() {
      this.loading = true
      try {
        // 实际应该从API获取数据
        // const response = await this.$http.get('/student/courses')
        // this.myCourses = response.data.data
        
        // 使用模拟数据
        setTimeout(() => {
          this.myCourses = [
            {
              id: 1,
              title: 'Web开发基础',
              description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
              cover_image: 'web-dev.jpg',
              teacher_name: '张教授',
              category: '计算机科学',
              start_date: '2023-09-01',
              end_date: '2024-01-15'
            },
            {
              id: 2,
              title: '数据库系统',
              description: '关系型数据库设计、SQL查询和数据库管理系统。',
              cover_image: 'database.jpg',
              teacher_name: '李教授',
              category: '计算机科学',
              start_date: '2023-09-01',
              end_date: '2024-01-15'
            }
          ]
          this.loading = false
        }, 1000)
      } catch (error) {
        console.error('获取我的课程失败:', error)
        this.$message.error('获取我的课程失败，请稍后再试')
        this.loading = false
      }
    },
    async fetchAvailableCourses() {
      this.loading = true
      try {
        // 实际应该从API获取数据
        // const response = await this.$http.get('/student/available-courses')
        // this.availableCourses = response.data.data
        
        // 使用模拟数据
        setTimeout(() => {
          this.availableCourses = [
            {
              id: 3,
              title: '移动应用开发',
              description: '使用React Native开发跨平台移动应用。',
              cover_image: 'mobile-dev.jpg',
              teacher_name: '王教授',
              category: '软件工程',
              start_date: '2023-09-01',
              end_date: '2024-01-15'
            },
            {
              id: 4,
              title: '人工智能导论',
              description: '人工智能基础概念、算法和应用。',
              cover_image: 'ai.jpg',
              teacher_name: '赵教授',
              category: '人工智能',
              start_date: '2023-09-01',
              end_date: '2024-01-15'
            }
          ]
          this.loading = false
        }, 1000)
      } catch (error) {
        console.error('获取可选课程失败:', error)
        this.$message.error('获取可选课程失败，请稍后再试')
        this.loading = false
      }
    },
    viewCourseDetail(courseId) {
      this.$router.push(`/student/courses/${courseId}`)
    },
    async enrollCourse(course) {
      try {
        // 实际应该调用API
        // await this.$http.post(`/student/courses/${course.id}/enroll`)
        
        // 模拟API调用
        this.$message.success(`成功选修课程: ${course.title}`)
        
        // 刷新课程列表
        this.fetchMyCourses()
        this.fetchAvailableCourses()
      } catch (error) {
        console.error('选修课程失败:', error)
        this.$message.error('选修课程失败，请稍后再试')
      }
    },
    async joinCourse() {
      this.$refs.joinForm.validate(async valid => {
        if (valid) {
          this.joining = true
          try {
            // 实际应该调用API
            // await this.$http.post('/student/join-course', this.joinForm)
            
            // 模拟API调用
            setTimeout(() => {
              this.$message.success('成功加入课程')
              this.joinForm.courseCode = ''
              this.joining = false
              
              // 刷新课程列表
              this.fetchMyCourses()
            }, 1000)
          } catch (error) {
            console.error('加入课程失败:', error)
            this.$message.error('加入课程失败，请检查课程代码是否正确')
            this.joining = false
          }
        }
      })
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD')
    },
    getCourseImage(course) {
      // 实际应该返回课程封面图片的URL
      // 这里使用默认图片
      return require('@/assets/logo.svg')
    }
  },
  created() {
    this.fetchMyCourses()
  },
  watch: {
    activeTab(newVal) {
      if (newVal === 'available-courses') {
        this.fetchAvailableCourses()
      } else if (newVal === 'my-courses') {
        this.fetchMyCourses()
      }
    }
  }
}
</script>

<style scoped>
.courses-container {
  padding: 20px;
}

.search-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.search-bar .el-input {
  width: 300px;
}

.course-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.3s;
}

.course-card:hover {
  transform: translateY(-5px);
}

.course-image {
  height: 150px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-info {
  padding: 10px 0;
}

.course-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.teacher, .category {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.date {
  font-size: 12px;
  color: #909399;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.join-course-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.join-course-card {
  width: 100%;
  max-width: 500px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
  }
  
  .search-bar .el-input {
    width: 100%;
  }
}
</style>
