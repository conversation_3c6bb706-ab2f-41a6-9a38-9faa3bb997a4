const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect, authorize } = require('../middleware/auth');

// 获取所有讨论（管理员用）
router.get('/', protect, authorize('admin'), async (req, res) => {
  try {
    const { search, course_id, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let query = `
      SELECT d.*, c.title as course_title, u.username as author_name,
             (SELECT COUNT(*) FROM discussion_replies WHERE discussion_id = d.id) as reply_count
      FROM discussions d
      JOIN courses c ON d.course_id = c.id
      JOIN users u ON d.author_id = u.id
    `;
    const queryParams = [];

    // 添加搜索条件
    if (search) {
      query += ' WHERE (d.title LIKE ? OR d.content LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加课程过滤
    if (course_id) {
      if (search) {
        query += ' AND c.id = ?';
      } else {
        query += ' WHERE c.id = ?';
      }
      queryParams.push(course_id);
    }

    // 添加排序和分页
    query += ' ORDER BY d.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [rows] = await pool.query(query, queryParams);

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM discussions d
      JOIN courses c ON d.course_id = c.id
    `;
    const countParams = [];

    if (search) {
      countQuery += ' WHERE (d.title LIKE ? OR d.content LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`);
    }

    if (course_id) {
      if (search) {
        countQuery += ' AND c.id = ?';
      } else {
        countQuery += ' WHERE c.id = ?';
      }
      countParams.push(course_id);
    }

    const [countRows] = await pool.query(countQuery, countParams);
    const total = countRows[0].total;

    res.status(200).json({
      success: true,
      count: rows.length,
      total,
      data: rows
    });
  } catch (error) {
    console.error('获取讨论列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取讨论列表失败',
      error: error.message
    });
  }
});

// 获取所有讨论回复（评论）
router.get('/replies', protect, authorize('admin'), async (req, res) => {
  try {
    const { search, course_id, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let query = `
      SELECT r.*, d.title as discussion_title, d.id as discussion_id,
             c.title as course_title, c.id as course_id,
             u.username as author_name
      FROM discussion_replies r
      JOIN discussions d ON r.discussion_id = d.id
      JOIN courses c ON d.course_id = c.id
      JOIN users u ON r.author_id = u.id
    `;
    const queryParams = [];

    // 添加搜索条件
    if (search) {
      query += ' WHERE r.content LIKE ?';
      queryParams.push(`%${search}%`);
    }

    // 添加课程过滤
    if (course_id) {
      if (search) {
        query += ' AND c.id = ?';
      } else {
        query += ' WHERE c.id = ?';
      }
      queryParams.push(course_id);
    }

    // 添加排序和分页
    query += ' ORDER BY r.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [rows] = await pool.query(query, queryParams);

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM discussion_replies r
      JOIN discussions d ON r.discussion_id = d.id
      JOIN courses c ON d.course_id = c.id
    `;
    const countParams = [];

    if (search) {
      countQuery += ' WHERE r.content LIKE ?';
      countParams.push(`%${search}%`);
    }

    if (course_id) {
      if (search) {
        countQuery += ' AND c.id = ?';
      } else {
        countQuery += ' WHERE c.id = ?';
      }
      countParams.push(course_id);
    }

    const [countRows] = await pool.query(countQuery, countParams);
    const total = countRows[0].total;

    res.status(200).json({
      success: true,
      count: rows.length,
      total,
      data: rows
    });
  } catch (error) {
    console.error('获取讨论回复失败:', error);
    res.status(500).json({
      success: false,
      message: '获取讨论回复失败',
      error: error.message
    });
  }
});

// 获取单个讨论及其回复
router.get('/:id', protect, async (req, res) => {
  try {
    const discussionId = req.params.id;

    // 获取讨论信息
    const [discussionRows] = await pool.query(
      `SELECT d.*, c.title as course_title, u.username as author_name
       FROM discussions d
       JOIN courses c ON d.course_id = c.id
       JOIN users u ON d.author_id = u.id
       WHERE d.id = ?`,
      [discussionId]
    );

    if (discussionRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '讨论不存在'
      });
    }

    const discussion = discussionRows[0];

    // 获取讨论回复
    const [replyRows] = await pool.query(
      `SELECT r.*, u.username as author_name
       FROM discussion_replies r
       JOIN users u ON r.author_id = u.id
       WHERE r.discussion_id = ?
       ORDER BY r.created_at ASC`,
      [discussionId]
    );

    // 返回讨论和回复
    res.status(200).json({
      success: true,
      discussion: discussion,
      replies: replyRows
    });
  } catch (error) {
    console.error('获取讨论失败:', error);
    res.status(500).json({
      success: false,
      message: '获取讨论失败',
      error: error.message
    });
  }
});

// 删除讨论 (仅管理员和原作者)
router.delete('/:id', protect, async (req, res) => {
  try {
    const discussionId = req.params.id;

    // 验证讨论存在
    const [discussionRows] = await pool.query(
      'SELECT * FROM discussions WHERE id = ?',
      [discussionId]
    );

    if (discussionRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '讨论不存在'
      });
    }

    const discussion = discussionRows[0];

    // 验证权限 (仅管理员和原作者可以删除)
    if (req.user.role !== 'admin' && discussion.author_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您没有权限删除此讨论'
      });
    }

    // 开始事务
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // 先删除讨论的所有回复
      await connection.query('DELETE FROM discussion_replies WHERE discussion_id = ?', [discussionId]);

      // 再删除讨论
      await connection.query('DELETE FROM discussions WHERE id = ?', [discussionId]);

      // 提交事务
      await connection.commit();
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    } finally {
      // 释放连接
      connection.release();
    }

    res.status(200).json({
      success: true,
      message: '讨论删除成功'
    });
  } catch (error) {
    console.error('删除讨论失败:', error);
    res.status(500).json({
      success: false,
      message: '删除讨论失败',
      error: error.message
    });
  }
});

// 删除讨论回复 (仅管理员和原作者)
router.delete('/replies/:id', protect, async (req, res) => {
  try {
    const replyId = req.params.id;

    // 验证回复存在
    const [replyRows] = await pool.query(
      'SELECT * FROM discussion_replies WHERE id = ?',
      [replyId]
    );

    if (replyRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '回复不存在'
      });
    }

    const reply = replyRows[0];

    // 验证权限 (仅管理员和原作者可以删除)
    if (req.user.role !== 'admin' && reply.author_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您没有权限删除此回复'
      });
    }

    // 删除回复
    await pool.query('DELETE FROM discussion_replies WHERE id = ?', [replyId]);

    // 更新讨论的最后活动时间
    await pool.query(
      'UPDATE discussions SET last_activity = NOW() WHERE id = ?',
      [reply.discussion_id]
    );

    res.status(200).json({
      success: true,
      message: '回复删除成功'
    });
  } catch (error) {
    console.error('删除回复失败:', error);
    res.status(500).json({
      success: false,
      message: '删除回复失败',
      error: error.message
    });
  }
});

// 获取课程讨论列表
router.get('/course/:courseId', protect, async (req, res) => {
  try {
    const courseId = req.params.courseId;
    const { search, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // 验证课程存在
    const [courseRows] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    // 构建查询条件
    let query = `
      SELECT d.*, u.username as author_name,
             (SELECT COUNT(*) FROM discussion_replies WHERE discussion_id = d.id) as reply_count
      FROM discussions d
      JOIN users u ON d.author_id = u.id
      WHERE d.course_id = ?
    `;
    const queryParams = [courseId];

    // 添加搜索条件
    if (search) {
      query += ' AND (d.title LIKE ? OR d.content LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加排序和分页
    query += ' ORDER BY d.is_pinned DESC, d.last_activity DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [rows] = await pool.query(query, queryParams);

    // 获取总数
    const [countRows] = await pool.query(
      'SELECT COUNT(*) as total FROM discussions WHERE course_id = ?',
      [courseId]
    );
    const total = countRows[0].total;

    res.status(200).json({
      success: true,
      count: rows.length,
      total,
      data: rows
    });
  } catch (error) {
    console.error('获取课程讨论失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程讨论失败',
      error: error.message
    });
  }
});

// 创建讨论 (学生和教师)
router.post('/', protect, async (req, res) => {
  try {
    const { title, content, course_id, is_pinned } = req.body;

    // 验证必要字段
    if (!title || !content || !course_id) {
      return res.status(400).json({
        success: false,
        message: '请提供标题、内容和课程ID'
      });
    }

    // 验证课程存在
    const [courseRows] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [course_id]
    );

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    // 只有教师可以创建置顶讨论
    const isPinned = req.user.role === 'teacher' && is_pinned ? 1 : 0;

    // 插入讨论
    const [result] = await pool.query(
      `INSERT INTO discussions (
        title, content, course_id, author_id, is_pinned,
        created_at, last_activity
      ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
      [title, content, course_id, req.user.id, isPinned]
    );

    // 获取新创建的讨论
    const [discussionRows] = await pool.query(
      `SELECT d.*, u.username as author_name
       FROM discussions d
       JOIN users u ON d.author_id = u.id
       WHERE d.id = ?`,
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '讨论创建成功',
      data: discussionRows[0]
    });
  } catch (error) {
    console.error('创建讨论失败:', error);
    res.status(500).json({
      success: false,
      message: '创建讨论失败',
      error: error.message
    });
  }
});

// 添加讨论回复
router.post('/:id/replies', protect, async (req, res) => {
  try {
    const discussionId = req.params.id;
    const { content } = req.body;

    // 验证必要字段
    if (!content) {
      return res.status(400).json({
        success: false,
        message: '请提供回复内容'
      });
    }

    // 验证讨论存在
    const [discussionRows] = await pool.query(
      'SELECT * FROM discussions WHERE id = ?',
      [discussionId]
    );

    if (discussionRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '讨论不存在'
      });
    }

    // 插入回复
    const [result] = await pool.query(
      `INSERT INTO discussion_replies (
        discussion_id, author_id, content, created_at
      ) VALUES (?, ?, ?, NOW())`,
      [discussionId, req.user.id, content]
    );

    // 更新讨论的最后活动时间
    await pool.query(
      'UPDATE discussions SET last_activity = NOW() WHERE id = ?',
      [discussionId]
    );

    // 获取新创建的回复
    const [replyRows] = await pool.query(
      `SELECT r.*, u.username as author_name
       FROM discussion_replies r
       JOIN users u ON r.author_id = u.id
       WHERE r.id = ?`,
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '回复添加成功',
      data: replyRows[0]
    });
  } catch (error) {
    console.error('添加回复失败:', error);
    res.status(500).json({
      success: false,
      message: '添加回复失败',
      error: error.message
    });
  }
});

module.exports = router;
