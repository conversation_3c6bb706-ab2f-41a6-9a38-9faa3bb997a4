<template>
  <div class="resource-management">
    <div class="page-header">
      <h1>课程资源管理</h1>
      <el-button type="primary" @click="openResourceDialog()">
        <i class="el-icon-plus"></i> 添加资源
      </el-button>
    </div>

    <div class="filter-bar">
      <el-select v-model="courseId" placeholder="选择课程" @change="fetchResources">
        <el-option
          v-for="course in courses"
          :key="course.id"
          :label="course.title"
          :value="course.id">
        </el-option>
      </el-select>

      <el-select v-model="typeFilter" placeholder="资源类型" clearable @change="fetchResources">
        <el-option label="文档" value="document"></el-option>
        <el-option label="视频" value="video"></el-option>
        <el-option label="图片" value="image"></el-option>
        <el-option label="链接" value="link"></el-option>
        <el-option label="其他" value="other"></el-option>
      </el-select>

      <el-input
        placeholder="搜索资源"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchResources"
        @keyup.enter.native="fetchResources">
      </el-input>

      <el-button type="primary" @click="fetchResources">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="!courseId" class="empty-data">
      <el-empty description="请选择一个课程"></el-empty>
    </div>

    <div v-else-if="resources.length === 0" class="empty-data">
      <el-empty description="暂无资源数据"></el-empty>
    </div>

    <el-table
      v-else
      :data="resources"
      style="width: 100%"
      border>
      <el-table-column
        prop="title"
        label="资源标题"
        min-width="200">
      </el-table-column>

      <el-table-column
        prop="file_type"
        label="类型"
        width="100">
        <template slot-scope="scope">
          <el-tag :type="getFileTypeTag(scope.row.file_type)">
            {{ getFileTypeText(scope.row.file_type) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="description"
        label="描述"
        min-width="250">
        <template slot-scope="scope">
          <span v-if="scope.row.description">{{ scope.row.description }}</span>
          <span v-else class="text-muted">无描述</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="is_public"
        label="可见性"
        width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.is_public ? 'success' : 'info'">
            {{ scope.row.is_public ? '公开' : '私有' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="download_count"
        label="下载次数"
        width="100">
      </el-table-column>

      <el-table-column
        prop="created_at"
        label="创建时间"
        width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="previewResource(scope.row)">
            预览
          </el-button>
          <el-button
            size="mini"
            type="warning"
            @click="openResourceDialog(scope.row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="deleteResource(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container" v-if="resources.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>

    <!-- 资源表单对话框 -->
    <el-dialog
      :title="resourceForm.id ? '编辑资源' : '添加资源'"
      :visible.sync="dialogVisible"
      width="600px"
      @close="resetForm">
      <el-form :model="resourceForm" :rules="rules" ref="resourceForm" label-width="100px">
        <el-form-item label="课程" prop="course_id" v-if="!resourceForm.id">
          <el-select v-model="resourceForm.course_id" placeholder="选择课程" style="width: 100%">
            <el-option
              v-for="course in courses"
              :key="course.id"
              :label="course.title"
              :value="course.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="资源标题" prop="title">
          <el-input v-model="resourceForm.title" placeholder="请输入资源标题"></el-input>
        </el-form-item>

        <el-form-item label="资源描述" prop="description">
          <el-input
            type="textarea"
            v-model="resourceForm.description"
            placeholder="请输入资源描述"
            :rows="3">
          </el-input>
        </el-form-item>

        <el-form-item label="资源类型" prop="file_type" v-if="!resourceForm.id">
          <el-select v-model="resourceForm.file_type" placeholder="选择资源类型" style="width: 100%">
            <el-option label="文档" value="document"></el-option>
            <el-option label="视频" value="video"></el-option>
            <el-option label="图片" value="image"></el-option>
            <el-option label="链接" value="link"></el-option>
            <el-option label="其他" value="other"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="资源文件" prop="file" v-if="!resourceForm.id">
          <el-upload
            class="upload-demo"
            :action="`${$http.defaults.baseURL}/api/resources/upload`"
            :headers="uploadHeaders"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :file-list="fileList">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持各种文档、图片和视频格式，单个文件不超过50MB</div>
          </el-upload>
        </el-form-item>

        <el-form-item label="可见性" prop="is_public">
          <el-switch
            v-model="resourceForm.is_public"
            active-text="公开"
            inactive-text="私有">
          </el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveResource" :loading="saving">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 资源预览对话框 -->
    <el-dialog
      title="资源预览"
      :visible.sync="previewVisible"
      width="800px">
      <div v-if="currentPreviewResource">
        <h3>{{ currentPreviewResource.title }}</h3>
        <p v-if="currentPreviewResource.description">{{ currentPreviewResource.description }}</p>

        <div class="resource-preview-content">
          <!-- 根据资源类型显示不同的预览 -->
          <div v-if="currentPreviewResource.file_type === 'image'" class="image-preview">
            <img :src="`${$http.defaults.baseURL}/${currentPreviewResource.file_path}`" alt="图片预览">
          </div>

          <div v-else-if="currentPreviewResource.file_type === 'video'" class="video-preview">
            <video controls :src="`${$http.defaults.baseURL}/${currentPreviewResource.file_path}`"></video>
          </div>

          <div v-else-if="currentPreviewResource.file_type === 'document'" class="document-preview">
            <iframe :src="`${$http.defaults.baseURL}/${currentPreviewResource.file_path}`" frameborder="0"></iframe>
          </div>

          <div v-else class="file-info">
            <p>文件类型: {{ getFileTypeText(currentPreviewResource.file_type) }}</p>
            <p>文件大小: {{ formatFileSize(currentPreviewResource.file_size) }}</p>
            <p>上传时间: {{ formatDate(currentPreviewResource.created_at) }}</p>
            <p>下载次数: {{ currentPreviewResource.download_count }}</p>
          </div>
        </div>

        <div class="resource-actions">
          <el-button type="primary" @click="downloadResource(currentPreviewResource)">
            <i class="el-icon-download"></i> 下载资源
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ResourceManagement',
  data() {
    return {
      courses: [],
      resources: [],
      loading: false,
      saving: false,
      courseId: '',
      searchQuery: '',
      typeFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      previewVisible: false,
      currentPreviewResource: null,
      resourceForm: {
        id: '',
        course_id: '',
        title: '',
        description: '',
        file_type: 'document',
        file_path: '',
        file_size: 0,
        is_public: true
      },
      fileList: [],
      rules: {
        course_id: [
          { required: true, message: '请选择课程', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入资源标题', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        file_type: [
          { required: true, message: '请选择资源类型', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    uploadHeaders() {
      return {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    }
  },
  methods: {
    async fetchCourses() {
      try {
        const response = await this.$http.get('/api/courses')
        if (response.data.success) {
          this.courses = response.data.data

          // 如果有课程，默认选择第一个
          if (this.courses.length > 0 && !this.courseId) {
            this.courseId = this.courses[0].id
            this.fetchResources()
          }
        }
      } catch (error) {
        console.error('获取课程失败:', error)
        this.$message.error('获取课程列表失败，请稍后再试')
      }
    },
    async fetchResources() {
      if (!this.courseId) return

      this.loading = true
      try {
        const response = await this.$http.get(`/api/resources/course/${this.courseId}`, {
          params: {
            search: this.searchQuery,
            type: this.typeFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.resources = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取资源失败')
        }
      } catch (error) {
        console.error('获取资源失败:', error)
        this.$message.error('获取资源列表失败，请稍后再试')
        this.resources = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchResources()
    },
    openResourceDialog(resource = null) {
      if (resource) {
        // 编辑现有资源
        this.resourceForm = {
          id: resource.id,
          course_id: resource.course_id,
          title: resource.title,
          description: resource.description,
          file_type: resource.file_type,
          file_path: resource.file_path,
          file_size: resource.file_size,
          is_public: resource.is_public
        }
      } else {
        // 添加新资源
        this.resourceForm = {
          id: '',
          course_id: this.courseId || '',
          title: '',
          description: '',
          file_type: 'document',
          file_path: '',
          file_size: 0,
          is_public: true
        }
        this.fileList = []
      }

      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.resourceForm.clearValidate()
      })
    },
    resetForm() {
      this.resourceForm = {
        id: '',
        course_id: '',
        title: '',
        description: '',
        file_type: 'document',
        file_path: '',
        file_size: 0,
        is_public: true
      }
      this.fileList = []
    },
    async saveResource() {
      this.$refs.resourceForm.validate(async valid => {
        if (valid) {
          // 如果是新资源，检查是否上传了文件
          if (!this.resourceForm.id && !this.resourceForm.file_path) {
            this.$message.warning('请上传资源文件')
            return
          }

          this.saving = true

          try {
            let response

            if (this.resourceForm.id) {
              // 更新现有资源
              response = await this.$http.put(`/api/resources/${this.resourceForm.id}`, {
                title: this.resourceForm.title,
                description: this.resourceForm.description,
                is_public: this.resourceForm.is_public
              })
            } else {
              // 创建新资源
              response = await this.$http.post('/api/resources', this.resourceForm)
            }

            if (response.data.success) {
              this.$message.success(this.resourceForm.id ? '资源更新成功' : '资源创建成功')
              this.dialogVisible = false
              this.fetchResources()
            } else {
              throw new Error(response.data.message || '保存资源失败')
            }
          } catch (error) {
            console.error('保存资源失败:', error)
            this.$message.error(error.message || '保存资源失败，请稍后再试')
          } finally {
            this.saving = false
          }
        }
      })
    },
    async deleteResource(resource) {
      try {
        await this.$confirm(`确定要删除资源 "${resource.title}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await this.$http.delete(`/api/resources/${resource.id}`)

        if (response.data.success) {
          this.$message.success('资源删除成功')
          this.fetchResources()
        } else {
          throw new Error(response.data.message || '删除资源失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除资源失败:', error)
          this.$message.error(error.message || '删除资源失败，请稍后再试')
        }
      }
    },
    previewResource(resource) {
      this.currentPreviewResource = resource
      this.previewVisible = true
    },
    async downloadResource(resource) {
      try {
        window.open(`${this.$http.defaults.baseURL}/api/resources/${resource.id}/download`, '_blank')
      } catch (error) {
        console.error('下载资源失败:', error)
        this.$message.error('下载资源失败，请稍后再试')
      }
    },
    // 文件上传相关方法
    handlePreview(file) {
      console.log('预览文件:', file)
    },
    handleRemove(file, fileList) {
      console.log('移除文件:', file, fileList)
      this.resourceForm.file_path = ''
      this.resourceForm.file_size = 0
    },
    handleUploadSuccess(response, file, fileList) {
      if (response.success) {
        this.$message.success('文件上传成功')
        this.resourceForm.file_path = response.data.path
        this.resourceForm.file_size = response.data.size

        // 根据文件类型自动设置资源类型
        const mimeType = response.data.mimetype || file.type
        if (mimeType.startsWith('image/')) {
          this.resourceForm.file_type = 'image'
        } else if (mimeType.startsWith('video/')) {
          this.resourceForm.file_type = 'video'
        } else if (mimeType === 'application/pdf' || mimeType.includes('document')) {
          this.resourceForm.file_type = 'document'
        } else {
          this.resourceForm.file_type = 'other'
        }

        // 清除文件字段的验证错误
        this.$nextTick(() => {
          if (this.$refs.resourceForm) {
            this.$refs.resourceForm.clearValidate(['file'])
          }
        })
      } else {
        this.$message.error(response.message || '文件上传失败')
      }
    },
    handleUploadError(err, file, fileList) {
      console.error('上传失败:', err)
      this.$message.error('文件上传失败，请稍后再试')
    },
    beforeUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50

      if (!isLt50M) {
        this.$message.error('文件大小不能超过 50MB!')
      }

      return isLt50M
    },
    // 工具方法
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else if (size < 1024 * 1024 * 1024) {
        return (size / 1024 / 1024).toFixed(2) + ' MB'
      } else {
        return (size / 1024 / 1024 / 1024).toFixed(2) + ' GB'
      }
    },
    getFileTypeText(type) {
      const types = {
        document: '文档',
        video: '视频',
        image: '图片',
        link: '链接',
        other: '其他'
      }
      return types[type] || '未知'
    },
    getFileTypeTag(type) {
      const tags = {
        document: 'primary',
        video: 'success',
        image: 'warning',
        link: 'info',
        other: ''
      }
      return tags[type] || ''
    }
  },
  created() {
    this.fetchCourses()
  }
}
</script>

<style scoped>
.resource-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-select {
  width: 180px;
}

.filter-bar .el-input {
  width: 300px;
}

.loading-container, .empty-data {
  padding: 20px;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.text-muted {
  color: #909399;
}

.resource-preview-content {
  margin: 20px 0;
  max-height: 400px;
  overflow: auto;
}

.image-preview img {
  max-width: 100%;
  max-height: 400px;
}

.video-preview video {
  width: 100%;
  max-height: 400px;
}

.document-preview iframe {
  width: 100%;
  height: 400px;
}

.file-info {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.resource-actions {
  margin-top: 20px;
  text-align: center;
}
</style>
