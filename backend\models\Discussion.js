const mongoose = require('mongoose');

// 回复模式
const ReplySchema = new mongoose.Schema({
  // 回复内容
  content: {
    type: String,
    required: [true, '请提供回复内容']
  },
  // 回复者
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定回复者']
  },
  // 回复时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  // 点赞数
  likes: {
    type: Number,
    default: 0
  },
  // 点赞用户
  likedBy: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }]
});

// 讨论模式
const DiscussionSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, '请提供讨论标题'],
    trim: true,
    maxlength: [100, '讨论标题不能超过100个字符']
  },
  content: {
    type: String,
    required: [true, '请提供讨论内容']
  },
  // 所属课程
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, '请指定所属课程']
  },
  // 发起者
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定发起者']
  },
  // 回复列表
  replies: [ReplySchema],
  // 是否置顶
  isPinned: {
    type: Boolean,
    default: false
  },
  // 标签
  tags: [String],
  // 浏览次数
  viewCount: {
    type: Number,
    default: 0
  },
  // 点赞数
  likes: {
    type: Number,
    default: 0
  },
  // 点赞用户
  likedBy: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  // 最后更新时间（包括回复）
  lastActivity: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 更新最后活动时间
DiscussionSchema.pre('save', function(next) {
  // 如果有新回复，更新最后活动时间
  if (this.isModified('replies')) {
    this.lastActivity = Date.now();
  }
  next();
});

// 虚拟字段：回复数量
DiscussionSchema.virtual('replyCount').get(function() {
  return this.replies ? this.replies.length : 0;
});

module.exports = mongoose.model('Discussion', DiscussionSchema);
