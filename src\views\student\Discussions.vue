<template>
  <div class="discussions">
    <div class="page-header">
      <h1>课程讨论</h1>
      <el-button type="primary" @click="openDiscussionDialog()" v-if="selectedCourse">
        <i class="el-icon-plus"></i> 发起讨论
      </el-button>
    </div>

    <!-- 课程选择 -->
    <div class="course-selector">
      <el-select v-model="selectedCourse" placeholder="选择课程" @change="fetchDiscussions" style="width: 300px;">
        <el-option
          v-for="course in enrolledCourses"
          :key="course.id"
          :label="course.title"
          :value="course.id">
        </el-option>
      </el-select>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="!selectedCourse" class="empty-data">
      <el-empty description="请选择一个课程查看讨论"></el-empty>
    </div>

    <div v-else-if="discussions.length === 0" class="empty-data">
      <el-empty description="暂无讨论，快来发起第一个讨论吧！"></el-empty>
    </div>

    <div v-else class="discussions-list">
      <el-card
        v-for="discussion in discussions"
        :key="discussion.id"
        class="discussion-card"
        shadow="hover"
        @click.native="viewDiscussion(discussion)">
        <div class="discussion-header">
          <div class="discussion-title">
            <el-tag v-if="discussion.is_pinned" type="warning" size="mini">置顶</el-tag>
            <el-tag v-if="discussion.is_locked" type="info" size="mini">已锁定</el-tag>
            <h3>{{ discussion.title }}</h3>
          </div>
          <div class="discussion-meta">
            <span class="author">{{ discussion.author_name }}</span>
            <span class="time">{{ formatDate(discussion.created_at) }}</span>
          </div>
        </div>
        <div class="discussion-content">
          {{ discussion.content.substring(0, 200) }}{{ discussion.content.length > 200 ? '...' : '' }}
        </div>
        <div class="discussion-stats">
          <span><i class="el-icon-view"></i> {{ discussion.view_count || 0 }} 浏览</span>
          <span><i class="el-icon-chat-line-round"></i> {{ discussion.reply_count || 0 }} 回复</span>
          <span v-if="discussion.latest_reply">
            最后回复：{{ formatDate(discussion.latest_reply.created_at) }}
          </span>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="discussions.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>

    <!-- 发起讨论对话框 -->
    <el-dialog
      title="发起讨论"
      :visible.sync="dialogVisible"
      width="700px"
      @close="resetForm">
      <el-form :model="discussionForm" :rules="rules" ref="discussionForm" label-width="100px">
        <el-form-item label="讨论标题" prop="title">
          <el-input v-model="discussionForm.title" placeholder="请输入讨论标题"></el-input>
        </el-form-item>
        
        <el-form-item label="讨论内容" prop="content">
          <el-input
            type="textarea"
            v-model="discussionForm.content"
            placeholder="请输入讨论内容"
            :rows="6">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="createDiscussion" :loading="saving">发 布</el-button>
      </span>
    </el-dialog>

    <!-- 讨论详情对话框 -->
    <el-dialog
      title="讨论详情"
      :visible.sync="viewDialogVisible"
      width="800px"
      top="5vh">
      <div v-if="currentDiscussion" class="discussion-detail">
        <div class="discussion-main">
          <div class="discussion-header">
            <h2>{{ currentDiscussion.title }}</h2>
            <div class="discussion-meta">
              <el-tag v-if="currentDiscussion.is_pinned" type="warning" size="mini">置顶</el-tag>
              <el-tag v-if="currentDiscussion.is_locked" type="info" size="mini">已锁定</el-tag>
              <span class="author">发起人：{{ currentDiscussion.author_name }}</span>
              <span class="time">{{ formatDate(currentDiscussion.created_at) }}</span>
            </div>
          </div>
          <div class="discussion-content">
            {{ currentDiscussion.content }}
          </div>
        </div>

        <!-- 回复列表 -->
        <div class="replies-section">
          <h3>回复 ({{ currentDiscussion.replies ? currentDiscussion.replies.length : 0 }})</h3>
          
          <div v-if="currentDiscussion.replies && currentDiscussion.replies.length > 0" class="replies-list">
            <div
              v-for="reply in currentDiscussion.replies"
              :key="reply.id"
              class="reply-item">
              <div class="reply-header">
                <span class="reply-author">{{ reply.author_name }}</span>
                <span class="reply-time">{{ formatDate(reply.created_at) }}</span>
              </div>
              <div class="reply-content">{{ reply.content }}</div>
            </div>
          </div>

          <!-- 回复表单 -->
          <div v-if="!currentDiscussion.is_locked" class="reply-form">
            <el-form :model="replyForm" ref="replyForm">
              <el-form-item>
                <el-input
                  type="textarea"
                  v-model="replyForm.content"
                  placeholder="写下你的回复..."
                  :rows="3">
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitReply" :loading="replying">回复</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <div v-else class="locked-notice">
            <el-alert title="此讨论已被锁定，无法回复" type="info" :closable="false"></el-alert>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'StudentDiscussions',
  data() {
    return {
      discussions: [],
      enrolledCourses: [],
      selectedCourse: '',
      loading: false,
      saving: false,
      replying: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      viewDialogVisible: false,
      currentDiscussion: null,
      discussionForm: {
        title: '',
        content: ''
      },
      replyForm: {
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入讨论标题', trigger: 'blur' },
          { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入讨论内容', trigger: 'blur' },
          { min: 10, message: '内容至少10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async fetchEnrolledCourses() {
      try {
        const response = await this.$http.get('/api/courses/enrolled')
        if (response.data.success) {
          this.enrolledCourses = response.data.data
          if (this.enrolledCourses.length > 0 && !this.selectedCourse) {
            this.selectedCourse = this.enrolledCourses[0].id
            this.fetchDiscussions()
          }
        }
      } catch (error) {
        console.error('获取已选课程失败:', error)
        this.$message.error('获取课程列表失败')
      }
    },
    async fetchDiscussions() {
      if (!this.selectedCourse) return
      
      this.loading = true
      try {
        const response = await this.$http.get('/api/discussions', {
          params: {
            course_id: this.selectedCourse,
            page: this.currentPage,
            limit: this.pageSize
          }
        })
        
        if (response.data.success) {
          this.discussions = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取讨论失败')
        }
      } catch (error) {
        console.error('获取讨论失败:', error)
        this.$message.error('获取讨论列表失败')
        this.discussions = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    async viewDiscussion(discussion) {
      try {
        const response = await this.$http.get(`/api/discussions/${discussion.id}`)
        if (response.data.success) {
          this.currentDiscussion = response.data.data
          this.viewDialogVisible = true
        } else {
          throw new Error(response.data.message || '获取讨论详情失败')
        }
      } catch (error) {
        console.error('获取讨论详情失败:', error)
        this.$message.error('获取讨论详情失败')
      }
    },
    openDiscussionDialog() {
      this.discussionForm = {
        title: '',
        content: ''
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.discussionForm.clearValidate()
      })
    },
    resetForm() {
      this.discussionForm = {
        title: '',
        content: ''
      }
    },
    async createDiscussion() {
      this.$refs.discussionForm.validate(async valid => {
        if (valid) {
          this.saving = true
          try {
            const response = await this.$http.post('/api/discussions', {
              ...this.discussionForm,
              course_id: this.selectedCourse
            })
            
            if (response.data.success) {
              this.$message.success('讨论发布成功')
              this.dialogVisible = false
              this.fetchDiscussions()
            } else {
              throw new Error(response.data.message || '发布讨论失败')
            }
          } catch (error) {
            console.error('发布讨论失败:', error)
            this.$message.error(error.message || '发布讨论失败')
          } finally {
            this.saving = false
          }
        }
      })
    },
    async submitReply() {
      if (!this.replyForm.content.trim()) {
        this.$message.warning('请输入回复内容')
        return
      }
      
      this.replying = true
      try {
        const response = await this.$http.post('/api/discussions/replies', {
          discussion_id: this.currentDiscussion.id,
          content: this.replyForm.content
        })
        
        if (response.data.success) {
          this.$message.success('回复成功')
          this.replyForm.content = ''
          // 重新获取讨论详情
          this.viewDiscussion(this.currentDiscussion)
        } else {
          throw new Error(response.data.message || '回复失败')
        }
      } catch (error) {
        console.error('回复失败:', error)
        this.$message.error(error.message || '回复失败')
      } finally {
        this.replying = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchDiscussions()
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    }
  },
  created() {
    this.fetchEnrolledCourses()
  }
}
</script>

<style scoped>
.discussions {
  padding: 20px;
  color: white;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  color: white;
  margin: 0;
}

.course-selector {
  margin-bottom: 20px;
}

.loading-container, .empty-data {
  padding: 40px;
  text-align: center;
}

.discussions-list {
  margin-bottom: 20px;
}

.discussion-card {
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.discussion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.discussion-title {
  flex: 1;
}

.discussion-title h3 {
  margin: 5px 0;
  color: #333;
}

.discussion-meta {
  text-align: right;
  font-size: 12px;
  color: #999;
}

.discussion-meta .author {
  font-weight: bold;
  color: #666;
}

.discussion-content {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.discussion-stats {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #999;
  border-top: 1px solid #f0f0f0;
  padding-top: 10px;
}

.discussion-stats i {
  margin-right: 4px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.discussion-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.discussion-main {
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
}

.discussion-main .discussion-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.discussion-main .discussion-content {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.replies-section h3 {
  color: #333;
  margin-bottom: 15px;
}

.replies-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.reply-item {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.reply-author {
  font-weight: bold;
  color: #409EFF;
}

.reply-time {
  color: #999;
}

.reply-content {
  color: #333;
  line-height: 1.5;
}

.reply-form {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.locked-notice {
  margin-top: 20px;
}
</style>
