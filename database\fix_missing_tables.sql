-- 修复缺失的表
USE elearning;

-- 考勤表
CREATE TABLE IF NOT EXISTS attendances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    attendance_code VARCHAR(6),
    creator_id BIGINT UNSIGNED NOT NULL,
    status ENUM('active', 'closed') DEFAULT 'active',
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    location_radius INT DEFAULT 100, -- 默认100米范围
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREI<PERSON><PERSON> KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_date (date),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 学生考勤记录表
CREATE TABLE IF NOT EXISTS student_attendances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    attendance_id BIGINT UNSIGNED NOT NULL,
    student_id BIGINT UNSIGNED NOT NULL,
    status ENUM('present', 'late', 'absent', 'leave') DEFAULT 'absent',
    check_in_time TIMESTAMP NULL,
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (attendance_id) REFERENCES attendances(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_attendance_student (attendance_id, student_id),
    INDEX idx_attendance (attendance_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 创建触发器：生成考勤码
DELIMITER //
CREATE TRIGGER IF NOT EXISTS generate_attendance_code
BEFORE INSERT ON attendances
FOR EACH ROW
BEGIN
    IF NEW.attendance_code IS NULL THEN
        SET NEW.attendance_code = LPAD(
            FLOOR(RAND() * 1000000), 6, '0'
        );
    END IF;
END //
DELIMITER ;
