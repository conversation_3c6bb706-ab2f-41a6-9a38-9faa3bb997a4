const { pool } = require('./config/db');

async function showTables() {
  try {
    const [rows] = await pool.query('SHOW TABLES');
    console.log('数据库表:');
    rows.forEach(row => console.log(Object.values(row)[0]));
    
    // 查询users表结构
    const [userColumns] = await pool.query('DESCRIBE users');
    console.log('\nusers表结构:');
    userColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
    });
    
    // 查询courses表结构
    const [courseColumns] = await pool.query('DESCRIBE courses');
    console.log('\ncourses表结构:');
    courseColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
    });
    
    // 查询announcements表结构
    const [announcementColumns] = await pool.query('DESCRIBE announcements');
    console.log('\nannouncements表结构:');
    announcementColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('查询失败:', error);
    process.exit(1);
  }
}

showTables();
