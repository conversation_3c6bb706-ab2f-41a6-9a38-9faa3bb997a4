<template>
  <div class="comment-management">
    <div class="page-header">
      <h1>评论管理</h1>
    </div>

    <div class="filter-bar">
      <el-input
        placeholder="搜索评论内容"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchComments"
        @keyup.enter.native="fetchComments">
      </el-input>
      <el-select v-model="courseFilter" placeholder="选择课程" clearable @change="fetchComments">
        <el-option
          v-for="course in courses"
          :key="course.id"
          :label="course.title"
          :value="course.id">
        </el-option>
      </el-select>
      <el-button type="primary" @click="fetchComments">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="comments.length === 0" class="empty-data">
      <el-empty description="暂无评论数据"></el-empty>
    </div>

    <el-table
      v-else
      :data="comments"
      style="width: 100%"
      border>
      <el-table-column
        prop="content"
        label="评论内容"
        min-width="300">
      </el-table-column>
      <el-table-column
        prop="discussion_title"
        label="所属讨论"
        min-width="200">
      </el-table-column>
      <el-table-column
        prop="course_title"
        label="所属课程"
        width="180">
      </el-table-column>
      <el-table-column
        prop="author_name"
        label="评论者"
        width="120">
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="评论时间"
        width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            @click="deleteComment(scope.row)">
            删除
          </el-button>
          <el-button
            size="mini"
            type="warning"
            @click="viewDiscussion(scope.row)">
            查看讨论
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container" v-if="comments.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>

    <!-- 查看讨论对话框 -->
    <el-dialog
      title="讨论详情"
      :visible.sync="discussionDialogVisible"
      width="60%">
      <div v-if="currentDiscussion" class="discussion-detail">
        <h2>{{ currentDiscussion.title }}</h2>
        <div class="discussion-meta">
          <span>作者: {{ currentDiscussion.author_name }}</span>
          <span>发布时间: {{ formatDate(currentDiscussion.created_at) }}</span>
          <span>课程: {{ currentDiscussion.course_title }}</span>
        </div>
        <div class="discussion-content">
          {{ currentDiscussion.content }}
        </div>

        <h3>回复列表</h3>
        <el-divider></el-divider>

        <div v-if="currentDiscussion.replies && currentDiscussion.replies.length > 0" class="replies-list">
          <div v-for="(reply, index) in currentDiscussion.replies" :key="index" class="reply-item">
            <div class="reply-header">
              <span class="reply-author">{{ reply.author_name }}</span>
              <span class="reply-time">{{ formatDate(reply.created_at) }}</span>
            </div>
            <div class="reply-content">
              {{ reply.content }}
            </div>
            <div class="reply-actions">
              <el-button
                size="mini"
                type="danger"
                @click="deleteReply(reply, index)">
                删除回复
              </el-button>
            </div>
          </div>
        </div>
        <div v-else class="no-replies">
          暂无回复
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CommentManagement',
  data() {
    return {
      comments: [],
      courses: [],
      loading: false,
      searchQuery: '',
      courseFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      discussionDialogVisible: false,
      currentDiscussion: null
    }
  },
  methods: {
    async fetchComments() {
      this.loading = true
      try {
        // 从API获取评论数据
        const response = await this.$http.get('/api/discussions/replies', {
          params: {
            search: this.searchQuery,
            course_id: this.courseFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.comments = response.data.data || []
          this.total = response.data.total || 0
        } else {
          throw new Error('获取评论失败')
        }
      } catch (error) {
        console.error('获取评论失败:', error)
        this.$message.error('获取评论列表失败，请稍后再试')
        this.comments = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    async fetchCourses() {
      try {
        // 从API获取课程数据
        const response = await this.$http.get('/api/courses')

        if (response.data.success) {
          this.courses = response.data.data || []
        } else {
          throw new Error('获取课程失败')
        }
      } catch (error) {
        console.error('获取课程失败:', error)
        this.$message.error('获取课程列表失败')
        this.courses = []
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchComments()
    },
    async deleteComment(comment) {
      try {
        await this.$confirm('确定要删除这条评论吗？此操作不可逆。', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用API删除评论
        const response = await this.$http.delete(`/api/discussions/replies/${comment.id}`)

        if (response.data.success) {
          // 更新本地数据
          this.comments = this.comments.filter(c => c.id !== comment.id)

          if (this.comments.length === 0 && this.currentPage > 1) {
            this.currentPage--
            this.fetchComments()
          }

          this.$message.success('评论删除成功')
        } else {
          throw new Error(response.data.message || '删除评论失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除评论失败:', error)
          this.$message.error(error.message || '删除评论失败，请稍后再试')
        }
      }
    },
    async viewDiscussion(comment) {
      try {
        // 从API获取讨论详情
        const response = await this.$http.get(`/api/discussions/${comment.discussion_id}`)

        if (response.data.success) {
          // 使用API返回的讨论数据
          const discussionData = response.data.discussion

          // 确保回复列表包含当前评论
          if (discussionData && discussionData.replies) {
            // 检查当前评论是否已经在回复列表中
            const replyExists = discussionData.replies.some(r => r.id === comment.id)

            // 如果不在列表中，添加当前评论到回复列表
            if (!replyExists) {
              discussionData.replies.push({
                id: comment.id,
                content: comment.content,
                author_name: comment.author_name,
                created_at: comment.created_at
              })
            }
          }

          this.currentDiscussion = discussionData
          this.discussionDialogVisible = true
        }
      } catch (error) {
        console.error('获取讨论详情失败:', error)
        this.$message.error(error.message || '获取讨论详情失败，请稍后再试')
      }
    },
    async deleteReply(reply, index) {
      try {
        await this.$confirm('确定要删除这条回复吗？此操作不可逆。', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用API删除回复
        const response = await this.$http.delete(`/api/discussions/replies/${reply.id}`)

        if (response.data.success) {
          // 更新当前讨论中的回复列表
          this.currentDiscussion.replies.splice(index, 1)

          // 如果这个回复也在评论列表中，也要更新
          const commentIndex = this.comments.findIndex(c => c.id === reply.id)
          if (commentIndex !== -1) {
            this.comments.splice(commentIndex, 1)
          }

          this.$message.success('回复删除成功')
        } else {
          throw new Error(response.data.message || '删除回复失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除回复失败:', error)
          this.$message.error(error.message || '删除回复失败，请稍后再试')
        }
      }
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    }
  },
  created() {
    this.fetchComments()
    this.fetchCourses()
  }
}
</script>

<style scoped>
.comment-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.loading-container, .empty-data {
  padding: 20px;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.discussion-detail {
  padding: 10px;
}

.discussion-meta {
  display: flex;
  gap: 20px;
  color: #666;
  margin-bottom: 15px;
}

.discussion-content {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 20px;
}

.replies-list {
  margin-top: 15px;
}

.reply-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.reply-author {
  font-weight: bold;
}

.reply-time {
  color: #999;
}

.reply-content {
  padding: 5px 0;
}

.reply-actions {
  margin-top: 5px;
  text-align: right;
}

.no-replies {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style>
