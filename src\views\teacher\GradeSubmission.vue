<template>
  <div class="grade-submission">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else>
      <!-- 提交信息 -->
      <div class="submission-header">
        <div class="submission-info">
          <h2>{{ submission.test_title }}</h2>
          <div class="student-info">
            <span class="student-name">学生: {{ submission.student_name }}</span>
            <el-tag :type="getSubmissionStatusType(submission.status)">
              {{ getSubmissionStatusText(submission.status) }}
            </el-tag>
          </div>
          <div class="submission-meta">
            <span><i class="el-icon-time"></i> 提交时间: {{ formatDateTime(submission.submit_time) }}</span>
            <span v-if="submission.is_fully_graded">
              <i class="el-icon-medal"></i> 得分: {{ submission.total_score }} / {{ totalPossibleScore }}
            </span>
            <span v-else>
              <i class="el-icon-warning"></i> 未完全评分
            </span>
          </div>
        </div>
        <div class="submission-actions">
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </div>
      </div>
      
      <!-- 答案列表 -->
      <div class="answers-section">
        <h3>学生答案</h3>
        
        <div v-for="(answer, index) in submission.answers" :key="answer.id" class="answer-card">
          <div class="question-header">
            <div class="question-number">问题 {{ index + 1 }}</div>
            <div class="question-type">
              <el-tag :type="getQuestionTypeTag(answer.question_type)">
                {{ getQuestionTypeText(answer.question_type) }}
              </el-tag>
            </div>
            <div class="question-score">
              <span v-if="answer.is_graded">{{ answer.score }} / {{ getQuestionScore(answer) }}</span>
              <el-tag v-else type="warning" size="mini">未评分</el-tag>
            </div>
          </div>
          
          <div class="question-content" v-html="answer.question_content"></div>
          
          <div class="student-answer">
            <div class="answer-label">学生答案:</div>
            <div class="answer-content">
              <!-- 单选题 -->
              <template v-if="answer.question_type === 'single'">
                <div class="option-answer">
                  {{ getOptionLabel(answer, getStudentAnswer(answer)) }}
                </div>
              </template>
              
              <!-- 多选题 -->
              <template v-else-if="answer.question_type === 'multiple'">
                <div v-for="option in getStudentAnswer(answer)" :key="option" class="option-answer">
                  {{ getOptionLabel(answer, option) }}
                </div>
              </template>
              
              <!-- 判断题 -->
              <template v-else-if="answer.question_type === 'truefalse'">
                <div class="option-answer">
                  {{ getStudentAnswer(answer) === 'true' ? '正确' : '错误' }}
                </div>
              </template>
              
              <!-- 简答题 -->
              <template v-else-if="answer.question_type === 'essay'">
                <div class="essay-answer" v-html="getStudentAnswer(answer)"></div>
              </template>
            </div>
          </div>
          
          <!-- 评分区域 -->
          <div class="grading-section">
            <el-form :model="gradingForms[answer.id]" label-width="80px">
              <el-form-item label="分数">
                <el-input-number 
                  v-model="gradingForms[answer.id].score" 
                  :min="0" 
                  :max="getQuestionScore(answer)" 
                  :step="0.5"
                  :precision="1">
                </el-input-number>
                <span class="score-max">/ {{ getQuestionScore(answer) }}</span>
              </el-form-item>
              <el-form-item label="评语">
                <el-input 
                  type="textarea" 
                  v-model="gradingForms[answer.id].comment" 
                  :rows="2"
                  placeholder="输入评语（可选）">
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      
      <!-- 总评区域 -->
      <div class="feedback-section">
        <h3>总评</h3>
        <el-input 
          type="textarea" 
          v-model="feedback" 
          :rows="3"
          placeholder="输入总评（可选）">
        </el-input>
      </div>
      
      <!-- 提交按钮 -->
      <div class="submit-section">
        <el-button type="primary" @click="submitGrading" :loading="saving">提交评分</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GradeSubmission',
  props: {
    id: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      submissionId: parseInt(this.id),
      submission: {
        answers: []
      },
      gradingForms: {},
      feedback: '',
      loading: true,
      saving: false,
      totalPossibleScore: 0
    }
  },
  methods: {
    async fetchSubmissionDetails() {
      this.loading = true
      try {
        const response = await this.$http.get(`/api/submissions/submissions/${this.submissionId}`)
        
        if (response.data.success) {
          this.submission = response.data.data
          
          // 初始化评分表单
          this.initGradingForms()
          
          // 计算总分
          this.calculateTotalPossibleScore()
        } else {
          throw new Error(response.data.message || '获取提交详情失败')
        }
      } catch (error) {
        console.error('获取提交详情失败:', error)
        this.$message.error('获取提交详情失败，请稍后再试')
        
        // 使用模拟数据（仅在开发环境或API失败时）
        this.submission = {
          id: this.submissionId,
          student_id: 101,
          student_name: '张三',
          test_id: 1,
          test_title: '示例测试',
          status: 'submitted',
          start_time: '2023-09-15 10:15:00',
          submit_time: '2023-09-15 10:45:00',
          total_score: 0,
          is_fully_graded: false,
          feedback: '',
          answers: [
            {
              id: 1,
              question_id: 1,
              question_type: 'single',
              question_content: '以下哪个是HTML的根元素？',
              answer: JSON.stringify('A'),
              score: 0,
              is_graded: false,
              comment: '',
              question_answer: JSON.stringify({
                correct: 'A',
                options: [
                  { label: '<html>', value: 'A' },
                  { label: '<body>', value: 'B' },
                  { label: '<head>', value: 'C' },
                  { label: '<root>', value: 'D' }
                ]
              }),
              question_score: 2
            },
            {
              id: 2,
              question_id: 2,
              question_type: 'multiple',
              question_content: '以下哪些是JavaScript的数据类型？',
              answer: JSON.stringify(['A', 'C', 'D']),
              score: 0,
              is_graded: false,
              comment: '',
              question_answer: JSON.stringify({
                correct: ['A', 'C', 'D'],
                options: [
                  { label: 'String', value: 'A' },
                  { label: 'Loop', value: 'B' },
                  { label: 'Number', value: 'C' },
                  { label: 'Boolean', value: 'D' }
                ]
              }),
              question_score: 3
            },
            {
              id: 3,
              question_id: 3,
              question_type: 'truefalse',
              question_content: 'CSS是用来描述HTML文档样式的语言。',
              answer: JSON.stringify('true'),
              score: 0,
              is_graded: false,
              comment: '',
              question_answer: JSON.stringify({
                correct: 'true'
              }),
              question_score: 1
            },
            {
              id: 4,
              question_id: 4,
              question_type: 'essay',
              question_content: '简述HTML、CSS和JavaScript的作用。',
              answer: JSON.stringify('HTML用于构建网页结构，CSS用于设计网页样式，JavaScript用于实现网页交互功能。'),
              score: 0,
              is_graded: false,
              comment: '',
              question_answer: JSON.stringify({
                reference: 'HTML用于定义网页的内容和结构，CSS用于控制网页的外观和布局，JavaScript用于实现网页的交互和动态功能。'
              }),
              question_score: 5
            }
          ]
        }
        
        // 初始化评分表单
        this.initGradingForms()
        
        // 计算总分
        this.calculateTotalPossibleScore()
      } finally {
        this.loading = false
      }
    },
    initGradingForms() {
      this.gradingForms = {}
      this.feedback = this.submission.feedback || ''
      
      this.submission.answers.forEach(answer => {
        this.gradingForms[answer.id] = {
          score: answer.is_graded ? answer.score : this.getDefaultScore(answer),
          comment: answer.comment || ''
        }
      })
    },
    calculateTotalPossibleScore() {
      this.totalPossibleScore = this.submission.answers.reduce((total, answer) => {
        return total + this.getQuestionScore(answer)
      }, 0)
    },
    getDefaultScore(answer) {
      // 为选择题和判断题自动评分
      if (['single', 'multiple', 'truefalse'].includes(answer.question_type)) {
        try {
          const studentAnswer = JSON.parse(answer.answer)
          const correctAnswer = JSON.parse(answer.question_answer)
          
          if (answer.question_type === 'single' || answer.question_type === 'truefalse') {
            return studentAnswer === correctAnswer.correct ? this.getQuestionScore(answer) : 0
          } else if (answer.question_type === 'multiple') {
            // 完全匹配才得分
            if (
              Array.isArray(studentAnswer) && 
              Array.isArray(correctAnswer.correct) &&
              studentAnswer.length === correctAnswer.correct.length &&
              studentAnswer.every(a => correctAnswer.correct.includes(a))
            ) {
              return this.getQuestionScore(answer)
            }
            return 0
          }
        } catch (error) {
          console.error('解析答案失败:', error)
          return 0
        }
      }
      
      // 简答题默认为0分
      return 0
    },
    getQuestionScore(answer) {
      return answer.question_score || 0
    },
    formatDateTime(dateTime) {
      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm')
    },
    getSubmissionStatusType(status) {
      const types = {
        'in_progress': 'info',
        'submitted': 'warning',
        'graded': 'success'
      }
      return types[status] || 'info'
    },
    getSubmissionStatusText(status) {
      const texts = {
        'in_progress': '进行中',
        'submitted': '已提交',
        'graded': '已评分'
      }
      return texts[status] || status
    },
    getQuestionTypeTag(type) {
      const types = {
        'single': '',
        'multiple': 'success',
        'truefalse': 'info',
        'essay': 'warning'
      }
      return types[type] || ''
    },
    getQuestionTypeText(type) {
      const texts = {
        'single': '单选题',
        'multiple': '多选题',
        'truefalse': '判断题',
        'essay': '简答题'
      }
      return texts[type] || type
    },
    getStudentAnswer(answer) {
      try {
        return JSON.parse(answer.answer)
      } catch (error) {
        console.error('解析学生答案失败:', error)
        return ''
      }
    },
    getOptionLabel(answer, optionValue) {
      try {
        if (answer.question_type === 'single' || answer.question_type === 'multiple') {
          const questionAnswer = JSON.parse(answer.question_answer)
          const option = questionAnswer.options.find(opt => opt.value === optionValue)
          return option ? `${option.value}. ${option.label}` : optionValue
        }
        return optionValue
      } catch (error) {
        console.error('获取选项标签失败:', error)
        return optionValue
      }
    },
    goBack() {
      this.$router.push(`/teacher/tests/${this.submission.test_id}/submissions`)
    },
    async submitGrading() {
      try {
        // 验证评分
        for (const answer of this.submission.answers) {
          const form = this.gradingForms[answer.id]
          if (form.score < 0 || form.score > this.getQuestionScore(answer)) {
            this.$message.error(`问题 ${this.submission.answers.indexOf(answer) + 1} 的分数超出范围`)
            return
          }
        }
        
        this.saving = true
        
        // 准备提交数据
        const answers = this.submission.answers.map(answer => {
          const form = this.gradingForms[answer.id]
          return {
            id: answer.id,
            score: form.score,
            comment: form.comment
          }
        })
        
        const response = await this.$http.put(`/api/submissions/submissions/${this.submissionId}/grade`, {
          answers,
          feedback: this.feedback
        })
        
        if (response.data.success) {
          this.$message.success('评分提交成功')
          
          // 更新本地数据
          this.submission.is_fully_graded = response.data.data.isFullyGraded
          this.submission.total_score = response.data.data.totalScore
          this.submission.status = response.data.data.isFullyGraded ? 'graded' : 'submitted'
          this.submission.feedback = this.feedback
          
          // 更新答案评分状态
          this.submission.answers.forEach(answer => {
            const form = this.gradingForms[answer.id]
            answer.score = form.score
            answer.comment = form.comment
            answer.is_graded = true
          })
        } else {
          throw new Error(response.data.message || '提交评分失败')
        }
      } catch (error) {
        console.error('提交评分失败:', error)
        this.$message.error(error.message || '提交评分失败，请稍后再试')
      } finally {
        this.saving = false
      }
    }
  },
  created() {
    this.fetchSubmissionDetails()
  }
}
</script>

<style scoped>
.grade-submission {
  padding: 20px;
}

.loading-container {
  padding: 20px;
}

.submission-header {
  display: flex;
  justify-content: space-between;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 30px;
}

.submission-info {
  flex: 1;
}

.submission-info h2 {
  margin-top: 0;
  margin-bottom: 10px;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.student-name {
  font-weight: bold;
}

.submission-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #909399;
}

.submission-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.answers-section {
  margin-bottom: 30px;
}

.answer-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-number {
  font-weight: bold;
  color: #409EFF;
}

.question-content {
  margin-bottom: 15px;
  font-size: 16px;
}

.student-answer {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.answer-label {
  font-weight: bold;
  margin-bottom: 10px;
}

.option-answer {
  margin-bottom: 5px;
}

.essay-answer {
  white-space: pre-wrap;
  line-height: 1.5;
}

.grading-section {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.score-max {
  margin-left: 10px;
  color: #909399;
}

.feedback-section {
  margin-bottom: 30px;
}

.submit-section {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .submission-header {
    flex-direction: column;
  }

  .submission-actions {
    margin-top: 15px;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
