const { pool } = require('./config/db');

async function showTestSubmissions() {
  try {
    console.log('数据库配置:');
    console.log('Host: localhost');
    console.log('User: root');
    console.log('Database: elearning');
    console.log('');
    
    // 查询test_submissions表结构
    try {
      const [submissionColumns] = await pool.query('DESCRIBE test_submissions');
      console.log('\ntest_submissions表结构:');
      submissionColumns.forEach(col => {
        console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
      });
    } catch (error) {
      console.log('\ntest_submissions表不存在或无法访问');
      console.error(error);
    }
    
    // 查询test_answers表结构
    try {
      const [answerColumns] = await pool.query('DESCRIBE test_answers');
      console.log('\ntest_answers表结构:');
      answerColumns.forEach(col => {
        console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
      });
    } catch (error) {
      console.log('\ntest_answers表不存在或无法访问');
      console.error(error);
    }
    
    // 查询discussion_replies表结构
    try {
      const [replyColumns] = await pool.query('DESCRIBE discussion_replies');
      console.log('\ndiscussion_replies表结构:');
      replyColumns.forEach(col => {
        console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
      });
    } catch (error) {
      console.log('\ndiscussion_replies表不存在或无法访问');
      console.error(error);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('查询失败:', error);
    process.exit(1);
  }
}

showTestSubmissions();
