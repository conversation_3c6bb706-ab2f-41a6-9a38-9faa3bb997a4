/* 全局样式 */
html, body, #app {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Aria<PERSON>, sans-serif;
}

/* 响应式布局 */
.responsive-container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .responsive-container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .responsive-container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .responsive-container {
    max-width: 1140px;
  }
}

/* 通用卡片样式 */
.card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border: 1px solid #ebeef5;
  background-color: #fff;
  overflow: hidden;
  transition: 0.3s;
}

.card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
}

/* 页面内容区域 */
.content-container {
  padding: 20px;
}

/* 表单样式 */
.form-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
  }
  
  .el-form-item {
    margin-bottom: 10px !important;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}
