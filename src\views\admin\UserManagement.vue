<template>
  <div class="user-management">
    <div class="page-header">
      <h1>用户管理</h1>
      <el-button type="primary" @click="showCreateUserDialog">创建新用户</el-button>
    </div>
    
    <div class="filter-bar">
      <el-input
        placeholder="搜索用户"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchUsers">
      </el-input>
      <el-select v-model="roleFilter" placeholder="用户角色" clearable @change="fetchUsers">
        <el-option label="管理员" value="admin"></el-option>
        <el-option label="教师" value="teacher"></el-option>
        <el-option label="学生" value="student"></el-option>
      </el-select>
      <el-select v-model="statusFilter" placeholder="用户状态" clearable @change="fetchUsers">
        <el-option label="活跃" value="active"></el-option>
        <el-option label="禁用" value="disabled"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchUsers">搜索</el-button>
    </div>
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>
    
    <div v-else-if="users.length === 0" class="empty-data">
      <el-empty description="暂无用户数据"></el-empty>
    </div>
    
    <el-table v-else :data="users" style="width: 100%" border>
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" inline class="user-expand">
            <el-form-item label="用户ID">
              <span>{{ props.row.id }}</span>
            </el-form-item>
            <el-form-item label="注册时间">
              <span>{{ formatDate(props.row.created_at) }}</span>
            </el-form-item>
            <el-form-item label="最后登录">
              <span>{{ props.row.last_login ? formatDate(props.row.last_login) : '从未登录' }}</span>
            </el-form-item>
            <el-form-item label="登录次数">
              <span>{{ props.row.login_count || 0 }}</span>
            </el-form-item>
            <el-form-item label="IP地址">
              <span>{{ props.row.last_ip || '未知' }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column prop="username" label="用户名" min-width="120"></el-table-column>
      <el-table-column prop="email" label="邮箱" min-width="180"></el-table-column>
      <el-table-column prop="role" label="角色" width="100">
        <template slot-scope="scope">
          <el-tag :type="getUserRoleType(scope.row.role)">
            {{ getUserRoleText(scope.row.role) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '活跃' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editUser(scope.row)">编辑</el-button>
          <el-button 
            type="text" 
            size="small" 
            @click="toggleUserStatus(scope.row)">
            {{ scope.row.status === 'active' ? '禁用' : '启用' }}
          </el-button>
          <el-button type="text" size="small" @click="resetPassword(scope.row)">重置密码</el-button>
          <el-button 
            type="text" 
            size="small" 
            class="danger-button" 
            @click="deleteUser(scope.row)"
            :disabled="scope.row.role === 'admin' && scope.row.id === 1">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container" v-if="users.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>
    
    <!-- 创建/编辑用户对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
      <el-form :model="userForm" :rules="userRules" ref="userForm" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" type="email"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择用户角色" style="width: 100%">
            <el-option label="管理员" value="admin" :disabled="!isAdmin"></el-option>
            <el-option label="教师" value="teacher"></el-option>
            <el-option label="学生" value="student"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!userForm.id">
          <el-input v-model="userForm.password" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="!userForm.id">
          <el-input v-model="userForm.confirmPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="userForm.status"
            active-value="active"
            inactive-value="disabled"
            active-text="活跃"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">保存</el-button>
      </span>
    </el-dialog>
    
    <!-- 重置密码对话框 -->
    <el-dialog title="重置密码" :visible.sync="resetPasswordVisible" width="40%">
      <el-form :model="resetPasswordForm" :rules="resetPasswordRules" ref="resetPasswordForm" label-width="100px">
        <el-form-item label="新密码" prop="password">
          <el-input v-model="resetPasswordForm.password" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="resetPasswordForm.confirmPassword" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetPasswordVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmResetPassword" :loading="resetting">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserManagement',
  data() {
    // 自定义验证规则
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        if (this.userForm.confirmPassword !== '') {
          this.$refs.userForm.validateField('confirmPassword')
        }
        callback()
      }
    }
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.userForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    const validateResetPass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入新密码'))
      } else {
        if (this.resetPasswordForm.confirmPassword !== '') {
          this.$refs.resetPasswordForm.validateField('confirmPassword')
        }
        callback()
      }
    }
    const validateResetPass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入新密码'))
      } else if (value !== this.resetPasswordForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    
    return {
      users: [],
      loading: false,
      searchQuery: '',
      roleFilter: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '创建新用户',
      userForm: {
        id: null,
        username: '',
        email: '',
        role: 'student',
        password: '',
        confirmPassword: '',
        status: 'active'
      },
      userRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        role: [
          { required: true, message: '请选择用户角色', trigger: 'change' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { validator: validatePass, trigger: 'blur' },
          { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validatePass2, trigger: 'blur' }
        ]
      },
      saving: false,
      resetPasswordVisible: false,
      resetPasswordForm: {
        userId: null,
        password: '',
        confirmPassword: ''
      },
      resetPasswordRules: {
        password: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validateResetPass, trigger: 'blur' },
          { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateResetPass2, trigger: 'blur' }
        ]
      },
      resetting: false
    }
  },
  computed: {
    isAdmin() {
      return this.$store.getters.isAdmin
    }
  },
  methods: {
    async fetchUsers() {
      this.loading = true
      try {
        // 实际应该从API获取数据
        // const response = await this.$http.get('/admin/users', {
        //   params: {
        //     search: this.searchQuery,
        //     role: this.roleFilter,
        //     status: this.statusFilter,
        //     page: this.currentPage,
        //     limit: this.pageSize
        //   }
        // })
        // this.users = response.data.data
        // this.total = response.data.total
        
        // 使用模拟数据
        setTimeout(() => {
          this.users = [
            {
              id: 1,
              username: 'admin',
              email: '<EMAIL>',
              role: 'admin',
              status: 'active',
              created_at: '2023-08-01T08:00:00Z',
              last_login: '2023-09-15T10:30:00Z',
              login_count: 42,
              last_ip: '127.0.0.1'
            },
            {
              id: 2,
              username: 'teacher1',
              email: '<EMAIL>',
              role: 'teacher',
              status: 'active',
              created_at: '2023-08-15T10:30:00Z',
              last_login: '2023-09-14T14:20:00Z',
              login_count: 28,
              last_ip: '*************'
            },
            {
              id: 3,
              username: 'student1',
              email: '<EMAIL>',
              role: 'student',
              status: 'active',
              created_at: '2023-08-20T09:15:00Z',
              last_login: '2023-09-15T09:10:00Z',
              login_count: 15,
              last_ip: '*************'
            },
            {
              id: 4,
              username: 'student2',
              email: '<EMAIL>',
              role: 'student',
              status: 'disabled',
              created_at: '2023-08-25T11:45:00Z',
              last_login: null,
              login_count: 0,
              last_ip: null
            }
          ]
          this.total = 4
          this.loading = false
        }, 1000)
      } catch (error) {
        console.error('获取用户失败:', error)
        this.$message.error('获取用户失败，请稍后再试')
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchUsers()
    },
    showCreateUserDialog() {
      this.dialogTitle = '创建新用户'
      this.userForm = {
        id: null,
        username: '',
        email: '',
        role: 'student',
        password: '',
        confirmPassword: '',
        status: 'active'
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.userForm.clearValidate()
      })
    },
    editUser(user) {
      this.dialogTitle = '编辑用户'
      this.userForm = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        status: user.status
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.userForm.clearValidate()
      })
    },
    async saveUser() {
      this.$refs.userForm.validate(async valid => {
        if (valid) {
          this.saving = true
          try {
            // 实际应该调用API
            // const method = this.userForm.id ? 'put' : 'post'
            // const url = this.userForm.id ? `/admin/users/${this.userForm.id}` : '/admin/users'
            // await this.$http[method](url, this.userForm)
            
            // 模拟API调用
            setTimeout(() => {
              this.$message.success(this.userForm.id ? '用户更新成功' : '用户创建成功')
              this.dialogVisible = false
              this.fetchUsers()
              this.saving = false
            }, 1000)
          } catch (error) {
            console.error('保存用户失败:', error)
            this.$message.error('保存用户失败，请稍后再试')
            this.saving = false
          }
        }
      })
    },
    async toggleUserStatus(user) {
      try {
        const newStatus = user.status === 'active' ? 'disabled' : 'active'
        const actionText = newStatus === 'active' ? '启用' : '禁用'
        
        // 不允许禁用主管理员
        if (user.role === 'admin' && user.id === 1 && newStatus === 'disabled') {
          this.$message.warning('不能禁用主管理员账号')
          return
        }
        
        await this.$confirm(`确定要${actionText}用户 "${user.username}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 实际应该调用API
        // await this.$http.patch(`/admin/users/${user.id}/status`, { status: newStatus })
        
        // 模拟API调用
        this.$message.success(`用户${actionText}成功`)
        
        // 更新本地数据
        const index = this.users.findIndex(u => u.id === user.id)
        if (index !== -1) {
          this.users[index].status = newStatus
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('更改用户状态失败:', error)
          this.$message.error('更改用户状态失败，请稍后再试')
        }
      }
    },
    resetPassword(user) {
      this.resetPasswordForm = {
        userId: user.id,
        password: '',
        confirmPassword: ''
      }
      this.resetPasswordVisible = true
      this.$nextTick(() => {
        this.$refs.resetPasswordForm.clearValidate()
      })
    },
    async confirmResetPassword() {
      this.$refs.resetPasswordForm.validate(async valid => {
        if (valid) {
          this.resetting = true
          try {
            // 实际应该调用API
            // await this.$http.post(`/admin/users/${this.resetPasswordForm.userId}/reset-password`, {
            //   password: this.resetPasswordForm.password
            // })
            
            // 模拟API调用
            setTimeout(() => {
              this.$message.success('密码重置成功')
              this.resetPasswordVisible = false
              this.resetting = false
            }, 1000)
          } catch (error) {
            console.error('重置密码失败:', error)
            this.$message.error('重置密码失败，请稍后再试')
            this.resetting = false
          }
        }
      })
    },
    async deleteUser(user) {
      try {
        // 不允许删除主管理员
        if (user.role === 'admin' && user.id === 1) {
          this.$message.warning('不能删除主管理员账号')
          return
        }
        
        await this.$confirm(`确定要删除用户 "${user.username}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 实际应该调用API
        // await this.$http.delete(`/admin/users/${user.id}`)
        
        // 模拟API调用
        this.$message.success('用户删除成功')
        
        // 更新本地数据
        this.users = this.users.filter(u => u.id !== user.id)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除用户失败:', error)
          this.$message.error('删除用户失败，请稍后再试')
        }
      }
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    getUserRoleType(role) {
      const types = {
        'admin': 'danger',
        'teacher': 'warning',
        'student': 'success'
      }
      return types[role] || 'info'
    },
    getUserRoleText(role) {
      const texts = {
        'admin': '管理员',
        'teacher': '教师',
        'student': '学生'
      }
      return texts[role] || role
    }
  },
  created() {
    this.fetchUsers()
  }
}
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.danger-button {
  color: #F56C6C;
}

.danger-button:hover {
  color: #F78989;
}

.user-expand {
  padding: 20px;
}

.user-expand .el-form-item {
  margin-right: 20px;
  margin-bottom: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
  }
  
  .filter-bar .el-input {
    width: 100%;
  }
}
</style>
