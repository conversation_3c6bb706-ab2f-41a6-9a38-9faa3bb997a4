<template>
  <div class="batch-question-editor">
    <div class="editor-header">
      <h3>批量添加问题</h3>
      <p class="editor-description">
        您可以通过以下方式批量添加问题：
        <br>1. 使用模板导入问题
        <br>2. 手动添加多个问题
      </p>
    </div>
    
    <el-tabs v-model="activeTab">
      <el-tab-pane label="模板导入" name="template">
        <div class="template-info">
          <p>使用以下格式的文本导入问题：</p>
          <pre class="template-format">
# 单选题 [分值]
问题内容
A. 选项A
B. 选项B
C. 选项C
D. 选项D
答案: A

# 多选题 [分值]
问题内容
A. 选项A
B. 选项B
C. 选项C
D. 选项D
答案: A,B

# 判断题 [分值]
问题内容
答案: 正确

# 简答题 [分值]
问题内容
参考答案: 参考答案内容（可选）
          </pre>
        </div>
        
        <el-form :model="templateForm" ref="templateForm">
          <el-form-item>
            <el-input
              type="textarea"
              v-model="templateForm.content"
              :rows="10"
              placeholder="请粘贴按照上述格式编写的问题内容">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="parseTemplate">解析模板</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="手动添加" name="manual">
        <div class="manual-questions">
          <div v-for="(question, index) in manualQuestions" :key="index" class="question-item">
            <div class="question-header">
              <h4>问题 {{ index + 1 }}</h4>
              <el-button 
                type="danger" 
                icon="el-icon-delete" 
                circle 
                size="mini" 
                @click="removeQuestion(index)">
              </el-button>
            </div>
            
            <el-form :model="question" label-width="100px">
              <el-form-item label="问题类型">
                <el-select v-model="question.type" placeholder="请选择问题类型" style="width: 100%" @change="(val) => handleTypeChange(val, index)">
                  <el-option label="单选题" value="single"></el-option>
                  <el-option label="多选题" value="multiple"></el-option>
                  <el-option label="判断题" value="truefalse"></el-option>
                  <el-option label="简答题" value="essay"></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="问题内容">
                <el-input 
                  type="textarea" 
                  v-model="question.content" 
                  :rows="2"
                  placeholder="请输入问题内容">
                </el-input>
              </el-form-item>
              
              <!-- 单选题和多选题选项 -->
              <template v-if="['single', 'multiple'].includes(question.type)">
                <el-form-item label="选项">
                  <div v-for="(option, optIndex) in question.options" :key="optIndex" class="option-item">
                    <el-input v-model="option.label" placeholder="选项内容" style="width: calc(100% - 100px)">
                      <template slot="prepend">{{ option.value }}</template>
                    </el-input>
                    <el-button 
                      type="danger" 
                      icon="el-icon-delete" 
                      circle 
                      size="mini" 
                      @click="removeOption(index, optIndex)"
                      :disabled="question.options.length <= 2">
                    </el-button>
                  </div>
                  <div class="option-actions">
                    <el-button type="text" icon="el-icon-plus" @click="addOption(index)">添加选项</el-button>
                  </div>
                </el-form-item>
                
                <el-form-item label="正确答案">
                  <el-select 
                    v-if="question.type === 'single'" 
                    v-model="question.answer" 
                    placeholder="请选择正确答案" 
                    style="width: 100%">
                    <el-option 
                      v-for="option in question.options" 
                      :key="option.value" 
                      :label="option.label" 
                      :value="option.value">
                    </el-option>
                  </el-select>
                  <el-checkbox-group 
                    v-else 
                    v-model="question.answer" 
                    style="width: 100%">
                    <el-checkbox 
                      v-for="option in question.options" 
                      :key="option.value" 
                      :label="option.value">
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </template>
              
              <!-- 判断题答案 -->
              <el-form-item label="正确答案" v-if="question.type === 'truefalse'">
                <el-radio-group v-model="question.answer">
                  <el-radio label="true">正确</el-radio>
                  <el-radio label="false">错误</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <!-- 简答题参考答案 -->
              <el-form-item label="参考答案" v-if="question.type === 'essay'">
                <el-input 
                  type="textarea" 
                  v-model="question.answer" 
                  :rows="2"
                  placeholder="请输入参考答案（可选）">
                </el-input>
              </el-form-item>
              
              <el-form-item label="分值">
                <el-input-number v-model="question.score" :min="0.5" :step="0.5" :precision="1"></el-input-number>
              </el-form-item>
            </el-form>
            
            <el-divider></el-divider>
          </div>
          
          <div class="add-question-button">
            <el-button type="primary" icon="el-icon-plus" @click="addQuestion">添加问题</el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <div class="preview-section" v-if="parsedQuestions.length > 0">
      <h3>预览 ({{ parsedQuestions.length }}个问题)</h3>
      
      <el-table :data="parsedQuestions" style="width: 100%" border>
        <el-table-column prop="type" label="类型" width="100">
          <template slot-scope="scope">
            {{ getQuestionTypeText(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="content" label="问题内容" show-overflow-tooltip></el-table-column>
        <el-table-column prop="score" label="分值" width="80" align="center"></el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button 
              type="danger" 
              icon="el-icon-delete" 
              circle 
              size="mini" 
              @click="removeFromParsed(scope.$index)">
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div class="form-actions">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="save" :loading="saving" :disabled="parsedQuestions.length === 0">
        保存 ({{ parsedQuestions.length }}个问题)
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BatchQuestionEditor',
  props: {
    testId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      activeTab: 'template',
      templateForm: {
        content: ''
      },
      manualQuestions: [
        this.createEmptyQuestion()
      ],
      parsedQuestions: [],
      saving: false
    }
  },
  methods: {
    createEmptyQuestion() {
      return {
        type: 'single',
        content: '',
        options: [
          { label: '选项A', value: 'A' },
          { label: '选项B', value: 'B' },
          { label: '选项C', value: 'C' },
          { label: '选项D', value: 'D' }
        ],
        answer: 'A',
        score: 2
      }
    },
    getQuestionTypeText(type) {
      const texts = {
        'single': '单选题',
        'multiple': '多选题',
        'truefalse': '判断题',
        'essay': '简答题'
      }
      return texts[type] || type
    },
    handleTypeChange(type, index) {
      const question = this.manualQuestions[index]
      
      // 重置答案
      if (type === 'single') {
        question.answer = question.options[0]?.value || 'A'
      } else if (type === 'multiple') {
        question.answer = []
      } else if (type === 'truefalse') {
        question.answer = 'true'
      } else if (type === 'essay') {
        question.answer = ''
      }
    },
    addQuestion() {
      this.manualQuestions.push(this.createEmptyQuestion())
    },
    removeQuestion(index) {
      this.manualQuestions.splice(index, 1)
      
      // 至少保留一个问题
      if (this.manualQuestions.length === 0) {
        this.manualQuestions.push(this.createEmptyQuestion())
      }
    },
    addOption(questionIndex) {
      const question = this.manualQuestions[questionIndex]
      
      // 生成新选项的值
      const lastOption = question.options[question.options.length - 1]
      const lastValue = lastOption ? lastOption.value : 'A'
      const newValue = String.fromCharCode(lastValue.charCodeAt(0) + 1)
      
      question.options.push({
        label: `选项${newValue}`,
        value: newValue
      })
    },
    removeOption(questionIndex, optionIndex) {
      const question = this.manualQuestions[questionIndex]
      
      // 至少保留两个选项
      if (question.options.length <= 2) {
        return
      }
      
      const removedOption = question.options[optionIndex]
      question.options.splice(optionIndex, 1)
      
      // 如果删除的是正确答案，重置答案
      if (question.type === 'single' && question.answer === removedOption.value) {
        question.answer = question.options[0]?.value || 'A'
      } else if (question.type === 'multiple' && Array.isArray(question.answer)) {
        question.answer = question.answer.filter(value => value !== removedOption.value)
      }
    },
    parseTemplate() {
      const content = this.templateForm.content.trim()
      
      if (!content) {
        this.$message.warning('请输入模板内容')
        return
      }
      
      try {
        // 按问题分割
        const questionBlocks = content.split(/\n\s*#/).map((block, index) => {
          return index === 0 ? block.trim() : `#${block.trim()}`
        }).filter(block => block.trim())
        
        const parsedQuestions = []
        
        for (const block of questionBlocks) {
          // 解析问题类型和分值
          const typeMatch = block.match(/^#\s*(单选题|多选题|判断题|简答题)\s*\[(\d+(\.\d+)?)\]/)
          
          if (!typeMatch) {
            continue
          }
          
          const typeText = typeMatch[1]
          const score = parseFloat(typeMatch[2])
          
          let type = ''
          if (typeText === '单选题') type = 'single'
          else if (typeText === '多选题') type = 'multiple'
          else if (typeText === '判断题') type = 'truefalse'
          else if (typeText === '简答题') type = 'essay'
          
          // 提取问题内容和选项
          const lines = block.substring(typeMatch[0].length).trim().split('\n')
          const content = lines[0].trim()
          
          let options = []
          let answer = null
          
          if (type === 'single' || type === 'multiple') {
            // 提取选项
            const optionLines = []
            let i = 1
            while (i < lines.length && lines[i].match(/^[A-Z]\.\s+/)) {
              optionLines.push(lines[i])
              i++
            }
            
            options = optionLines.map(line => {
              const match = line.match(/^([A-Z])\.\s+(.+)/)
              return {
                value: match[1],
                label: match[2].trim()
              }
            })
            
            // 提取答案
            const answerLine = lines.find(line => line.startsWith('答案:'))
            if (answerLine) {
              const answerText = answerLine.substring(3).trim()
              
              if (type === 'single') {
                answer = answerText
              } else {
                answer = answerText.split(',').map(a => a.trim())
              }
            }
          } else if (type === 'truefalse') {
            // 提取判断题答案
            const answerLine = lines.find(line => line.startsWith('答案:'))
            if (answerLine) {
              const answerText = answerLine.substring(3).trim()
              answer = answerText === '正确' ? 'true' : 'false'
            }
          } else if (type === 'essay') {
            // 提取简答题参考答案
            const answerLine = lines.find(line => line.startsWith('参考答案:'))
            if (answerLine) {
              answer = answerLine.substring(5).trim()
            } else {
              answer = ''
            }
          }
          
          // 添加到解析结果
          parsedQuestions.push({
            testId: this.testId,
            type,
            content,
            options,
            answer,
            score,
            sortOrder: parsedQuestions.length
          })
        }
        
        if (parsedQuestions.length === 0) {
          this.$message.warning('未能解析出有效的问题，请检查模板格式')
          return
        }
        
        // 添加到预览列表
        this.parsedQuestions = [...this.parsedQuestions, ...parsedQuestions]
        this.$message.success(`成功解析出 ${parsedQuestions.length} 个问题`)
      } catch (error) {
        console.error('解析模板失败:', error)
        this.$message.error('解析模板失败，请检查格式是否正确')
      }
    },
    addManualQuestions() {
      // 验证手动添加的问题
      const validQuestions = this.manualQuestions.filter(q => {
        return q.content && (
          (q.type === 'single' && q.answer) ||
          (q.type === 'multiple' && Array.isArray(q.answer) && q.answer.length > 0) ||
          (q.type === 'truefalse' && q.answer) ||
          q.type === 'essay'
        )
      })
      
      if (validQuestions.length === 0) {
        this.$message.warning('请至少添加一个有效的问题')
        return
      }
      
      // 转换为标准格式
      const parsedQuestions = validQuestions.map((q, index) => {
        return {
          testId: this.testId,
          type: q.type,
          content: q.content,
          options: q.options,
          answer: q.answer,
          score: q.score,
          sortOrder: this.parsedQuestions.length + index
        }
      })
      
      // 添加到预览列表
      this.parsedQuestions = [...this.parsedQuestions, ...parsedQuestions]
      this.$message.success(`成功添加 ${parsedQuestions.length} 个问题`)
      
      // 重置手动添加表单
      this.manualQuestions = [this.createEmptyQuestion()]
    },
    removeFromParsed(index) {
      this.parsedQuestions.splice(index, 1)
    },
    cancel() {
      this.$emit('cancel')
    },
    async save() {
      if (this.parsedQuestions.length === 0) {
        this.$message.warning('请至少添加一个问题')
        return
      }
      
      this.saving = true
      
      try {
        // 准备问题数据
        const questions = this.parsedQuestions.map(q => {
          const questionData = {
            type: q.type,
            content: q.content,
            score: q.score,
            sortOrder: q.sortOrder
          }
          
          // 根据问题类型准备答案数据
          if (q.type === 'single') {
            questionData.answer = {
              correct: q.answer,
              options: q.options
            }
          } else if (q.type === 'multiple') {
            questionData.answer = {
              correct: q.answer,
              options: q.options
            }
          } else if (q.type === 'truefalse') {
            questionData.answer = {
              correct: q.answer
            }
          } else if (q.type === 'essay') {
            questionData.answer = {
              reference: q.answer
            }
          }
          
          return questionData
        })
        
        // 批量添加问题
        const response = await this.$http.post(`/api/questions/tests/${this.testId}/questions/batch`, {
          questions
        })
        
        if (response.data.success) {
          this.$message.success(`成功添加 ${response.data.count} 个问题`)
          this.$emit('save')
        } else {
          throw new Error(response.data.message || '批量添加问题失败')
        }
      } catch (error) {
        console.error('批量添加问题失败:', error)
        this.$message.error(error.message || '批量添加问题失败，请稍后再试')
      } finally {
        this.saving = false
      }
    }
  },
  watch: {
    activeTab(tab) {
      if (tab === 'manual' && this.manualQuestions.length === 0) {
        this.manualQuestions.push(this.createEmptyQuestion())
      }
    }
  }
}
</script>

<style scoped>
.batch-question-editor {
  padding: 10px 0;
}

.editor-header {
  margin-bottom: 20px;
}

.editor-description {
  color: #606266;
  line-height: 1.5;
}

.template-info {
  margin-bottom: 20px;
}

.template-format {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  line-height: 1.5;
}

.question-item {
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-header h4 {
  margin: 0;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.option-actions {
  margin-top: 10px;
}

.add-question-button {
  margin: 20px 0;
  text-align: center;
}

.preview-section {
  margin-top: 30px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}
</style>
