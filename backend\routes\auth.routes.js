const express = require('express');
const { check } = require('express-validator');
const { register, login, getMe, logout } = require('../controllers/auth.controller');
const { protect } = require('../middleware/auth');

const router = express.Router();

// 注册路由
router.post(
  '/register',
  [
    check('username', '用户名是必需的').not().isEmpty(),
    check('username', '用户名长度应在3-20个字符之间').isLength({ min: 3, max: 20 }),
    check('email', '请提供有效的邮箱').isEmail(),
    check('password', '密码长度应至少为6个字符').isLength({ min: 6 }),
    check('role', '角色无效').optional().isIn(['student', 'teacher'])
  ],
  register
);

// 登录路由
router.post(
  '/login',
  [
    check('username', '请提供用户名').not().isEmpty(),
    check('password', '请提供密码').exists()
  ],
  login
);

// 获取当前用户信息
router.get('/me', protect, getMe);

// 退出登录
router.post('/logout', protect, logout);

module.exports = router;
