const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect } = require('../middleware/auth');

// 授权中间件 - 检查用户是否为教师或管理员
const authorizeTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'teacher' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: '权限不足，需要教师权限'
    });
  }
};

// 获取课程测试列表
router.get('/course/:courseId', protect, async (req, res) => {
  try {
    const courseId = req.params.courseId;
    const { page = 1, limit = 10 } = req.query;

    // 验证课程存在
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    // 检查用户是否有权限访问该课程
    const course = courses[0];
    const isTeacher = req.user.role === 'teacher' && course.teacher_id === req.user.id;
    const isAdmin = req.user.role === 'admin';
    const isStudent = req.user.role === 'student';

    // 如果是学生，检查课程是否已发布且学生是否已选修
    if (isStudent) {
      if (course.status !== 'published') {
        return res.status(403).json({
          success: false,
          message: '该课程尚未发布'
        });
      }

      // 检查学生是否已选修该课程
      const [enrollments] = await pool.query(
        'SELECT * FROM enrollments WHERE course_id = ? AND student_id = ?',
        [courseId, req.user.id]
      );

      if (enrollments.length === 0) {
        return res.status(403).json({
          success: false,
          message: '您尚未选修该课程'
        });
      }
    }

    // 构建查询条件
    let query = `
      SELECT t.*, u.username as teacher_name,
             CASE WHEN t.status = 'published' THEN 1 ELSE 0 END as is_published,
             t.time_limit as duration, 100 as passing_score
      FROM tests t
      JOIN users u ON t.creator_id = u.id
      WHERE t.course_id = ?
    `;
    const queryParams = [courseId];

    // 如果是学生，只显示已发布的测试
    if (isStudent) {
      query += ' AND t.status = "published"';
    }

    // 添加排序和分页
    const offset = (page - 1) * limit;
    query += ' ORDER BY t.start_time DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [tests] = await pool.query(query, queryParams);

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM tests t
      WHERE t.course_id = ?
    `;
    const countParams = [courseId];

    // 如果是学生，只计算已发布的测试
    if (isStudent) {
      countQuery += ' AND t.status = "published"';
    }

    const [countResult] = await pool.query(countQuery, countParams);
    const total = countResult[0].total;

    // 如果是学生，添加测试状态信息
    if (isStudent) {
      // 获取学生的测试提交记录
      const [submissions] = await pool.query(
        `SELECT test_id, status, score, is_graded
         FROM test_submissions
         WHERE student_id = ? AND test_id IN (?)`,
        [req.user.id, tests.length > 0 ? tests.map(test => test.id) : [0]]
      );

      // 将提交状态添加到测试数据中
      tests.forEach(test => {
        const submission = submissions.find(s => s.test_id === test.id);
        if (submission) {
          test.submission_status = submission.status;
          test.score = submission.score;
          test.is_graded = submission.is_graded;
        } else {
          const now = new Date();
          const startTime = new Date(test.start_time);
          const endTime = new Date(test.end_time);

          if (now < startTime) {
            test.submission_status = 'not_started';
          } else if (now > endTime) {
            test.submission_status = 'expired';
          } else {
            test.submission_status = 'available';
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      count: tests.length,
      total,
      data: tests
    });
  } catch (error) {
    console.error('获取课程测试失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程测试失败',
      error: error.message
    });
  }
});

// 创建新测试
router.post('/', protect, authorizeTeacher, async (req, res) => {
  try {
    const {
      title,
      description,
      course_id,
      start_time,
      end_time,
      duration,
      total_score = 100,
      passing_score = 60,
      is_published = false
    } = req.body;

    // 验证课程存在且教师有权限
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [course_id]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    const course = courses[0];

    // 检查教师是否有权限操作该课程
    if (req.user.role === 'teacher' && course.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该课程的教师，无权添加测试'
      });
    }

    // 插入测试记录
    const [result] = await pool.query(
      `INSERT INTO tests (
        title, description, course_id, creator_id,
        start_time, end_time, time_limit, total_score, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        title,
        description,
        course_id,
        req.user.id,
        start_time,
        end_time,
        duration,
        total_score,
        is_published ? 'published' : 'draft'
      ]
    );

    // 获取新创建的测试
    const [newTest] = await pool.query(
      `SELECT t.*, u.username as teacher_name,
              CASE WHEN t.status = 'published' THEN 1 ELSE 0 END as is_published,
              t.time_limit as duration, 100 as passing_score
       FROM tests t
       JOIN users u ON t.creator_id = u.id
       WHERE t.id = ?`,
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '测试创建成功',
      data: newTest[0]
    });
  } catch (error) {
    console.error('创建测试失败:', error);
    res.status(500).json({
      success: false,
      message: '创建测试失败',
      error: error.message
    });
  }
});

// 添加测试题目
router.post('/:id/questions', protect, authorizeTeacher, async (req, res) => {
  try {
    const testId = req.params.id;
    const {
      question_text,
      question_type,
      options,
      correct_answer,
      score = 10,
      order_num
    } = req.body;

    // 验证测试存在且教师有权限
    const [tests] = await pool.query(
      'SELECT t.*, c.teacher_id FROM tests t JOIN courses c ON t.course_id = c.id WHERE t.id = ?',
      [testId]
    );

    if (tests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }

    const test = tests[0];

    // 检查教师是否有权限操作该测试
    if (req.user.role === 'teacher' && test.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权添加题目'
      });
    }

    // 插入题目记录
    const [result] = await pool.query(
      `INSERT INTO test_questions (
        test_id, question_text, question_type, options, correct_answer, score, order_num
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        testId,
        question_text,
        question_type,
        JSON.stringify(options),
        correct_answer,
        score,
        order_num
      ]
    );

    // 获取新创建的题目
    const [newQuestion] = await pool.query(
      'SELECT * FROM test_questions WHERE id = ?',
      [result.insertId]
    );

    // 解析JSON选项
    if (newQuestion[0].options) {
      newQuestion[0].options = JSON.parse(newQuestion[0].options);
    }

    res.status(201).json({
      success: true,
      message: '题目添加成功',
      data: newQuestion[0]
    });
  } catch (error) {
    console.error('添加题目失败:', error);
    res.status(500).json({
      success: false,
      message: '添加题目失败',
      error: error.message
    });
  }
});

// 获取测试题目列表
router.get('/:id/questions', protect, authorizeTeacher, async (req, res) => {
  try {
    const testId = req.params.id;

    // 验证测试存在且教师有权限
    const [tests] = await pool.query(
      'SELECT t.*, c.teacher_id FROM tests t JOIN courses c ON t.course_id = c.id WHERE t.id = ?',
      [testId]
    );

    if (tests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }

    const test = tests[0];

    // 检查教师是否有权限操作该测试
    if (req.user.role === 'teacher' && test.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权查看题目'
      });
    }

    // 获取题目列表
    const [questions] = await pool.query(
      'SELECT * FROM test_questions WHERE test_id = ? ORDER BY order_num ASC, id ASC',
      [testId]
    );

    // 解析JSON选项
    questions.forEach(question => {
      if (question.options) {
        try {
          question.options = JSON.parse(question.options);
        } catch (e) {
          console.error('解析题目选项失败:', e);
        }
      }
    });

    res.status(200).json({
      success: true,
      count: questions.length,
      data: questions
    });
  } catch (error) {
    console.error('获取测试题目失败:', error);
    res.status(500).json({
      success: false,
      message: '获取测试题目失败',
      error: error.message
    });
  }
});

// 更新测试状态（发布/取消发布）
router.patch('/:id/status', protect, authorizeTeacher, async (req, res) => {
  try {
    const testId = req.params.id;
    const { is_published } = req.body;

    // 验证测试存在且教师有权限
    const [tests] = await pool.query(
      'SELECT t.*, c.teacher_id FROM tests t JOIN courses c ON t.course_id = c.id WHERE t.id = ?',
      [testId]
    );

    if (tests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }

    const test = tests[0];

    // 检查教师是否有权限操作该测试
    if (req.user.role === 'teacher' && test.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权修改状态'
      });
    }

    // 更新测试状态
    const newStatus = is_published ? 'published' : 'draft';
    await pool.query(
      'UPDATE tests SET status = ?, updated_at = NOW() WHERE id = ?',
      [newStatus, testId]
    );

    res.status(200).json({
      success: true,
      message: is_published ? '测试已发布' : '测试已取消发布'
    });
  } catch (error) {
    console.error('更新测试状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新测试状态失败',
      error: error.message
    });
  }
});

module.exports = router;
