const fs = require('fs');
const path = require('path');

/**
 * 初始化上传目录
 */
function initUploadDirs() {
  const uploadDirs = [
    path.join(__dirname, '../uploads'),
    path.join(__dirname, '../uploads/images'),
    path.join(__dirname, '../uploads/videos'),
    path.join(__dirname, '../uploads/documents'),
    path.join(__dirname, '../uploads/documents/pdf'),
    path.join(__dirname, '../uploads/documents/other')
  ];

  uploadDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`创建目录: ${dir}`);
    }
  });

  console.log('上传目录初始化完成');
}

module.exports = { initUploadDirs };
