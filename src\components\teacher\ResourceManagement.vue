<template>
  <div class="resource-management">
    <div class="management-header">
      <div class="filter-bar">
        <el-input
          placeholder="搜索资源"
          v-model="searchQuery"
          prefix-icon="el-icon-search"
          clearable
          @clear="fetchResources">
        </el-input>
        <el-select v-model="typeFilter" placeholder="资源类型" clearable @change="fetchResources">
          <el-option label="文档" value="document"></el-option>
          <el-option label="视频" value="video"></el-option>
          <el-option label="链接" value="link"></el-option>
          <el-option label="其他" value="other"></el-option>
        </el-select>
        <el-button type="primary" @click="fetchResources">搜索</el-button>
      </div>
      <el-button type="success" @click="showUploadDialog">上传资源</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="resources.length === 0" class="empty-data">
      <el-empty description="暂无课程资源">
        <el-button type="primary" @click="showUploadDialog">上传第一个资源</el-button>
      </el-empty>
    </div>

    <div v-else>
      <!-- 资源列表 -->
      <el-table :data="resources" style="width: 100%">
        <el-table-column prop="title" label="资源名称" min-width="200"></el-table-column>
        <el-table-column prop="description" label="描述" min-width="300">
          <template slot-scope="scope">
            <span v-if="scope.row.description">{{ scope.row.description }}</span>
            <span v-else class="no-data">无描述</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getResourceTypeType(scope.row.type)">
              {{ getResourceTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.size">{{ formatFileSize(scope.row.size) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="download_count" label="下载次数" width="100" align="center"></el-table-column>
        <el-table-column prop="created_at" label="上传时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="previewResource(scope.row)"
              v-if="canPreview(scope.row)">
              预览
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="downloadResource(scope.row)">
              下载
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="editResource(scope.row)">
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              class="danger-button"
              @click="deleteResource(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange">
        </el-pagination>
      </div>
    </div>

    <!-- 资源预览对话框 -->
    <el-dialog :title="previewTitle" :visible.sync="previewVisible" width="70%">
      <div v-if="previewLoading" class="preview-loading">
        <el-skeleton :rows="10" animated />
      </div>
      <div v-else>
        <!-- 文档预览 -->
        <div v-if="currentResource.type === 'document'" class="document-preview">
          <iframe :src="currentResource.url" width="100%" height="500"></iframe>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="currentResource.type === 'video'" class="video-preview">
          <video controls width="100%" :src="currentResource.url"></video>
        </div>

        <!-- 链接预览 -->
        <div v-else-if="currentResource.type === 'link'" class="link-preview">
          <p>外部链接: <a :href="currentResource.url" target="_blank">{{ currentResource.url }}</a></p>
        </div>

        <!-- 其他类型 -->
        <div v-else class="other-preview">
          <p>无法预览此类型的资源，请下载后查看。</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadResource(currentResource)">下载</el-button>
      </span>
    </el-dialog>

    <!-- 上传/编辑资源对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="uploadVisible" width="50%">
      <el-form :model="resourceForm" :rules="resourceRules" ref="resourceForm" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="resourceForm.title" placeholder="请输入资源标题"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            type="textarea"
            v-model="resourceForm.description"
            :rows="3"
            placeholder="请输入资源描述">
          </el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="resourceForm.type" placeholder="请选择资源类型" style="width: 100%">
            <el-option label="文档" value="document"></el-option>
            <el-option label="视频" value="video"></el-option>
            <el-option label="链接" value="link"></el-option>
            <el-option label="其他" value="other"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件" prop="file" v-if="resourceForm.type !== 'link' && !resourceForm.id">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :http-request="handleFileUpload"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :limit="1"
            :on-exceed="handleExceed"
            :on-remove="handleRemove">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              文档类型支持PDF、Word、Excel、PPT等格式，视频类型支持MP4、WebM等格式
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="链接" prop="url" v-if="resourceForm.type === 'link'">
          <el-input v-model="resourceForm.url" placeholder="请输入资源链接"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadVisible = false">取消</el-button>
        <el-button type="primary" @click="saveResource" :loading="saving">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ResourceManagement',
  props: {
    courseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      resources: [],
      loading: false,
      searchQuery: '',
      typeFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      previewVisible: false,
      previewLoading: false,
      currentResource: {},
      uploadVisible: false,
      dialogTitle: '上传资源',
      resourceForm: {
        id: null,
        title: '',
        description: '',
        type: 'document',
        url: '',
        file: null
      },
      resourceRules: {
        title: [
          { required: true, message: '请输入资源标题', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择资源类型', trigger: 'change' }
        ],
        url: [
          { required: true, message: '请输入资源链接', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
        ]
      },
      fileList: [],
      saving: false
    }
  },
  computed: {
    previewTitle() {
      return `预览: ${this.currentResource.title || ''}`
    }
  },
  methods: {
    async fetchResources() {
      this.loading = true
      try {
        // 从API获取数据
        const response = await this.$http.get(`/api/resources/courses/${this.courseId}/resources`, {
          params: {
            search: this.searchQuery,
            type: this.typeFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.resources = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取资源失败')
        }
      } catch (error) {
        console.error('获取资源失败:', error)
        this.$message.error('获取资源失败，请稍后再试')

        // 使用模拟数据（仅在开发环境或API失败时）
        this.resources = [
          {
            id: 1,
            title: 'JavaScript基础教程',
            description: '详细介绍JavaScript的基础语法和使用方法',
            type: 'document',
            url: 'https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide',
            size: 2048576, // 2MB
            mime_type: 'application/pdf',
            created_at: '2023-09-05T10:30:00Z',
            download_count: 15
          },
          {
            id: 2,
            title: 'HTML5视频教程',
            description: 'HTML5新特性详解',
            type: 'video',
            url: 'https://www.example.com/videos/html5-tutorial.mp4',
            size: 52428800, // 50MB
            mime_type: 'video/mp4',
            created_at: '2023-09-10T14:00:00Z',
            download_count: 23
          },
          {
            id: 3,
            title: 'CSS参考手册',
            description: 'CSS属性和选择器完整参考',
            type: 'link',
            url: 'https://developer.mozilla.org/zh-CN/docs/Web/CSS/Reference',
            size: null,
            mime_type: null,
            created_at: '2023-09-15T09:00:00Z',
            download_count: 10
          }
        ]
        this.total = 3
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchResources()
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + ' MB'
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
      }
    },
    getResourceTypeType(type) {
      const types = {
        'document': 'primary',
        'video': 'success',
        'link': 'info',
        'other': 'warning'
      }
      return types[type] || 'info'
    },
    getResourceTypeText(type) {
      const texts = {
        'document': '文档',
        'video': '视频',
        'link': '链接',
        'other': '其他'
      }
      return texts[type] || type
    },
    canPreview(resource) {
      return ['document', 'video', 'link'].includes(resource.type)
    },
    async previewResource(resource) {
      this.currentResource = resource
      this.previewVisible = true
      this.previewLoading = true

      try {
        // 获取资源详情（如果需要更多信息）
        if (resource.type !== 'link') {
          const response = await this.$http.get(`/api/resources/${resource.id}`)
          if (response.data.success) {
            this.currentResource = response.data.data
          }
        }
      } catch (error) {
        console.error('获取资源详情失败:', error)
      } finally {
        this.previewLoading = false
      }
    },
    downloadResource(resource) {
      // 如果是链接类型，直接在新窗口打开
      if (resource.type === 'link') {
        window.open(resource.url, '_blank')
        return
      }

      // 其他类型通过API下载
      const downloadUrl = `/api/resources/${resource.id}/download`

      // 创建一个隐藏的a标签并触发点击
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = downloadUrl
      a.target = '_blank'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)

      this.$message.success(`开始下载资源: ${resource.title}`)
    },
    showUploadDialog() {
      this.dialogTitle = '上传资源'
      this.resourceForm = {
        id: null,
        title: '',
        description: '',
        type: 'document',
        url: '',
        file: null
      }
      this.fileList = []
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.resourceForm.clearValidate()
      })
    },
    editResource(resource) {
      this.dialogTitle = '编辑资源'
      this.resourceForm = {
        id: resource.id,
        title: resource.title,
        description: resource.description,
        type: resource.type,
        url: resource.url
      }
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.resourceForm.clearValidate()
      })
    },
    async deleteResource(resource) {
      try {
        await this.$confirm(`确定要删除资源 "${resource.title}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用API删除资源
        const response = await this.$http.delete(`/api/resources/${resource.id}`)

        if (response.data.success) {
          this.$message.success('资源删除成功')

          // 更新资源列表
          this.resources = this.resources.filter(r => r.id !== resource.id)
          this.total--
        } else {
          throw new Error(response.data.message || '删除资源失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除资源失败:', error)
          this.$message.error(error.message || '删除资源失败，请稍后再试')
        }
      }
    },
    beforeUpload(file) {
      // 检查文件类型和大小
      const isValidType = this.validateFileType(file)
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isValidType) {
        this.$message.error('文件类型不支持')
        return false
      }

      if (!isLt100M) {
        this.$message.error('文件大小不能超过100MB')
        return false
      }

      return true
    },
    validateFileType(file) {
      const type = this.resourceForm.type

      if (type === 'document') {
        return /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt)$/i.test(file.name)
      } else if (type === 'video') {
        return /\.(mp4|webm|ogg|mov)$/i.test(file.name)
      } else {
        return true
      }
    },
    handleExceed() {
      this.$message.warning('最多只能上传1个文件')
    },
    handleRemove() {
      this.resourceForm.file = null
      this.fileList = []
    },
    handleFileUpload(options) {
      this.resourceForm.file = options.file
      this.fileList = [options.file]

      // 自动设置标题（如果为空）
      if (!this.resourceForm.title) {
        this.resourceForm.title = options.file.name.split('.')[0]
      }
    },
    async saveResource() {
      this.$refs.resourceForm.validate(async valid => {
        if (valid) {
          // 检查链接类型是否提供了URL
          if (this.resourceForm.type === 'link' && !this.resourceForm.url) {
            this.$message.error('请输入资源链接')
            return
          }

          // 检查非链接类型是否提供了文件（仅新建时）
          if (this.resourceForm.type !== 'link' && !this.resourceForm.id && !this.resourceForm.file) {
            this.$message.error('请上传文件')
            return
          }

          this.saving = true

          try {
            if (this.resourceForm.id) {
              // 更新现有资源
              const response = await this.$http.put(`/api/resources/${this.resourceForm.id}`, {
                title: this.resourceForm.title,
                description: this.resourceForm.description,
                type: this.resourceForm.type,
                url: this.resourceForm.type === 'link' ? this.resourceForm.url : undefined,
                is_public: true
              })

              if (response.data.success) {
                // 更新本地资源列表
                const index = this.resources.findIndex(r => r.id === this.resourceForm.id)
                if (index !== -1) {
                  this.resources[index] = response.data.data
                }

                this.$message.success('资源更新成功')
                this.uploadVisible = false
              } else {
                throw new Error(response.data.message || '更新资源失败')
              }
            } else {
              // 创建新资源
              const formData = new FormData()
              formData.append('title', this.resourceForm.title)
              formData.append('description', this.resourceForm.description)
              formData.append('type', this.resourceForm.type)

              if (this.resourceForm.type === 'link') {
                formData.append('url', this.resourceForm.url)
              } else {
                formData.append('file', this.resourceForm.file)
              }

              const response = await this.$http.post(
                `/api/resources/courses/${this.courseId}/resources`,
                formData,
                {
                  headers: {
                    'Content-Type': 'multipart/form-data'
                  }
                }
              )

              if (response.data.success) {
                // 添加新资源到列表
                this.resources.unshift(response.data.data)
                this.total++

                this.$message.success('资源上传成功')
                this.uploadVisible = false
              } else {
                throw new Error(response.data.message || '上传资源失败')
              }
            }
          } catch (error) {
            console.error('保存资源失败:', error)
            this.$message.error(error.message || '保存资源失败，请稍后再试')
          } finally {
            this.saving = false
          }
        }
      })
    }
  },
  created() {
    this.fetchResources()
  }
}
</script>

<style scoped>
.resource-management {
  padding: 10px 0;
}

.management-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container, .preview-loading {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.no-data {
  color: #909399;
  font-style: italic;
}

.danger-button {
  color: #F56C6C;
}

.danger-button:hover {
  color: #F78989;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    gap: 10px;
  }

  .filter-bar {
    flex-direction: column;
    width: 100%;
  }

  .filter-bar .el-input {
    width: 100%;
  }
}
</style>
