const mongoose = require('mongoose');

const ResourceSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, '请提供资源标题'],
    trim: true,
    maxlength: [100, '资源标题不能超过100个字符']
  },
  description: {
    type: String
  },
  // 资源类型：document, video, link, other
  type: {
    type: String,
    enum: ['document', 'video', 'link', 'other'],
    required: [true, '请指定资源类型']
  },
  // 文件路径或URL
  url: {
    type: String,
    required: [true, '请提供资源路径或URL']
  },
  // 文件大小（字节）
  size: {
    type: Number
  },
  // 文件MIME类型
  mimeType: {
    type: String
  },
  // 所属课程
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, '请指定所属课程']
  },
  // 上传者
  uploader: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定上传者']
  },
  // 下载次数
  downloadCount: {
    type: Number,
    default: 0
  },
  // 是否公开（对所有学生可见）
  isPublic: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Resource', ResourceSchema);
