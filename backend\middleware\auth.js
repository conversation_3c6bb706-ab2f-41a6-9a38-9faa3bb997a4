const jwt = require('jsonwebtoken');
const { pool } = require('../config/db');

// 保护路由中间件
exports.protect = async (req, res, next) => {
  let token;

  // 从请求头或cookie中获取token
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    // 从Bearer token中提取
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies && req.cookies.token) {
    // 从cookie中提取
    token = req.cookies.token;
  }

  // 检查token是否存在
  if (!token) {
    return res.status(401).json({
      success: false,
      message: '未授权访问，请登录'
    });
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret_key');

    // 获取用户信息
    const [rows] = await pool.query(
      'SELECT id, username, email, role, avatar FROM users WHERE id = ?',
      [decoded.id]
    );

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: '找不到该用户'
      });
    }

    // 将用户信息添加到请求对象
    req.user = rows[0];
    next();
  } catch (error) {
    console.error('认证失败:', error);
    return res.status(401).json({
      success: false,
      message: '未授权访问，请登录'
    });
  }
};

// 角色授权中间件
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未授权访问，请登录'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '您没有权限执行此操作'
      });
    }

    next();
  };
};
