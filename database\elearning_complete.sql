-- E-learning平台数据库结构 (MySQL 8.0+)
-- 创建数据库
DROP DATABASE IF EXISTS elearning;
CREATE DATABASE elearning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE elearning;

-- 用户表
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(20) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student',
    avatar VARCHAR(255) DEFAULT 'default-avatar.jpg',
    -- 教师特有字段
    teacher_title VARCHAR(50),
    teacher_department VARCHAR(100),
    teacher_bio TEXT,
    -- 学生特有字段
    student_id VARCHAR(50),
    student_grade VARCHAR(20),
    student_major VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_role (role),
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB;

-- 课程表
CREATE TABLE courses (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    cover_image VARCHAR(255) DEFAULT 'default-course.jpg',
    teacher_id BIGINT UNSIGNED NOT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    category VARCHAR(50) NOT NULL,
    start_date DATE,
    end_date DATE,
    course_code VARCHAR(6) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_teacher (teacher_id),
    INDEX idx_status (status),
    INDEX idx_category (category)
) ENGINE=InnoDB;

-- 课程标签表
CREATE TABLE course_tags (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT UNSIGNED NOT NULL,
    tag VARCHAR(50) NOT NULL,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_course_tag (course_id, tag)
) ENGINE=InnoDB;

-- 课程学生关联表
CREATE TABLE course_students (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT UNSIGNED NOT NULL,
    student_id BIGINT UNSIGNED NOT NULL,
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_course_student (course_id, student_id),
    INDEX idx_course (course_id),
    INDEX idx_student (student_id)
) ENGINE=InnoDB;

-- 资源表
CREATE TABLE resources (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    type ENUM('document', 'video', 'link', 'other') NOT NULL,
    url VARCHAR(255) NOT NULL,
    size BIGINT UNSIGNED,
    mime_type VARCHAR(100),
    course_id BIGINT UNSIGNED NOT NULL,
    uploader_id BIGINT UNSIGNED NOT NULL,
    download_count INT UNSIGNED DEFAULT 0,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (uploader_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_type (type)
) ENGINE=InnoDB;

-- 公告表
CREATE TABLE announcements (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    type ENUM('system', 'course') DEFAULT 'system',
    course_id BIGINT UNSIGNED,
    author_id BIGINT UNSIGNED NOT NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_important BOOLEAN DEFAULT FALSE,
    publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_type (type),
    INDEX idx_publish_date (publish_date)
) ENGINE=InnoDB;

-- 测试表
CREATE TABLE tests (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    course_id BIGINT UNSIGNED NOT NULL,
    creator_id BIGINT UNSIGNED NOT NULL,
    total_score DECIMAL(10,2) DEFAULT 0,
    time_limit INT, -- 分钟
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    randomize_questions BOOLEAN DEFAULT FALSE,
    show_results BOOLEAN DEFAULT TRUE,
    status ENUM('draft', 'published', 'closed') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 测试问题表
CREATE TABLE test_questions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    test_id BIGINT UNSIGNED NOT NULL,
    type ENUM('single', 'multiple', 'truefalse', 'essay') NOT NULL,
    content TEXT NOT NULL,
    answer JSON, -- 存储正确答案
    score DECIMAL(10,2) NOT NULL DEFAULT 1,
    sort_order INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
    INDEX idx_test (test_id)
) ENGINE=InnoDB;

-- 问题选项表
CREATE TABLE question_options (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    question_id BIGINT UNSIGNED NOT NULL,
    label VARCHAR(255) NOT NULL,
    value VARCHAR(255) NOT NULL,
    sort_order INT NOT NULL DEFAULT 0,
    FOREIGN KEY (question_id) REFERENCES test_questions(id) ON DELETE CASCADE,
    INDEX idx_question (question_id)
) ENGINE=InnoDB;

-- 测试提交表
CREATE TABLE test_submissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    test_id BIGINT UNSIGNED NOT NULL,
    student_id BIGINT UNSIGNED NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submit_time TIMESTAMP NULL,
    total_score DECIMAL(10,2) DEFAULT 0,
    is_fully_graded BOOLEAN DEFAULT FALSE,
    status ENUM('in_progress', 'submitted', 'graded') DEFAULT 'in_progress',
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_test_student (test_id, student_id),
    INDEX idx_test (test_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 测试答案表
CREATE TABLE test_answers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    submission_id BIGINT UNSIGNED NOT NULL,
    question_id BIGINT UNSIGNED NOT NULL,
    answer JSON NOT NULL, -- 存储学生的答案
    score DECIMAL(10,2) DEFAULT 0,
    comment TEXT,
    is_graded BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (submission_id) REFERENCES test_submissions(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES test_questions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_submission_question (submission_id, question_id),
    INDEX idx_submission (submission_id)
) ENGINE=InnoDB;

-- 讨论表
CREATE TABLE discussions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    course_id BIGINT UNSIGNED NOT NULL,
    author_id BIGINT UNSIGNED NOT NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    view_count INT UNSIGNED DEFAULT 0,
    likes INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_author (author_id),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB;

-- 讨论标签表
CREATE TABLE discussion_tags (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    discussion_id BIGINT UNSIGNED NOT NULL,
    tag VARCHAR(50) NOT NULL,
    FOREIGN KEY (discussion_id) REFERENCES discussions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_discussion_tag (discussion_id, tag)
) ENGINE=InnoDB;

-- 讨论点赞表
CREATE TABLE discussion_likes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    discussion_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (discussion_id) REFERENCES discussions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_discussion_user (discussion_id, user_id)
) ENGINE=InnoDB;

-- 讨论回复表
CREATE TABLE discussion_replies (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    discussion_id BIGINT UNSIGNED NOT NULL,
    author_id BIGINT UNSIGNED NOT NULL,
    content TEXT NOT NULL,
    likes INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (discussion_id) REFERENCES discussions(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_discussion (discussion_id),
    INDEX idx_author (author_id)
) ENGINE=InnoDB;

-- 回复点赞表
CREATE TABLE reply_likes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    reply_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reply_id) REFERENCES discussion_replies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_reply_user (reply_id, user_id)
) ENGINE=InnoDB;

-- 考勤表 (修复了空间索引问题)
CREATE TABLE attendances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    attendance_code VARCHAR(6),
    creator_id BIGINT UNSIGNED NOT NULL,
    status ENUM('active', 'closed') DEFAULT 'active',
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    location_radius INT DEFAULT 100, -- 默认100米范围
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_date (date),
    INDEX idx_status (status),
    INDEX idx_location_lat (location_lat),
    INDEX idx_location_lng (location_lng)
) ENGINE=InnoDB;

-- 学生考勤记录表
CREATE TABLE student_attendances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    attendance_id BIGINT UNSIGNED NOT NULL,
    student_id BIGINT UNSIGNED NOT NULL,
    status ENUM('present', 'late', 'absent', 'leave') DEFAULT 'absent',
    check_in_time TIMESTAMP NULL,
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (attendance_id) REFERENCES attendances(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_attendance_student (attendance_id, student_id),
    INDEX idx_attendance (attendance_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- 创建触发器：更新讨论的最后活动时间
DELIMITER //
CREATE TRIGGER update_discussion_last_activity
AFTER INSERT ON discussion_replies
FOR EACH ROW
BEGIN
    UPDATE discussions
    SET last_activity = CURRENT_TIMESTAMP
    WHERE id = NEW.discussion_id;
END //
DELIMITER ;

-- 创建触发器：更新测试总分
DELIMITER //
CREATE TRIGGER update_test_total_score
AFTER INSERT ON test_questions
FOR EACH ROW
BEGIN
    UPDATE tests t
    SET t.total_score = (
        SELECT SUM(score)
        FROM test_questions
        WHERE test_id = NEW.test_id
    )
    WHERE t.id = NEW.test_id;
END //
DELIMITER ;

-- 创建触发器：更新测试提交总分
DELIMITER //
CREATE TRIGGER update_submission_total_score
AFTER UPDATE ON test_answers
FOR EACH ROW
BEGIN
    UPDATE test_submissions s
    SET s.total_score = (
        SELECT SUM(score)
        FROM test_answers
        WHERE submission_id = NEW.submission_id
    ),
    s.is_fully_graded = NOT EXISTS (
        SELECT 1
        FROM test_answers
        WHERE submission_id = NEW.submission_id
        AND is_graded = FALSE
    ),
    s.status = IF(
        NOT EXISTS (
            SELECT 1
            FROM test_answers
            WHERE submission_id = NEW.submission_id
            AND is_graded = FALSE
        ),
        'graded',
        s.status
    )
    WHERE s.id = NEW.submission_id;
END //
DELIMITER ;

-- 创建触发器：生成课程代码
DELIMITER //
CREATE TRIGGER generate_course_code
BEFORE INSERT ON courses
FOR EACH ROW
BEGIN
    IF NEW.course_code IS NULL THEN
        SET NEW.course_code = UPPER(
            SUBSTRING(MD5(RAND()), 1, 6)
        );
    END IF;
END //
DELIMITER ;

-- 创建触发器：生成考勤码
DELIMITER //
CREATE TRIGGER generate_attendance_code
BEFORE INSERT ON attendances
FOR EACH ROW
BEGIN
    IF NEW.attendance_code IS NULL THEN
        SET NEW.attendance_code = LPAD(
            FLOOR(RAND() * 1000000), 6, '0'
        );
    END IF;
END //
DELIMITER ;
