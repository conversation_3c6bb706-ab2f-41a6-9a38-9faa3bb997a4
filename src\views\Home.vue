<template>
  <div class="home-container">
    <el-card class="welcome-card">
      <div class="welcome-header">
        <img src="../assets/logo.png" alt="Logo" class="logo">
        <h1>欢迎使用E-learning平台</h1>
      </div>
      <div class="welcome-content">
        <p>请选择您的角色进入相应的系统：</p>
        <div class="role-buttons">
          <el-button
            type="primary"
            @click="navigateTo('/admin')"
            v-if="isAdmin"
            icon="el-icon-s-tools">
            管理员系统
          </el-button>
          <el-button
            type="success"
            @click="navigateTo('/teacher')"
            v-if="isTeacher"
            icon="el-icon-s-custom">
            教师系统
          </el-button>
          <el-button
            type="warning"
            @click="navigateTo('/student')"
            v-if="isStudent"
            icon="el-icon-reading">
            学生系统
          </el-button>
        </div>
      </div>
      <div class="announcement-section">
        <h3>最新公告</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(announcement, index) in announcements"
            :key="index"
            :timestamp="formatDate(announcement.createdAt)"
            placement="top"
            :type="getTimelineItemType(index)">
            <el-card>
              <h4>{{ announcement.title }}</h4>
              <p>{{ announcement.content }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        <div class="view-more" v-if="announcements.length > 0">
          <router-link to="/announcements">查看更多公告</router-link>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data () {
    return {
      announcements: []
    }
  },
  computed: {
    isAdmin () {
      return this.$store.getters.isAdmin
    },
    isTeacher () {
      return this.$store.getters.isTeacher
    },
    isStudent () {
      return this.$store.getters.isStudent
    }
  },
  methods: {
    navigateTo (path) {
      this.$router.push(path)
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    getTimelineItemType (index) {
      const types = ['primary', 'success', 'warning', 'danger']
      return types[index % types.length]
    },
    async fetchAnnouncements () {
      try {
        // 获取最新的5条公告
        const params = { limit: 5, sort: '-createdAt' }
        await this.$store.dispatch('fetchAnnouncements', params)
        this.announcements = this.$store.state.announcements
      } catch (error) {
        console.error('获取公告失败:', error)
        this.$message.error('获取公告失败')
      }
    }
  },
  created() {
    this.fetchAnnouncements()
  }
}
</script>

<style scoped>
.home-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  padding: 20px;
}

.welcome-card {
  width: 100%;
  max-width: 800px;
}

.welcome-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
}

.welcome-content {
  text-align: center;
  margin-bottom: 30px;
}

.role-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.role-buttons .el-button {
  min-width: 150px;
}

.announcement-section {
  margin-top: 30px;
}

.announcement-section h3 {
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.view-more {
  text-align: center;
  margin-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .home-container {
    padding: 10px;
    align-items: flex-start;
    overflow-y: auto;
  }

  .welcome-card {
    margin: 20px 0;
  }

  .role-buttons {
    flex-direction: column;
  }

  .role-buttons .el-button {
    margin-bottom: 10px;
  }
}
</style>
