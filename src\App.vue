<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App',
  created() {
    // 检查用户是否已登录
    if (this.$store.getters.isAuthenticated) {
      // 根据用户角色重定向到相应的首页
      this.redirectBasedOnRole()
    }
  },
  methods: {
    redirectBasedOnRole() {
      try {
        const userRole = this.$store.getters.userRole
        const currentPath = this.$route.path

        // 如果已经在正确的路径上，不需要重定向
        if (
          (userRole === 'admin' && currentPath.startsWith('/admin')) ||
          (userRole === 'teacher' && currentPath.startsWith('/teacher')) ||
          (userRole === 'student' && currentPath.startsWith('/student'))
        ) {
          return
        }

        // 根据角色重定向
        if (userRole === 'admin') {
          this.$router.push('/admin')
        } else if (userRole === 'teacher') {
          this.$router.push('/teacher')
        } else if (userRole === 'student') {
          this.$router.push('/student')
        } else {
          // 如果角色无效，重定向到登录页
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          this.$router.push('/login')
        }
      } catch (error) {
        console.error('重定向错误:', error)
      }
    }
  }
}
</script>

<style>
/* 全局样式已移至 src/assets/css/global.css */
</style>
