<template>
  <div class="teacher-dashboard">
    <h1>教师仪表盘</h1>

    <el-row :gutter="20">
      <!-- 欢迎卡片 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card class="welcome-card">
          <div class="welcome-header">
            <img :src="userAvatar" alt="Avatar" class="avatar">
            <div class="welcome-text">
              <h2>·欢迎回来，{{ userName }}·</h2>
              <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 统计卡片 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-reading"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.courseCount }}</div>
            <div class="stat-label">我的课程</div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-user"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.studentCount }}</div>
            <div class="stat-label">学生总数</div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-document"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.resourceCount }}</div>
            <div class="stat-label">资源总数</div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-edit-outline"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.testCount }}</div>
            <div class="stat-label">测试总数</div>
          </div>
        </el-card>
      </el-col>

      <!-- 我的课程 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span>我的课程</span>
            <el-button type="text" @click="navigateTo('/teacher/courses')">管理课程</el-button>
          </div>
          <div v-if="courses.length > 0">
            <el-table :data="courses.slice(0, 3)" style="width: 100%">
              <el-table-column prop="title" label="课程名称"></el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <el-tag :type="getCourseStatusType(scope.row.status)">
                    {{ getCourseStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="student_count" label="学生数"></el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="navigateTo(`/teacher/courses/${scope.row.id}`)">
                    管理
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>您还没有创建任何课程</p>
            <el-button type="primary" size="small" @click="navigateTo('/teacher/courses')">
              创建课程
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 最新提交 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span>最新测试提交</span>
          </div>
          <div v-if="submissions.length > 0">
            <el-table :data="submissions" style="width: 100%">
              <el-table-column prop="student_name" label="学生"></el-table-column>
              <el-table-column prop="test_title" label="测试"></el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <el-tag :type="getSubmissionStatusType(scope.row.status)">
                    {{ getSubmissionStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click="navigateTo(`/teacher/courses/${scope.row.course_id}/tests/${scope.row.test_id}/submissions/${scope.row.id}`)"
                    :disabled="scope.row.status !== 'submitted'">
                    评分
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无测试提交</p>
          </div>
        </el-card>
      </el-col>

      <!-- 系统公告 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card announcement-card">
          <div slot="header" class="card-header">
            <span>系统公告</span>
          </div>
          <div v-if="announcements.length > 0">
            <el-timeline>
              <el-timeline-item
                v-for="(announcement, index) in announcements.filter(a => a.type === 'system').slice(0, 3)"
                :key="index"
                :timestamp="formatDate(announcement.publish_date)"
                placement="top"
                :type="announcement.is_important ? 'danger' : 'primary'">
                <el-card class="announcement-item" :class="{'is-pinned': announcement.is_pinned}">
                  <div class="announcement-title">
                    <span>{{ announcement.title }}</span>
                    <div>
                      <el-tag v-if="announcement.is_pinned" type="warning" size="mini">置顶</el-tag>
                      <el-tag v-if="announcement.is_important" type="danger" size="mini">重要</el-tag>
                    </div>
                  </div>
                  <div class="announcement-content">{{ announcement.content }}</div>
                  <div class="announcement-footer">
                    <span class="author">发布者: {{ announcement.author_name }}</span>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else class="empty-data">
            <p>暂无系统公告</p>
          </div>
        </el-card>
      </el-col>

      <!-- 最近活动 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span>最近活动</span>
          </div>
          <div v-if="activities.length > 0">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :timestamp="formatDate(activity.time)"
                placement="top"
                :type="getTimelineItemType(index)">
                {{ activity.content }}
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else class="empty-data">
            <p>暂无活动记录</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'TeacherDashboard',
  data() {
    return {
      courses: [],
      submissions: [],
      announcements: [],
      activities: [
        { content: '您登录了系统', time: new Date() },
        { content: '您创建了一个新的测试', time: new Date(Date.now() - 3600000) },
        { content: '学生小明提交了测试', time: new Date(Date.now() - 86400000) },
        { content: '您上传了一个新的资源', time: new Date(Date.now() - 172800000) }
      ],
      stats: {
        courseCount: 0,
        studentCount: 0,
        resourceCount: 0,
        testCount: 0
      },
      currentDate: this.formatCurrentDate()
    }
  },
  computed: {
    userName() {
      return this.$store.getters.userName || '老师'
    },
    userAvatar() {
      const user = this.$store.state.user
      return user && user.avatar ? user.avatar : require('@/assets/default-avatar.svg')
    }
  },
  methods: {
    navigateTo(path) {
      this.$router.push(path)
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatCurrentDate() {
      return this.$moment().format('YYYY年MM月DD日')
    },
    getCourseStatusType(status) {
      const types = {
        'draft': 'info',
        'published': 'success',
        'archived': 'warning'
      }
      return types[status] || 'info'
    },
    getCourseStatusText(status) {
      const texts = {
        'draft': '草稿',
        'published': '已发布',
        'archived': '已归档'
      }
      return texts[status] || status
    },
    getSubmissionStatusType(status) {
      const types = {
        'in_progress': 'info',
        'submitted': 'warning',
        'graded': 'success'
      }
      return types[status] || 'info'
    },
    getSubmissionStatusText(status) {
      const texts = {
        'in_progress': '进行中',
        'submitted': '待评分',
        'graded': '已评分'
      }
      return texts[status] || status
    },
    getTimelineItemType(index) {
      const types = ['primary', 'success', 'warning', 'danger']
      return types[index % types.length]
    },
    async fetchData() {
      try {
        // 使用模拟数据，避免API请求错误
        // 模拟课程数据
        this.courses = [
          {
            id: 1,
            title: 'Web开发基础',
            description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
            category: '计算机科学',
            status: 'published',
            student_count: 25,
            resource_count: 12,
            test_count: 3,
            start_date: '2023-09-01',
            end_date: '2024-01-15'
          },
          {
            id: 2,
            title: '数据库系统',
            description: '关系型数据库设计、SQL查询和数据库管理系统。',
            category: '计算机科学',
            status: 'published',
            student_count: 18,
            resource_count: 8,
            test_count: 2,
            start_date: '2023-09-01',
            end_date: '2024-01-15'
          }
        ];

        this.stats.courseCount = this.courses.length;

        // 计算学生总数
        this.stats.studentCount = this.courses.reduce((sum, course) => sum + (course.student_count || 0), 0);

        // 模拟测试提交数据
        this.submissions = [
          {
            id: 1,
            student_name: '张三',
            test_title: 'JavaScript基础测试',
            course_id: 1,
            test_id: 1,
            status: 'submitted',
            submit_time: '2023-09-15T10:30:00Z'
          },
          {
            id: 2,
            student_name: '李四',
            test_title: 'CSS布局测试',
            course_id: 1,
            test_id: 2,
            status: 'graded',
            submit_time: '2023-09-14T14:20:00Z',
            score: 85
          },
          {
            id: 3,
            student_name: '王五',
            test_title: 'SQL基础测试',
            course_id: 2,
            test_id: 3,
            status: 'submitted',
            submit_time: '2023-09-15T09:10:00Z'
          }
        ];

        // 获取公告数据
        try {
          const response = await this.$http.get('/api/announcements', {
            params: {
              limit: 5
            }
          });
          if (response.data.success) {
            this.announcements = response.data.data || [];
          } else {
            throw new Error('获取公告失败');
          }
        } catch (error) {
          console.error('获取公告数据失败:', error);
          // 使用空数组作为备份
          this.announcements = [];
        }

        // 模拟统计数据
        this.stats = {
          ...this.stats,
          resourceCount: 20,
          testCount: 5
        };
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败，请稍后再试')
      }
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style scoped>
.teacher-dashboard {
  padding: 20px;
}

.dashboard-card {
  margin-bottom: 20px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-card {
  margin-bottom: 20px;
  background-color: #f0f9eb;
}

.welcome-header {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-right: 20px;
}

.welcome-text h2 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #67c23a;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 20px;
  height: 100%;
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
  color: #409EFF;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
}

.empty-data {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}

.announcement-card {
  margin-bottom: 20px;
}

.announcement-item {
  border-left: 4px solid #409EFF;
}

.announcement-item.is-pinned {
  border-left: 4px solid #E6A23C;
  background-color: #fdf6ec;
}

.announcement-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
}

.announcement-content {
  margin-bottom: 10px;
  line-height: 1.5;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 12px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .welcome-header {
    flex-direction: column;
    text-align: center;
  }

  .avatar {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .stat-card {
    margin-bottom: 15px;
  }
}
</style>
