<template>
  <div class="course-overview">
    <el-row :gutter="20">
      <!-- 课程公告 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="overview-card">
          <div slot="header" class="card-header">
            <span>课程公告</span>
          </div>
          <div v-if="announcements.length > 0">
            <el-timeline>
              <el-timeline-item
                v-for="(announcement, index) in announcements"
                :key="index"
                :timestamp="formatDate(announcement.publish_date)"
                placement="top"
                :type="announcement.is_important ? 'danger' : 'primary'">
                <el-card>
                  <h4>
                    <el-tag type="danger" v-if="announcement.is_important">重要</el-tag>
                    {{ announcement.title }}
                  </h4>
                  <p>{{ announcement.content }}</p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else class="empty-data">
            <p>暂无课程公告</p>
          </div>
        </el-card>
      </el-col>
      
      <!-- 即将到来的测试 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="overview-card">
          <div slot="header" class="card-header">
            <span>即将到来的测试</span>
            <el-button type="text" @click="navigateToTests">查看全部</el-button>
          </div>
          <div v-if="upcomingTests.length > 0">
            <el-table :data="upcomingTests" style="width: 100%">
              <el-table-column prop="title" label="测试名称"></el-table-column>
              <el-table-column prop="start_time" label="开始时间">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.start_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="end_time" label="结束时间">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.end_time) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button 
                    type="text" 
                    size="small" 
                    @click="viewTest(scope.row)"
                    :disabled="!isTestAvailable(scope.row)">
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无即将到来的测试</p>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最新资源 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="overview-card">
          <div slot="header" class="card-header">
            <span>最新资源</span>
            <el-button type="text" @click="navigateToResources">查看全部</el-button>
          </div>
          <div v-if="resources.length > 0">
            <el-table :data="resources" style="width: 100%">
              <el-table-column prop="title" label="资源名称"></el-table-column>
              <el-table-column prop="type" label="类型" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getResourceTypeType(scope.row.type)">
                    {{ getResourceTypeText(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="downloadResource(scope.row)">
                    下载
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无课程资源</p>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最新讨论 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="overview-card">
          <div slot="header" class="card-header">
            <span>最新讨论</span>
            <el-button type="text" @click="navigateToDiscussions">查看全部</el-button>
          </div>
          <div v-if="discussions.length > 0">
            <el-table :data="discussions" style="width: 100%">
              <el-table-column prop="title" label="讨论主题"></el-table-column>
              <el-table-column prop="author_name" label="发起人" width="100"></el-table-column>
              <el-table-column prop="created_at" label="发布时间" width="150">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无课程讨论</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'CourseOverview',
  props: {
    course: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      announcements: [],
      upcomingTests: [],
      resources: [],
      discussions: []
    }
  },
  methods: {
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatDateTime(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    getResourceTypeType(type) {
      const types = {
        'document': 'primary',
        'video': 'success',
        'link': 'info',
        'other': 'warning'
      }
      return types[type] || 'info'
    },
    getResourceTypeText(type) {
      const texts = {
        'document': '文档',
        'video': '视频',
        'link': '链接',
        'other': '其他'
      }
      return texts[type] || type
    },
    navigateToTests() {
      this.$emit('navigate', 'tests')
    },
    navigateToResources() {
      this.$emit('navigate', 'resources')
    },
    navigateToDiscussions() {
      this.$emit('navigate', 'discussions')
    },
    viewTest(test) {
      this.$router.push(`/student/courses/${this.course.id}/test/${test.id}`)
    },
    downloadResource(resource) {
      // 实际应该调用API下载资源
      this.$message.success(`开始下载资源: ${resource.title}`)
      window.open(resource.url, '_blank')
    },
    isTestAvailable(test) {
      const now = new Date()
      return new Date(test.start_time) <= now && new Date(test.end_time) >= now
    },
    async fetchData() {
      try {
        // 使用模拟数据
        // 课程公告
        this.announcements = [
          {
            id: 1,
            title: 'Web开发课程开始',
            content: 'Web开发基础课程将于下周一开始，请所有学生提前准备好开发环境。',
            is_important: true,
            publish_date: '2023-09-05T10:30:00Z'
          },
          {
            id: 2,
            title: 'JavaScript作业提交',
            content: '请在本周五前提交JavaScript基础作业，提交方式为上传到课程资源区。',
            is_important: false,
            publish_date: '2023-09-10T14:00:00Z'
          }
        ]
        
        // 即将到来的测试
        this.upcomingTests = [
          {
            id: 1,
            title: 'JavaScript基础测试',
            start_time: new Date(Date.now() - 3600000 * 24),
            end_time: new Date(Date.now() + 3600000 * 24)
          },
          {
            id: 2,
            title: 'CSS布局测试',
            start_time: new Date(Date.now() + 3600000 * 48),
            end_time: new Date(Date.now() + 3600000 * 72)
          }
        ]
        
        // 最新资源
        this.resources = [
          {
            id: 1,
            title: 'JavaScript基础教程',
            type: 'document',
            url: '#',
            created_at: '2023-09-05T10:30:00Z'
          },
          {
            id: 2,
            title: 'HTML5视频教程',
            type: 'video',
            url: '#',
            created_at: '2023-09-10T14:00:00Z'
          },
          {
            id: 3,
            title: 'CSS参考手册',
            type: 'link',
            url: 'https://developer.mozilla.org/zh-CN/docs/Web/CSS/Reference',
            created_at: '2023-09-15T09:00:00Z'
          }
        ]
        
        // 最新讨论
        this.discussions = [
          {
            id: 1,
            title: 'JavaScript中的闭包如何理解？',
            author_name: '张三',
            created_at: '2023-09-10T14:00:00Z'
          },
          {
            id: 2,
            title: 'CSS Flexbox布局问题',
            author_name: '李四',
            created_at: '2023-09-15T09:00:00Z'
          }
        ]
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败，请稍后再试')
      }
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style scoped>
.course-overview {
  padding: 10px 0;
}

.overview-card {
  margin-bottom: 20px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-data {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}
</style>
