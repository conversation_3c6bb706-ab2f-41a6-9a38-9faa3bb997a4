<template>
  <div class="dashboard-container student-dashboard">
    <div class="dashboard-title">
      <i class="el-icon-s-home"></i>
      <span>学生仪表盘</span>
    </div>

    <el-row :gutter="20">
      <!-- 欢迎卡片 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <div class="welcome-card">
          <div class="welcome-header">
            <img :src="userAvatar" alt="Avatar" class="avatar">
            <div class="welcome-text">
              <h2>欢迎回来，{{ userName }}</h2>
              <p>今天是 {{ currentDate }}，祝您学习愉快！</p>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 统计卡片 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="stat-card primary">
          <div class="stat-icon">
            <i class="el-icon-reading"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ courses.length }}</div>
            <div class="stat-label">我的课程</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="stat-card warning">
          <div class="stat-icon">
            <i class="el-icon-edit-outline"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ pendingTests.length }}</div>
            <div class="stat-label">待完成测试</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="stat-card success">
          <div class="stat-icon">
            <i class="el-icon-document-checked"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ completedTestsCount }}</div>
            <div class="stat-label">已完成测试</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="stat-card info">
          <div class="stat-icon">
            <i class="el-icon-bell"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ announcements.length }}</div>
            <div class="stat-label">公告数量</div>
          </div>
        </div>
      </el-col>

      <!-- 系统公告 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <div class="card-header-title">
              <i class="el-icon-bell"></i>
              <span>系统公告</span>
            </div>
            <el-button type="primary" size="small" @click="navigateTo('/announcements')">查看全部</el-button>
          </div>
          <div class="card-body">
            <div v-if="announcements.length > 0">
              <el-timeline>
                <el-timeline-item
                  v-for="(announcement, index) in announcements.filter(a => a.type === 'system').slice(0, 3)"
                  :key="index"
                  :timestamp="formatDate(announcement.publish_date)"
                  placement="top"
                  :type="announcement.is_important ? 'danger' : 'primary'">
                  <el-card class="announcement-item" :class="{'is-pinned': announcement.is_pinned}">
                    <div class="announcement-title">
                      <span>{{ announcement.title }}</span>
                      <div>
                        <el-tag v-if="announcement.is_pinned" type="warning" size="mini">置顶</el-tag>
                        <el-tag v-if="announcement.is_important" type="danger" size="mini">重要</el-tag>
                      </div>
                    </div>
                    <div class="announcement-content">{{ announcement.content }}</div>
                    <div class="announcement-footer">
                      <span class="author">发布者: {{ announcement.author_name }}</span>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div v-else class="empty-data">
              <i class="el-icon-bell"></i>
              <p>暂无系统公告</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 我的课程 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <div class="card-header-title">
              <i class="el-icon-reading"></i>
              <span>我的课程</span>
            </div>
            <el-button type="primary" size="small" @click="navigateTo('/student/courses')">查看全部</el-button>
          </div>
          <div class="card-body">
            <div v-if="courses.length > 0">
              <el-table :data="courses.slice(0, 3)" style="width: 100%" stripe>
                <el-table-column prop="title" label="课程名称"></el-table-column>
                <el-table-column prop="teacher_name" label="教师"></el-table-column>
                <el-table-column label="操作" width="120" align="center">
                  <template slot-scope="scope">
                    <el-button type="primary" size="mini" @click="navigateTo(`/student/courses/${scope.row.id}`)">
                      查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="empty-data">
              <i class="el-icon-reading"></i>
              <p>您还没有选修任何课程</p>
              <el-button type="primary" size="small" @click="navigateTo('/student/courses')">
                浏览课程
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 课程公告 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <div class="card-header-title">
              <i class="el-icon-message"></i>
              <span>课程公告</span>
            </div>
            <el-button type="primary" size="small" @click="navigateTo('/announcements')">查看全部</el-button>
          </div>
          <div class="card-body">
            <div v-if="announcements.filter(a => a.type === 'course').length > 0">
              <el-timeline>
                <el-timeline-item
                  v-for="(announcement, index) in announcements.filter(a => a.type === 'course').slice(0, 3)"
                  :key="index"
                  :timestamp="formatDate(announcement.publish_date)"
                  placement="top"
                  :type="getTimelineItemType(index)">
                  <el-card class="announcement-item" :class="{'is-pinned': announcement.is_pinned}">
                    <div class="announcement-title">
                      <span>{{ announcement.title }}</span>
                      <div>
                        <el-tag v-if="announcement.is_pinned" type="warning" size="mini">置顶</el-tag>
                        <el-tag v-if="announcement.is_important" type="danger" size="mini">重要</el-tag>
                      </div>
                    </div>
                    <div class="announcement-content">{{ announcement.content }}</div>
                    <div class="announcement-footer">
                      <span class="author">发布者: {{ announcement.author_name }}</span>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div v-else class="empty-data">
              <i class="el-icon-message"></i>
              <p>暂无课程公告</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 待完成测试 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <div class="card-header-title">
              <i class="el-icon-edit-outline"></i>
              <span>待完成测试</span>
            </div>
            <el-button type="primary" size="small" @click="navigateTo('/student/tests')">查看全部</el-button>
          </div>
          <div class="card-body">
            <div v-if="pendingTests.length > 0">
              <el-table :data="pendingTests.slice(0, 3)" style="width: 100%" stripe>
                <el-table-column prop="title" label="测试名称"></el-table-column>
                <el-table-column prop="course_title" label="所属课程"></el-table-column>
                <el-table-column label="截止时间" width="180">
                  <template slot-scope="scope">
                    <div>{{ formatDate(scope.row.end_time) }}</div>
                    <div>
                      <el-tag
                        :type="getTimeRemainingType(scope.row.end_time)"
                        size="mini">
                        {{ getTimeRemaining(scope.row.end_time) }}
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" align="center">
                  <template slot-scope="scope">
                    <el-button type="primary" size="mini" @click="navigateTo(`/student/courses/${scope.row.course_id}/test/${scope.row.id}`)">
                      开始
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="empty-data">
              <i class="el-icon-edit-outline"></i>
              <p>暂无待完成测试</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最近活动 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <div class="card-header-title">
              <i class="el-icon-time"></i>
              <span>最近活动</span>
            </div>
          </div>
          <div class="card-body">
            <div v-if="activities.length > 0">
              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in activities"
                  :key="index"
                  :timestamp="formatDate(activity.time)"
                  placement="top"
                  :type="getTimelineItemType(index)">
                  {{ activity.content }}
                </el-timeline-item>
              </el-timeline>
            </div>
            <div v-else class="empty-data">
              <i class="el-icon-time"></i>
              <p>暂无活动记录</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'StudentDashboard',
  data() {
    return {
      courses: [],
      announcements: [],
      pendingTests: [],
      completedTests: [],
      activities: [
        { content: '您登录了系统', time: new Date() },
        { content: '您查看了课程列表', time: new Date(Date.now() - 3600000) },
        { content: '您完成了一次测试', time: new Date(Date.now() - 86400000) }
      ],
      currentDate: this.formatCurrentDate()
    }
  },
  computed: {
    userName() {
      return this.$store.getters.userName || '同学'
    },
    userAvatar() {
      const user = this.$store.state.user
      return user && user.avatar ? user.avatar : require('@/assets/default-avatar.svg')
    },
    completedTestsCount() {
      return this.completedTests.length || 5 // 默认值为5
    }
  },
  methods: {
    navigateTo(path) {
      this.$router.push(path)
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatCurrentDate() {
      return this.$moment().format('YYYY年MM月DD日')
    },
    formatEndTime(row, column, cellValue) {
      return this.$moment(cellValue).format('YYYY-MM-DD HH:mm')
    },
    getTimelineItemType(index) {
      const types = ['primary', 'success', 'warning', 'danger']
      return types[index % types.length]
    },
    getTimeRemainingType(endTime) {
      const now = new Date()
      const end = new Date(endTime)
      const diffHours = (end - now) / (1000 * 60 * 60)

      if (diffHours < 24) {
        return 'danger'
      } else if (diffHours < 72) {
        return 'warning'
      } else {
        return 'primary'
      }
    },
    getTimeRemaining(endTime) {
      const now = new Date()
      const end = new Date(endTime)
      const diffMs = end - now

      if (diffMs < 0) {
        return '已截止'
      }

      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

      if (diffDays > 0) {
        return `剩余 ${diffDays} 天 ${diffHours} 小时`
      } else {
        return `剩余 ${diffHours} 小时`
      }
    },
    async fetchData() {
      try {
        // 获取课程数据
        try {
          const response = await this.$http.get('/api/courses/enrolled');
          if (response.data.success) {
            this.courses = response.data.data || [];
          }
        } catch (error) {
          console.error('获取课程数据失败:', error);
          // 使用模拟数据作为备份
          this.courses = [
            {
              id: 1,
              title: 'Web开发基础',
              description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
              teacher_name: '张教授',
              category: '计算机科学',
              start_date: '2023-09-01',
              end_date: '2024-01-15'
            },
            {
              id: 2,
              title: '数据库系统',
              description: '关系型数据库设计、SQL查询和数据库管理系统。',
              teacher_name: '李教授',
              category: '计算机科学',
              start_date: '2023-09-01',
              end_date: '2024-01-15'
            }
          ];
        }

        // 获取公告数据
        try {
          const response = await this.$http.get('/api/announcements', {
            params: {
              limit: 5
            }
          });
          if (response.data.success) {
            this.announcements = response.data.data || [];
          } else {
            throw new Error('获取公告失败');
          }
        } catch (error) {
          console.error('获取公告数据失败:', error);
          // 使用模拟数据作为备份
          this.announcements = [];
        }

        // 获取待完成测试数据
        try {
          const response = await this.$http.get('/api/tests/pending');
          if (response.data.success) {
            this.pendingTests = response.data.data || [];
          }
        } catch (error) {
          console.error('获取待完成测试数据失败:', error);
          // 使用模拟数据作为备份
          this.pendingTests = [
            {
              id: 1,
              title: 'JavaScript基础测试',
              course_id: 1,
              course_title: 'Web开发基础',
              end_time: new Date(Date.now() + 86400000 * 3) // 3天后
            },
            {
              id: 2,
              title: 'CSS布局测试',
              course_id: 1,
              course_title: 'Web开发基础',
              end_time: new Date(Date.now() + 86400000 * 5) // 5天后
            }
          ];
        }

        // 模拟活动数据
        this.activities = [
          { content: '您登录了系统', time: new Date() },
          { content: '您查看了课程列表', time: new Date(Date.now() - 3600000) },
          { content: '您完成了一次测试', time: new Date(Date.now() - 86400000) }
        ];
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败，请稍后再试')
      }
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style scoped>
@import '../../assets/css/dashboard.css';

/* 学生仪表盘特定样式 */
.student-dashboard .stat-card.primary .stat-icon {
  color: #409EFF;
}

.student-dashboard .stat-card.success .stat-icon {
  color: #67C23A;
}

.student-dashboard .stat-card.warning .stat-icon {
  color: #E6A23C;
}

.student-dashboard .stat-card.info .stat-icon {
  color: #909399;
}

.student-dashboard .dashboard-title {
  color: #409EFF;
}
</style>
