const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect, authorize } = require('../middleware/auth');

// 获取公告列表
router.get('/', protect, async (req, res) => {
  try {
    const { search, type, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let query = `
      SELECT a.*, u.username as author_name
      FROM announcements a
      JOIN users u ON a.author_id = u.id
    `;
    const queryParams = [];

    // 添加搜索条件
    if (search) {
      query += ' WHERE (a.title LIKE ? OR a.content LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加类型过滤
    if (type) {
      if (search) {
        query += ' AND a.type = ?';
      } else {
        query += ' WHERE a.type = ?';
      }
      queryParams.push(type);
    }

    // 添加排序和分页
    query += ' ORDER BY a.is_pinned DESC, a.publish_date DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [rows] = await pool.query(query, queryParams);

    // 获取课程信息
    for (let i = 0; i < rows.length; i++) {
      if (rows[i].course_id) {
        const [courseRows] = await pool.query(
          'SELECT id, title FROM courses WHERE id = ?',
          [rows[i].course_id]
        );
        if (courseRows.length > 0) {
          rows[i].course = courseRows[0];
        }
      }
    }

    // 获取总数
    let countQuery = 'SELECT COUNT(*) as total FROM announcements';
    const countParams = [];

    if (search) {
      countQuery += ' WHERE (title LIKE ? OR content LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`);
    }

    if (type) {
      if (search) {
        countQuery += ' AND type = ?';
      } else {
        countQuery += ' WHERE type = ?';
      }
      countParams.push(type);
    }

    const [countRows] = await pool.query(countQuery, countParams);
    const total = countRows[0].total;

    res.status(200).json({
      success: true,
      count: rows.length,
      total,
      data: rows
    });
  } catch (error) {
    console.error('获取公告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公告失败',
      error: error.message
    });
  }
});

// 获取单个公告
router.get('/:id', protect, async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT a.*, u.username as author_name
       FROM announcements a
       JOIN users u ON a.author_id = u.id
       WHERE a.id = ?`,
      [req.params.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    const announcement = rows[0];

    // 获取课程信息
    if (announcement.course_id) {
      const [courseRows] = await pool.query(
        'SELECT id, title FROM courses WHERE id = ?',
        [announcement.course_id]
      );
      if (courseRows.length > 0) {
        announcement.course = courseRows[0];
      }
    }

    res.status(200).json({
      success: true,
      data: announcement
    });
  } catch (error) {
    console.error('获取公告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公告失败',
      error: error.message
    });
  }
});

// 创建公告 (仅管理员和教师)
router.post('/', protect, authorize('admin', 'teacher'), async (req, res) => {
  try {
    const { title, content, type, course_id, is_pinned, is_important } = req.body;

    // 验证必要字段
    if (!title || !content || !type) {
      return res.status(400).json({
        success: false,
        message: '请提供标题、内容和类型'
      });
    }

    // 如果是课程公告，验证课程存在
    if (type === 'course' && course_id) {
      const [courseRows] = await pool.query(
        'SELECT * FROM courses WHERE id = ?',
        [course_id]
      );

      if (courseRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '课程不存在'
        });
      }

      // 如果是教师，验证是否为该课程的教师
      if (req.user.role === 'teacher' && courseRows[0].teacher_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '您不是该课程的教师，无权发布课程公告'
        });
      }
    }

    // 插入公告
    const [result] = await pool.query(
      `INSERT INTO announcements (
        title, content, type, course_id, author_id, 
        is_pinned, is_important, publish_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        title,
        content,
        type,
        type === 'course' ? course_id : null,
        req.user.id,
        is_pinned ? 1 : 0,
        is_important ? 1 : 0
      ]
    );

    // 获取新创建的公告
    const [announcementRows] = await pool.query(
      `SELECT a.*, u.username as author_name
       FROM announcements a
       JOIN users u ON a.author_id = u.id
       WHERE a.id = ?`,
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '公告创建成功',
      data: announcementRows[0]
    });
  } catch (error) {
    console.error('创建公告失败:', error);
    res.status(500).json({
      success: false,
      message: '创建公告失败',
      error: error.message
    });
  }
});

// 更新公告 (仅管理员和原作者)
router.put('/:id', protect, async (req, res) => {
  try {
    const { title, content, type, course_id, is_pinned, is_important } = req.body;
    const announcementId = req.params.id;

    // 验证公告存在
    const [announcementRows] = await pool.query(
      'SELECT * FROM announcements WHERE id = ?',
      [announcementId]
    );

    if (announcementRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    const announcement = announcementRows[0];

    // 验证权限 (仅管理员和原作者可以更新)
    if (req.user.role !== 'admin' && announcement.author_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您没有权限更新此公告'
      });
    }

    // 如果是课程公告，验证课程存在
    if (type === 'course' && course_id) {
      const [courseRows] = await pool.query(
        'SELECT * FROM courses WHERE id = ?',
        [course_id]
      );

      if (courseRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '课程不存在'
        });
      }

      // 如果是教师，验证是否为该课程的教师
      if (req.user.role === 'teacher' && courseRows[0].teacher_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: '您不是该课程的教师，无权更新此课程公告'
        });
      }
    }

    // 更新公告
    await pool.query(
      `UPDATE announcements
       SET title = ?, content = ?, type = ?, course_id = ?,
           is_pinned = ?, is_important = ?, updated_at = NOW()
       WHERE id = ?`,
      [
        title,
        content,
        type,
        type === 'course' ? course_id : null,
        is_pinned ? 1 : 0,
        is_important ? 1 : 0,
        announcementId
      ]
    );

    // 获取更新后的公告
    const [updatedRows] = await pool.query(
      `SELECT a.*, u.username as author_name
       FROM announcements a
       JOIN users u ON a.author_id = u.id
       WHERE a.id = ?`,
      [announcementId]
    );

    res.status(200).json({
      success: true,
      message: '公告更新成功',
      data: updatedRows[0]
    });
  } catch (error) {
    console.error('更新公告失败:', error);
    res.status(500).json({
      success: false,
      message: '更新公告失败',
      error: error.message
    });
  }
});

// 更新公告置顶状态 (仅管理员和原作者)
router.patch('/:id/pin', protect, async (req, res) => {
  try {
    const { is_pinned } = req.body;
    const announcementId = req.params.id;

    // 验证公告存在
    const [announcementRows] = await pool.query(
      'SELECT * FROM announcements WHERE id = ?',
      [announcementId]
    );

    if (announcementRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    const announcement = announcementRows[0];

    // 验证权限 (仅管理员和原作者可以更新)
    if (req.user.role !== 'admin' && announcement.author_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您没有权限更新此公告'
      });
    }

    // 更新公告置顶状态
    await pool.query(
      'UPDATE announcements SET is_pinned = ?, updated_at = NOW() WHERE id = ?',
      [is_pinned ? 1 : 0, announcementId]
    );

    res.status(200).json({
      success: true,
      message: is_pinned ? '公告已置顶' : '公告已取消置顶'
    });
  } catch (error) {
    console.error('更新公告置顶状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新公告置顶状态失败',
      error: error.message
    });
  }
});

// 删除公告 (仅管理员和原作者)
router.delete('/:id', protect, async (req, res) => {
  try {
    const announcementId = req.params.id;

    // 验证公告存在
    const [announcementRows] = await pool.query(
      'SELECT * FROM announcements WHERE id = ?',
      [announcementId]
    );

    if (announcementRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    const announcement = announcementRows[0];

    // 验证权限 (仅管理员和原作者可以删除)
    if (req.user.role !== 'admin' && announcement.author_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您没有权限删除此公告'
      });
    }

    // 删除公告
    await pool.query('DELETE FROM announcements WHERE id = ?', [announcementId]);

    res.status(200).json({
      success: true,
      message: '公告删除成功'
    });
  } catch (error) {
    console.error('删除公告失败:', error);
    res.status(500).json({
      success: false,
      message: '删除公告失败',
      error: error.message
    });
  }
});

module.exports = router;
