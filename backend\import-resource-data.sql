-- 导入资源数据
USE elearning;

-- 插入资源数据
INSERT INTO resources (title, description, url, type, size, course_id, uploader_id, is_public, download_count, created_at) VALUES
-- Web开发基础课程资源
('HTML5基础教程', 'HTML5的基本语法和标签使用指南',
 'uploads/resources/html5_basics.pdf', 'document', 1024000,
 2, 2, TRUE, 45, NOW()),

('CSS3样式指南', 'CSS3的选择器、样式属性和布局技术详解',
 'uploads/resources/css3_guide.pdf', 'document', 1536000,
 2, 2, TRUE, 38, NOW()),

('响应式设计实例', '使用媒体查询和弹性布局实现响应式网页设计',
 'uploads/resources/responsive_design.zip', 'other', 2048000,
 2, 2, TRUE, 27, NOW()),

('Web开发工具介绍', '常用的Web开发工具和环境配置指南',
 'uploads/resources/web_dev_tools.pptx', 'document', 3072000,
 2, 2, TRUE, 32, NOW()),

('JavaScript入门视频', 'JavaScript基础语法和DOM操作教学视频',
 'uploads/resources/js_intro.mp4', 'video', 15360000,
 2, 2, TRUE, 56, NOW()),

-- 数据库系统课程资源
('SQL基础教程', 'SQL语言基础和常用查询语句详解',
 'uploads/resources/sql_basics.pdf', 'document', 1228800,
 3, 2, TRUE, 41, NOW()),

('数据库设计原则', '数据库范式和设计最佳实践指南',
 'uploads/resources/db_design.pdf', 'document', 1638400,
 3, 2, TRUE, 35, NOW()),

('MySQL安装配置指南', 'MySQL数据库服务器的安装和基本配置',
 'uploads/resources/mysql_setup.pdf', 'document', 921600,
 3, 2, TRUE, 29, NOW()),

('数据库事务与并发控制', '数据库事务ACID特性和并发控制机制详解',
 'uploads/resources/db_transactions.pptx', 'document', 2457600,
 3, 2, TRUE, 26, NOW()),

('数据库索引优化', '数据库索引类型和查询优化技术',
 'uploads/resources/db_indexing.pdf', 'document', 1843200,
 3, 2, TRUE, 33, NOW()),

-- 高级Web开发课程资源
('React基础教程', 'React框架基础概念和组件开发指南',
 'uploads/resources/react_basics.pdf', 'document', 1536000,
 4, 2, TRUE, 48, NOW()),

('Node.js后端开发', 'Node.js环境搭建和Express框架使用指南',
 'uploads/resources/nodejs_backend.pdf', 'document', 1843200,
 4, 2, TRUE, 42, NOW()),

('RESTful API设计', 'RESTful API设计原则和最佳实践',
 'uploads/resources/restful_api.pdf', 'document', 1228800,
 4, 2, TRUE, 37, NOW()),

('前端构建工具', 'Webpack、Babel等前端构建工具使用指南',
 'uploads/resources/frontend_tools.pptx', 'document', 2764800,
 4, 2, TRUE, 31, NOW()),

('全栈开发实例项目', '使用MERN(MongoDB, Express, React, Node.js)栈的完整项目示例',
 'uploads/resources/fullstack_project.zip', 'other', 5120000,
 4, 2, TRUE, 53, NOW()),

-- 人工智能导论课程资源
('机器学习基础', '机器学习的基本概念和算法介绍',
 'uploads/resources/ml_basics.pdf', 'document', 2048000,
 1, 2, TRUE, 51, NOW()),

('Python数据分析', '使用Python进行数据处理和分析的教程',
 'uploads/resources/python_data.pdf', 'document', 1843200,
 1, 2, TRUE, 47, NOW()),

('神经网络入门', '人工神经网络的基本结构和工作原理',
 'uploads/resources/neural_networks.pptx', 'document', 3072000,
 1, 2, TRUE, 44, NOW()),

('深度学习框架比较', 'TensorFlow、PyTorch等主流深度学习框架对比',
 'uploads/resources/dl_frameworks.pdf', 'document', 1638400,
 1, 2, TRUE, 39, NOW()),

('AI伦理与社会影响', '人工智能的伦理问题和社会影响分析',
 'uploads/resources/ai_ethics.pdf', 'document', 1228800,
 1, 2, TRUE, 36, NOW()),

-- 网络安全基础课程资源
('网络安全概述', '网络安全的基本概念和威胁类型',
 'uploads/resources/security_overview.pdf', 'document', 1433600,
 5, 2, TRUE, 43, NOW()),

('密码学基础', '现代密码学的基本原理和常用算法',
 'uploads/resources/cryptography.pdf', 'document', 1638400,
 5, 2, TRUE, 38, NOW()),

('Web应用安全', 'Web应用常见漏洞和防护措施',
 'uploads/resources/web_security.pdf', 'document', 1843200,
 5, 2, TRUE, 41, NOW()),

('网络攻防实战', '常见网络攻击手段和防御策略',
 'uploads/resources/network_defense.pptx', 'document', 2560000,
 5, 2, TRUE, 46, NOW()),

('安全编码实践', '软件开发中的安全编码规范和最佳实践',
 'uploads/resources/secure_coding.pdf', 'document', 1536000,
 5, 2, TRUE, 35, NOW());
