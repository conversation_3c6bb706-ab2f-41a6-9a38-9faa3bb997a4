console.log('开始测试...');

try {
  const mysql = require('mysql2/promise');
  console.log('✅ mysql2模块加载成功');
  
  // 创建连接
  const connection = mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'elearning'
  });
  
  console.log('✅ 连接对象创建成功');
  
  // 测试连接
  connection.connect()
    .then(() => {
      console.log('✅ 数据库连接成功');
      return connection.query('SHOW TABLES LIKE "announcements"');
    })
    .then(([rows]) => {
      console.log('announcements表存在:', rows.length > 0);
      if (rows.length === 0) {
        console.log('❌ announcements表不存在');
      } else {
        console.log('✅ announcements表存在');
        return connection.query('SELECT COUNT(*) as count FROM announcements');
      }
    })
    .then((result) => {
      if (result) {
        console.log('公告数量:', result[0][0].count);
      }
      connection.end();
      console.log('✅ 测试完成');
    })
    .catch((error) => {
      console.error('❌ 错误:', error.message);
      connection.end();
    });
    
} catch (error) {
  console.error('❌ 模块加载错误:', error.message);
}
