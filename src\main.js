import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'
import moment from 'moment'
import 'moment/locale/zh-cn'
import './assets/css/global.css'

// 配置Element UI
Vue.use(ElementUI, { size: 'small' })

// 配置Axios
axios.defaults.baseURL = 'http://localhost:5000'
// 请求拦截器，添加token到请求头
axios.interceptors.request.use(config => {
  console.log('请求配置:', config)
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})
// 响应拦截器，处理错误
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('token')
      router.push('/login')
    }
    return Promise.reject(error)
  }
)
Vue.prototype.$http = axios

// 配置moment
moment.locale('zh-cn')
Vue.prototype.$moment = moment

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
