<template>
  <div class="teacher-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="aside">
        <div class="logo-container">
          <img src="@/assets/logo.svg" alt="Logo" class="logo" v-if="!isCollapse">
          <img src="@/assets/logo-small.svg" alt="Logo" class="logo-small" v-else>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          :collapse="isCollapse"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF">
          <el-menu-item index="/teacher" @click="$router.push('/teacher')">
            <i class="el-icon-s-home"></i>
            <span slot="title">仪表盘</span>
          </el-menu-item>
          <el-menu-item index="/teacher/courses" @click="$router.push('/teacher/courses')">
            <i class="el-icon-reading"></i>
            <span slot="title">我的课程</span>
          </el-menu-item>
          <el-menu-item index="/teacher/resources" @click="$router.push('/teacher/resources')">
            <i class="el-icon-folder"></i>
            <span slot="title">资源管理</span>
          </el-menu-item>
          <el-menu-item index="/teacher/tests" @click="$router.push('/teacher/tests')">
            <i class="el-icon-edit-outline"></i>
            <span slot="title">测试管理</span>
          </el-menu-item>
          <el-submenu index="announcements">
            <template slot="title">
              <i class="el-icon-bell"></i>
              <span>公告管理</span>
            </template>
            <el-menu-item index="/announcements" @click="$router.push('/announcements')">
              查看公告
            </el-menu-item>
          </el-submenu>
        </el-menu>
      </el-aside>

      <!-- 主要内容区域 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-left">
            <i
              :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
              @click="toggleSidebar"
              class="collapse-btn">
            </i>
            <breadcrumb />
          </div>
          <div class="header-right">
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="user-dropdown">
                <el-avatar :size="32" :src="userAvatar"></el-avatar>
                <span class="username">{{ userName }}</span>
                <i class="el-icon-arrow-down"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 内容区域 -->
        <el-main class="main">
          <router-view />
        </el-main>

        <!-- 页脚 -->
        <el-footer class="footer">
          <p>© {{ new Date().getFullYear() }} E-learning平台 | 教师系统</p>
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import Breadcrumb from '@/components/common/Breadcrumb.vue'

export default {
  name: 'TeacherLayout',
  components: {
    Breadcrumb
  },
  data() {
    return {
      isCollapse: false
    }
  },
  computed: {
    activeMenu() {
      return this.$route.path
    },
    userName() {
      return this.$store.getters.userName || '教师'
    },
    userAvatar() {
      const user = this.$store.state.user
      return user && user.avatar ? user.avatar : require('@/assets/default-avatar.svg')
    }
  },
  methods: {
    toggleSidebar() {
      this.isCollapse = !this.isCollapse
    },
    handleCommand(command) {
      if (command === 'logout') {
        this.$confirm('确定要退出登录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 显示加载中提示
          const loading = this.$loading({
            lock: true,
            text: '正在退出...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 延迟执行退出操作，确保UI状态更新
          setTimeout(() => {
            this.$store.dispatch('logout')
            loading.close();
          }, 500);
        }).catch(() => {})
      } else if (command === 'profile') {
        this.$router.push('/profile')
      }
    }
  }
}
</script>

<style scoped>
.teacher-layout {
  height: 100vh;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
}

.logo-container {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #263445;
}

.logo {
  height: 40px;
}

.logo-small {
  height: 30px;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 200px;
}

.header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 10px;
}

.main {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
}

.footer {
  text-align: center;
  background-color: #fff;
  color: #666;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .aside {
    position: fixed;
    z-index: 1000;
    height: 100%;
  }

  .header {
    padding: 0 10px;
  }

  .username {
    display: none;
  }

  .main {
    padding: 10px;
  }
}
</style>
