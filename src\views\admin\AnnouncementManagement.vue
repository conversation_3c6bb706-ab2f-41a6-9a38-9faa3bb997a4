<template>
  <div class="announcement-management">
    <div class="page-header">
      <h1>公告管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <i class="el-icon-plus"></i> 创建公告
      </el-button>
    </div>

    <div class="filter-bar">
      <el-input
        placeholder="搜索公告"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchAnnouncements"
        @keyup.enter.native="fetchAnnouncements">
      </el-input>
      <el-select v-model="typeFilter" placeholder="公告类型" clearable @change="fetchAnnouncements">
        <el-option label="系统公告" value="system"></el-option>
        <el-option label="课程公告" value="course"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchAnnouncements">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="announcements.length === 0" class="empty-data">
      <el-empty description="暂无公告数据"></el-empty>
    </div>

    <el-table
      v-else
      :data="announcements"
      style="width: 100%"
      border>
      <el-table-column
        prop="title"
        label="标题"
        min-width="200">
      </el-table-column>
      <el-table-column
        prop="content"
        label="内容"
        min-width="300">
        <template slot-scope="scope">
          <div class="content-preview">{{ scope.row.content }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="type"
        label="类型"
        width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === 'system' ? 'primary' : 'success'">
            {{ scope.row.type === 'system' ? '系统公告' : '课程公告' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="author_name"
        label="发布者"
        width="120">
      </el-table-column>
      <el-table-column
        prop="publish_date"
        label="发布时间"
        width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.publish_date) }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        width="120">
        <template slot-scope="scope">
          <div>
            <el-tag type="warning" v-if="scope.row.is_pinned">置顶</el-tag>
            <el-tag type="danger" v-if="scope.row.is_important">重要</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="editAnnouncement(scope.row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            :type="scope.row.is_pinned ? 'info' : 'warning'"
            @click="togglePin(scope.row)">
            {{ scope.row.is_pinned ? '取消置顶' : '置顶' }}
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="deleteAnnouncement(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container" v-if="announcements.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>

    <!-- 创建/编辑公告对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
      @close="resetForm">
      <el-form :model="announcementForm" :rules="rules" ref="announcementForm" label-width="100px">
        <el-form-item label="公告标题" prop="title">
          <el-input v-model="announcementForm.title" placeholder="请输入公告标题"></el-input>
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
          <el-input
            type="textarea"
            v-model="announcementForm.content"
            :rows="5"
            placeholder="请输入公告内容">
          </el-input>
        </el-form-item>
        <el-form-item label="公告类型" prop="type">
          <el-select v-model="announcementForm.type" placeholder="请选择公告类型" style="width: 100%">
            <el-option label="系统公告" value="system"></el-option>
            <el-option label="课程公告" value="course"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属课程" prop="course_id" v-if="announcementForm.type === 'course'">
          <el-select v-model="announcementForm.course_id" placeholder="请选择课程" style="width: 100%">
            <el-option
              v-for="course in courses"
              :key="course.id"
              :label="course.title"
              :value="course.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否置顶" prop="is_pinned">
          <el-switch v-model="announcementForm.is_pinned"></el-switch>
        </el-form-item>
        <el-form-item label="是否重要" prop="is_important">
          <el-switch v-model="announcementForm.is_important"></el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAnnouncement" :loading="saving">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AnnouncementManagement',
  data() {
    return {
      announcements: [],
      courses: [],
      loading: false,
      saving: false,
      searchQuery: '',
      typeFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '创建公告',
      announcementForm: {
        id: '',
        title: '',
        content: '',
        type: 'system',
        course_id: '',
        is_pinned: false,
        is_important: false
      },
      rules: {
        title: [
          { required: true, message: '请输入公告标题', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入公告内容', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择公告类型', trigger: 'change' }
        ],
        course_id: [
          { required: true, message: '请选择课程', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    async fetchAnnouncements() {
      this.loading = true
      try {
        const response = await this.$http.get('/api/announcements', {
          params: {
            search: this.searchQuery,
            type: this.typeFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.announcements = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取公告失败')
        }
      } catch (error) {
        console.error('获取公告失败:', error)
        this.$message.error('获取公告列表失败，请稍后再试')
        this.announcements = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    async fetchCourses() {
      try {
        const response = await this.$http.get('/api/courses')
        if (response.data.success) {
          this.courses = response.data.data
        }
      } catch (error) {
        console.error('获取课程失败:', error)
        this.$message.error('获取课程列表失败')
        this.courses = []
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchAnnouncements()
    },
    showCreateDialog() {
      this.dialogTitle = '创建公告'
      this.announcementForm = {
        id: '',
        title: '',
        content: '',
        type: 'system',
        course_id: '',
        is_pinned: false,
        is_important: false
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.announcementForm.clearValidate()
      })
    },
    editAnnouncement(announcement) {
      this.dialogTitle = '编辑公告'
      this.announcementForm = {
        id: announcement.id,
        title: announcement.title,
        content: announcement.content,
        type: announcement.type,
        course_id: announcement.course_id || '',
        is_pinned: announcement.is_pinned,
        is_important: announcement.is_important
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.announcementForm.clearValidate()
      })
    },
    resetForm() {
      this.announcementForm = {
        id: '',
        title: '',
        content: '',
        type: 'system',
        course_id: '',
        is_pinned: false,
        is_important: false
      }
    },
    async saveAnnouncement() {
      this.$refs.announcementForm.validate(async valid => {
        if (valid) {
          // 如果是课程公告，检查是否选择了课程
          if (this.announcementForm.type === 'course' && !this.announcementForm.course_id) {
            this.$message.warning('课程公告需要选择关联课程')
            return
          }

          this.saving = true

          try {
            let response

            if (this.announcementForm.id) {
              // 更新现有公告
              response = await this.$http.put(`/api/announcements/${this.announcementForm.id}`, this.announcementForm)
            } else {
              // 创建新公告
              response = await this.$http.post('/api/announcements', this.announcementForm)
            }

            if (response.data.success) {
              this.$message.success(this.announcementForm.id ? '公告更新成功' : '公告发布成功')
              this.dialogVisible = false
              this.fetchAnnouncements()
            } else {
              throw new Error(response.data.message || '保存公告失败')
            }
          } catch (error) {
            console.error('保存公告失败:', error)
            this.$message.error(error.message || '保存公告失败，请稍后再试')
          } finally {
            this.saving = false
          }
        }
      })
    },
    async togglePin(announcement) {
      try {
        const response = await this.$http.patch(`/api/announcements/${announcement.id}/pin`, {
          is_pinned: !announcement.is_pinned
        })

        if (response.data.success) {
          this.$message.success(response.data.message)
          this.fetchAnnouncements()
        } else {
          throw new Error(response.data.message || '更新置顶状态失败')
        }
      } catch (error) {
        console.error('更新置顶状态失败:', error)
        this.$message.error(error.message || '更新置顶状态失败，请稍后再试')
      }
    },
    async deleteAnnouncement(announcement) {
      try {
        await this.$confirm(`确定要删除公告 "${announcement.title}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await this.$http.delete(`/api/announcements/${announcement.id}`)

        if (response.data.success) {
          this.$message.success('公告删除成功')
          this.fetchAnnouncements()
        } else {
          throw new Error(response.data.message || '删除公告失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除公告失败:', error)
          this.$message.error(error.message || '删除公告失败，请稍后再试')
        }
      }
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    }
  },
  created() {
    this.fetchAnnouncements()
    this.fetchCourses()
  }
}
</script>

<style scoped>
.announcement-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.loading-container, .empty-data {
  padding: 20px;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.content-preview {
  max-height: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
