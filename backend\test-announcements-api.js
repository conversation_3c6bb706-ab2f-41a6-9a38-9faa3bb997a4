const mysql = require('mysql2/promise');

// 直接创建连接
const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'elearning',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

async function testAnnouncementsAPI() {
  try {
    console.log('🔍 测试公告API功能...\n');
    
    // 1. 测试获取公告列表
    console.log('1. 测试获取公告列表:');
    const [announcements] = await pool.query(`
      SELECT a.*, u.username as author_name
      FROM announcements a
      JOIN users u ON a.author_id = u.id
      ORDER BY a.is_pinned DESC, a.publish_date DESC
      LIMIT 5
    `);
    
    console.log(`   ✅ 找到 ${announcements.length} 条公告`);
    if (announcements.length > 0) {
      console.log('   📋 最新公告:');
      announcements.forEach((ann, index) => {
        console.log(`      ${index + 1}. ${ann.title} (${ann.type}) - ${ann.author_name}`);
      });
    }
    
    // 2. 测试创建新公告
    console.log('\n2. 测试创建新公告:');
    
    // 获取管理员用户ID
    const [adminUsers] = await pool.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');
    if (adminUsers.length === 0) {
      console.log('   ❌ 没有找到管理员用户');
      return;
    }
    
    const adminId = adminUsers[0].id;
    console.log(`   📝 使用管理员ID: ${adminId}`);
    
    // 创建测试公告
    const testAnnouncement = {
      title: '测试公告 - ' + new Date().toLocaleString(),
      content: '这是一个测试公告，用于验证API功能是否正常。',
      type: 'system',
      is_pinned: false,
      is_important: true
    };
    
    const [createResult] = await pool.query(
      `INSERT INTO announcements (title, content, type, author_id, is_pinned, is_important, publish_date)
       VALUES (?, ?, ?, ?, ?, ?, NOW())`,
      [
        testAnnouncement.title,
        testAnnouncement.content,
        testAnnouncement.type,
        adminId,
        testAnnouncement.is_pinned ? 1 : 0,
        testAnnouncement.is_important ? 1 : 0
      ]
    );
    
    const newAnnouncementId = createResult.insertId;
    console.log(`   ✅ 创建成功，公告ID: ${newAnnouncementId}`);
    
    // 3. 测试获取新创建的公告
    console.log('\n3. 测试获取新创建的公告:');
    const [newAnnouncement] = await pool.query(
      `SELECT a.*, u.username as author_name
       FROM announcements a
       JOIN users u ON a.author_id = u.id
       WHERE a.id = ?`,
      [newAnnouncementId]
    );
    
    if (newAnnouncement.length > 0) {
      const ann = newAnnouncement[0];
      console.log(`   ✅ 获取成功:`);
      console.log(`      标题: ${ann.title}`);
      console.log(`      内容: ${ann.content}`);
      console.log(`      类型: ${ann.type}`);
      console.log(`      作者: ${ann.author_name}`);
      console.log(`      重要: ${ann.is_important ? '是' : '否'}`);
      console.log(`      置顶: ${ann.is_pinned ? '是' : '否'}`);
    }
    
    // 4. 测试更新公告
    console.log('\n4. 测试更新公告:');
    await pool.query(
      `UPDATE announcements 
       SET title = ?, is_pinned = ?, updated_at = NOW()
       WHERE id = ?`,
      [
        testAnnouncement.title + ' (已更新)',
        1, // 设置为置顶
        newAnnouncementId
      ]
    );
    console.log('   ✅ 更新成功 (设置为置顶)');
    
    // 5. 测试按类型筛选
    console.log('\n5. 测试按类型筛选:');
    const [systemAnnouncements] = await pool.query(
      'SELECT COUNT(*) as count FROM announcements WHERE type = "system"'
    );
    const [courseAnnouncements] = await pool.query(
      'SELECT COUNT(*) as count FROM announcements WHERE type = "course"'
    );
    
    console.log(`   📊 系统公告: ${systemAnnouncements[0].count} 条`);
    console.log(`   📊 课程公告: ${courseAnnouncements[0].count} 条`);
    
    // 6. 测试置顶公告
    console.log('\n6. 测试置顶公告:');
    const [pinnedAnnouncements] = await pool.query(
      'SELECT COUNT(*) as count FROM announcements WHERE is_pinned = 1'
    );
    console.log(`   📌 置顶公告: ${pinnedAnnouncements[0].count} 条`);
    
    // 7. 清理测试数据
    console.log('\n7. 清理测试数据:');
    await pool.query('DELETE FROM announcements WHERE id = ?', [newAnnouncementId]);
    console.log('   🗑️ 测试公告已删除');
    
    console.log('\n✅ 所有测试完成！公告API功能正常。');
    
    // 8. 显示当前公告统计
    console.log('\n📊 当前公告统计:');
    const [totalCount] = await pool.query('SELECT COUNT(*) as count FROM announcements');
    const [importantCount] = await pool.query('SELECT COUNT(*) as count FROM announcements WHERE is_important = 1');
    const [pinnedCount] = await pool.query('SELECT COUNT(*) as count FROM announcements WHERE is_pinned = 1');
    
    console.log(`   总公告数: ${totalCount[0].count}`);
    console.log(`   重要公告: ${importantCount[0].count}`);
    console.log(`   置顶公告: ${pinnedCount[0].count}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

testAnnouncementsAPI();
