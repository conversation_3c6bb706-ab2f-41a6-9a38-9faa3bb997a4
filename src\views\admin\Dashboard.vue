<template>
  <div class="admin-dashboard">
    <h1>管理员仪表盘</h1>

    <el-row :gutter="20">
      <!-- 欢迎卡片 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card class="welcome-card">
          <div class="welcome-header">
            <img :src="userAvatar" alt="Avatar" class="avatar">
            <div class="welcome-text">
              <h2>·欢迎回来，{{ userName }}·</h2>
              <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 统计卡片 -->
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-bell"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.announcementCount }}</div>
            <div class="stat-label">公告总数</div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-chat-line-square"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.commentCount }}</div>
            <div class="stat-label">评论总数</div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-reading"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.courseCount }}</div>
            <div class="stat-label">课程总数</div>
          </div>
        </el-card>
      </el-col>

      <!-- 公告统计 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span>公告统计</span>
            <el-button type="primary" size="small" @click="navigateTo('/admin/announcements')">管理公告</el-button>
          </div>
          <div class="chart-container">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ stats.systemAnnouncementCount }}</div>
                  <div class="stat-label">系统公告</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ stats.courseAnnouncementCount }}</div>
                  <div class="stat-label">课程公告</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ stats.pinnedAnnouncementCount }}</div>
                  <div class="stat-label">置顶公告</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>

      <!-- 评论统计 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span>评论统计</span>
            <el-button type="primary" size="small" @click="navigateTo('/admin/comments')">管理评论</el-button>
          </div>
          <div class="chart-container">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ stats.todayCommentCount }}</div>
                  <div class="stat-label">今日新增</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ stats.weekCommentCount }}</div>
                  <div class="stat-label">本周新增</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>

      <!-- 最新公告 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span>最新公告</span>
            <el-button type="text" @click="navigateTo('/admin/announcements')">查看全部</el-button>
          </div>
          <div v-if="announcements.length > 0">
            <el-table :data="announcements" style="width: 100%">
              <el-table-column prop="title" label="标题" width="180"></el-table-column>
              <el-table-column prop="type" label="类型" width="100">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.type === 'system' ? 'primary' : 'success'">
                    {{ scope.row.type === 'system' ? '系统' : '课程' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="publish_date" label="发布时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.publish_date) }}
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click="navigateTo(`/admin/announcements?edit=${scope.row.id}`)" type="text" size="small">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无公告数据</p>
          </div>
        </el-card>
      </el-col>

      <!-- 最新评论 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span>最新评论</span>
            <el-button type="text" @click="navigateTo('/admin/comments')">查看全部</el-button>
          </div>
          <div v-if="comments.length > 0">
            <el-table :data="comments" style="width: 100%">
              <el-table-column prop="content" label="内容" show-overflow-tooltip></el-table-column>
              <el-table-column prop="author_name" label="评论者" width="100"></el-table-column>
              <el-table-column prop="created_at" label="评论时间" width="150">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无评论数据</p>
          </div>
        </el-card>
      </el-col>

      <!-- 系统日志 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span>系统日志</span>
          </div>
          <div v-if="logs.length > 0">
            <el-timeline>
              <el-timeline-item
                v-for="(log, index) in logs"
                :key="index"
                :timestamp="formatDate(log.time)"
                placement="top"
                :type="getLogType(log.level)">
                <p class="log-message">{{ log.message }}</p>
                <p class="log-details" v-if="log.details">{{ log.details }}</p>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else class="empty-data">
            <p>暂无系统日志</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
// 暂时不使用echarts
// import * as echarts from 'echarts'

export default {
  name: 'AdminDashboard',
  data() {
    return {
      announcements: [],
      comments: [],
      logs: [],
      stats: {
        announcementCount: 0,
        commentCount: 0,
        courseCount: 0,
        systemAnnouncementCount: 0,
        courseAnnouncementCount: 0,
        pinnedAnnouncementCount: 0,
        todayCommentCount: 0,
        weekCommentCount: 0
      },
      currentDate: this.formatCurrentDate()
    }
  },
  computed: {
    userName() {
      return this.$store.getters.userName || '管理员'
    },
    userAvatar() {
      const user = this.$store.state.user
      return user && user.avatar ? user.avatar : require('@/assets/default-avatar.svg')
    }
  },
  methods: {
    navigateTo(path) {
      this.$router.push(path)
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatCurrentDate() {
      return this.$moment().format('YYYY年MM月DD日')
    },
    getUserRoleType(role) {
      const types = {
        'admin': 'danger',
        'teacher': 'warning',
        'student': 'success'
      }
      return types[role] || 'info'
    },
    getUserRoleText(role) {
      const texts = {
        'admin': '管理员',
        'teacher': '教师',
        'student': '学生'
      }
      return texts[role] || role
    },
    getLogType(level) {
      const types = {
        'info': 'primary',
        'warning': 'warning',
        'error': 'danger',
        'success': 'success'
      }
      return types[level] || 'info'
    },
    // 暂时不使用图表
    initUserChart() {
      // 不再需要
    },
    initCourseChart() {
      // 不再需要
    },
    async fetchData() {
      // 直接使用模拟数据，不尝试调用API
      // 统计数据
      this.stats = {
        announcementCount: 15,
        commentCount: 48,
        courseCount: 13,
        systemAnnouncementCount: 8,
        courseAnnouncementCount: 7,
        pinnedAnnouncementCount: 3,
        todayCommentCount: 5,
        weekCommentCount: 23
      }

      // 最新公告
      this.announcements = [
        {
          id: 1,
          title: '欢迎使用在线学习平台',
          content: '欢迎所有师生使用我们的在线学习平台！',
          type: 'system',
          author_name: 'admin',
          publish_date: new Date(Date.now() - 3600000 * 48),
          is_pinned: true
        },
        {
          id: 2,
          title: '系统维护通知',
          content: '系统将于本周六进行例行维护，请提前做好准备。',
          type: 'system',
          author_name: 'admin',
          publish_date: new Date(Date.now() - 3600000 * 24),
          is_pinned: false
        },
        {
          id: 3,
          title: '数据库课程作业提交',
          content: '请各位同学在本周五前提交数据库设计作业。',
          type: 'course',
          author_name: 'teacher1',
          publish_date: new Date(Date.now() - 3600000 * 12),
          is_pinned: false
        },
        {
          id: 4,
          title: '关于期末考试安排',
          content: '各位同学请注意，期末考试将于下月初开始，请做好复习准备。',
          type: 'system',
          author_name: 'admin',
          publish_date: new Date(Date.now() - 3600000 * 36),
          is_pinned: true
        },
        {
          id: 5,
          title: 'Web开发课程项目展示',
          content: 'Web开发课程的期末项目展示将在下周三进行，请各小组做好准备。',
          type: 'course',
          author_name: 'teacher2',
          publish_date: new Date(Date.now() - 3600000 * 18),
          is_pinned: false
        }
      ]

      // 最新评论
      this.comments = [
        {
          id: 1,
          content: '这个课程非常有帮助，谢谢老师！',
          author_name: 'student1',
          discussion_title: '关于Web开发课程的讨论',
          created_at: new Date(Date.now() - 3600000 * 6)
        },
        {
          id: 2,
          content: '我对第三章的内容有些疑问，能否详细解释一下？',
          author_name: 'student2',
          discussion_title: '数据库设计原则讨论',
          created_at: new Date(Date.now() - 3600000 * 12)
        },
        {
          id: 3,
          content: '已经完成了作业，请老师查收。',
          author_name: 'student3',
          discussion_title: 'JavaScript作业讨论',
          created_at: new Date(Date.now() - 3600000 * 24)
        },
        {
          id: 4,
          content: '这个实验太难了，有没有同学愿意一起讨论？',
          author_name: 'student4',
          discussion_title: '人工智能实验讨论',
          created_at: new Date(Date.now() - 3600000 * 18)
        },
        {
          id: 5,
          content: '老师，我的项目已经提交，请您查看一下。',
          author_name: 'student5',
          discussion_title: '期末项目提交讨论',
          created_at: new Date(Date.now() - 3600000 * 3)
        }
      ]

      // 系统日志
      this.logs = [
        {
          level: 'info',
          message: '系统启动',
          time: new Date(Date.now() - 3600000 * 24)
        },
        {
          level: 'success',
          message: '新公告发布',
          details: '系统维护通知',
          time: new Date(Date.now() - 3600000 * 12)
        },
        {
          level: 'info',
          message: '用户登录',
          details: 'admin',
          time: new Date(Date.now() - 3600000 * 6)
        },
        {
          level: 'warning',
          message: '评论被删除',
          details: 'ID: 15, 内容不适当',
          time: new Date(Date.now() - 3600000 * 2)
        },
        {
          level: 'success',
          message: '公告更新',
          details: 'ID: 2, 系统维护通知',
          time: new Date(Date.now() - 3600000 * 1)
        }
      ]
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.dashboard-card {
  margin-bottom: 20px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-card {
  margin-bottom: 20px;
  background-color: #f0f9eb;
}

.welcome-header {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-right: 20px;
}

.welcome-text h2 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #67c23a;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 20px;
  height: 100%;
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
  color: #409EFF;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
}

.chart-container {
  padding: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.stat-item .stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #409EFF;
}

.stat-item .stat-label {
  color: #606266;
}

.empty-data {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}

.log-message {
  margin: 0;
  font-weight: bold;
}

.log-details {
  margin: 5px 0 0;
  color: #606266;
  font-size: 12px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .welcome-header {
    flex-direction: column;
    text-align: center;
  }

  .avatar {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .stat-card {
    margin-bottom: 15px;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
