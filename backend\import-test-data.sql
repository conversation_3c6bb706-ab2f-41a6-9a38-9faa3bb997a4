-- 导入测试数据
USE elearning;

-- 插入测试数据
INSERT INTO tests (title, description, course_id, creator_id, time_limit, start_time, end_time, randomize_questions, show_results, status, created_at) VALUES
('HTML和CSS基础测验', 'HTML标签和CSS选择器的基础知识测试。', 
 2, -- Web开发基础课程
 2, -- 教师用户
 30, -- 30分钟
 '2023-09-15 10:00:00', '2023-09-15 11:00:00',
 FALSE, TRUE, 'published', NOW()),
 
('JavaScript基础测试', 'JavaScript语言基础知识和DOM操作测试。', 
 2, -- Web开发基础课程
 2, -- 教师用户
 45, -- 45分钟
 '2023-10-10 14:00:00', '2023-10-10 15:30:00',
 TRUE, TRUE, 'published', NOW()),
 
('SQL基础测试', 'SQL查询语言基础知识测试。', 
 3, -- 数据库系统课程
 2, -- 教师用户
 45, -- 45分钟
 '2023-10-15 14:00:00', '2023-10-15 15:30:00',
 TRUE, TRUE, 'published', NOW()),
 
('数据库设计原则', '数据库范式和设计原则测试。', 
 3, -- 数据库系统课程
 2, -- 教师用户
 60, -- 60分钟
 '2023-11-05 09:00:00', '2023-11-05 10:30:00',
 FALSE, TRUE, 'draft', NOW()),
 
('React基础组件测试', 'React组件和生命周期测试。', 
 4, -- 高级Web开发课程
 2, -- 教师用户
 40, -- 40分钟
 '2023-10-20 09:00:00', '2023-10-20 10:30:00',
 FALSE, TRUE, 'draft', NOW()),
 
('人工智能导论期中考试', '人工智能基础概念和算法测试。', 
 1, -- 人工智能导论课程
 2, -- 教师用户
 90, -- 90分钟
 '2023-11-15 09:00:00', '2023-11-15 11:00:00',
 FALSE, TRUE, 'published', NOW());

-- 插入测试问题
INSERT INTO test_questions (test_id, type, content, answer, score, sort_order, created_at) VALUES
-- HTML和CSS基础测验的问题
(1, 'single',
 'HTML文档的根元素是什么？',
 '{"correct": "html"}', 2, 1, NOW()),
 
(1, 'multiple',
 '以下哪些是块级元素？',
 '{"correct": ["div", "p", "h1"]}', 3, 2, NOW()),
 
(1, 'truefalse',
 'CSS中，class选择器使用#符号。',
 '{"correct": false}', 1, 3, NOW()),
 
(1, 'essay',
 '简述CSS盒模型的组成部分及其作用。',
 NULL, 4, 4, NOW()),
 
-- JavaScript基础测试的问题
(2, 'single',
 'JavaScript中，以下哪个不是基本数据类型？',
 '{"correct": "array"}', 2, 1, NOW()),
 
(2, 'multiple',
 '以下哪些是JavaScript中的事件？',
 '{"correct": ["click", "load", "submit"]}', 3, 2, NOW()),
 
(2, 'truefalse',
 'JavaScript是一种编译型语言。',
 '{"correct": false}', 1, 3, NOW()),
 
(2, 'essay',
 '解释JavaScript中的闭包概念及其应用场景。',
 NULL, 4, 4, NOW()),
 
-- SQL基础测试的问题
(3, 'single',
 'SQL中用于从表中检索数据的语句是？',
 '{"correct": "SELECT"}', 2, 1, NOW()),
 
(3, 'multiple',
 '以下哪些是SQL的数据操作语言(DML)？',
 '{"correct": ["INSERT", "UPDATE", "DELETE"]}', 3, 2, NOW()),
 
(3, 'truefalse',
 'SQL中的JOIN操作只能连接两个表。',
 '{"correct": false}', 1, 3, NOW()),
 
(3, 'essay',
 '解释SQL中的事务特性(ACID)及其重要性。',
 NULL, 4, 4, NOW());

-- 为问题添加选项
INSERT INTO question_options (question_id, label, value, sort_order) VALUES
-- HTML文档的根元素是什么？
(1, 'html', 'html', 1),
(1, 'body', 'body', 2),
(1, 'head', 'head', 3),
(1, 'document', 'document', 4),

-- 以下哪些是块级元素？
(2, 'div', 'div', 1),
(2, 'span', 'span', 2),
(2, 'p', 'p', 3),
(2, 'a', 'a', 4),
(2, 'h1', 'h1', 5),

-- JavaScript中，以下哪个不是基本数据类型？
(5, 'string', 'string', 1),
(5, 'number', 'number', 2),
(5, 'boolean', 'boolean', 3),
(5, 'array', 'array', 4),
(5, 'undefined', 'undefined', 5),

-- 以下哪些是JavaScript中的事件？
(6, 'click', 'click', 1),
(6, 'load', 'load', 2),
(6, 'submit', 'submit', 3),
(6, 'execute', 'execute', 4),
(6, 'compile', 'compile', 5),

-- SQL中用于从表中检索数据的语句是？
(9, 'SELECT', 'SELECT', 1),
(9, 'FETCH', 'FETCH', 2),
(9, 'GET', 'GET', 3),
(9, 'RETRIEVE', 'RETRIEVE', 4),

-- 以下哪些是SQL的数据操作语言(DML)？
(10, 'SELECT', 'SELECT', 1),
(10, 'INSERT', 'INSERT', 2),
(10, 'UPDATE', 'UPDATE', 3),
(10, 'DELETE', 'DELETE', 4),
(10, 'CREATE', 'CREATE', 5);
