<template>
  <div class="course-detail">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else>
      <!-- 课程头部信息 -->
      <div class="course-header">
        <div class="course-cover" v-if="course.cover_image">
          <img :src="course.cover_image" alt="课程封面">
        </div>
        <div class="course-info">
          <div class="course-title-row">
            <h1>{{ course.title }}</h1>
            <el-tag v-if="isEnrolled" type="success">已选修</el-tag>
          </div>
          <p class="course-description">{{ course.description }}</p>
          <div class="course-meta">
            <span><i class="el-icon-user"></i> 教师: {{ course.teacher_name }}</span>
            <span><i class="el-icon-date"></i> {{ formatDate(course.start_date) }} - {{ formatDate(course.end_date) }}</span>
            <span><i class="el-icon-collection-tag"></i> {{ course.category }}</span>
          </div>
        </div>
        <div class="course-actions">
          <el-button
            v-if="!isEnrolled"
            type="primary"
            @click="enrollCourse"
            :loading="enrolling">
            选修课程
          </el-button>
          <el-button
            v-else
            type="danger"
            @click="unenrollCourse"
            :loading="unenrolling">
            退选课程
          </el-button>
        </div>
      </div>

      <!-- 课程内容导航 -->
      <el-tabs v-model="activeTab" type="card" v-if="isEnrolled">
        <el-tab-pane label="概览" name="overview">
          <course-overview :course="course" />
        </el-tab-pane>
        <el-tab-pane label="资源" name="resources">
          <course-resources :course-id="courseId" />
        </el-tab-pane>
        <el-tab-pane label="测试" name="tests">
          <course-tests :course-id="courseId" />
        </el-tab-pane>
        <el-tab-pane label="讨论" name="discussions">
          <course-discussions :course-id="courseId" />
        </el-tab-pane>
        <el-tab-pane label="出勤" name="attendance">
          <course-attendance :course-id="courseId" />
        </el-tab-pane>
      </el-tabs>

      <!-- 未选修提示 -->
      <div v-else class="not-enrolled-message">
        <el-alert
          title="您尚未选修此课程"
          type="info"
          description="选修课程后，您将可以访问课程资源、参加测试、参与讨论等。"
          show-icon
          :closable="false">
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script>
// 导入子组件
import CourseOverview from '@/components/student/CourseOverview.vue'

import CourseResources from '@/components/student/CourseResources.vue'

import CourseTests from '@/components/student/CourseTests.vue'

import CourseDiscussions from '@/components/student/CourseDiscussions.vue'

import CourseAttendance from '@/components/student/CourseAttendance.vue'

export default {
  name: 'StudentCourseDetail',
  components: {
    CourseOverview,
    CourseResources,
    CourseTests,
    CourseDiscussions,
    CourseAttendance
  },
  props: {
    id: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      courseId: parseInt(this.id),
      course: {},
      loading: true,
      isEnrolled: false,
      enrolling: false,
      unenrolling: false,
      activeTab: 'overview'
    }
  },
  methods: {
    async fetchCourseDetails() {
      this.loading = true
      try {
        // 从API获取课程详情
        const response = await this.$http.get(`/api/courses/${this.courseId}`)

        if (response.data.success) {
          this.course = response.data.data

          // 检查用户是否已选修该课程
          try {
            const enrollResponse = await this.$http.get(`/api/courses/${this.courseId}/enrollment-status`)
            this.isEnrolled = enrollResponse.data.isEnrolled
          } catch (enrollError) {
            console.error('获取选课状态失败:', enrollError)
            // 默认为未选修
            this.isEnrolled = false
          }
        } else {
          throw new Error(response.data.message || '获取课程详情失败')
        }
      } catch (error) {
        console.error('获取课程详情失败:', error)
        this.$message.error(error.message || '获取课程详情失败，请稍后再试')

        // 使用模拟数据（仅在开发环境或API失败时）
        this.course = {
          id: this.courseId,
          title: 'Web开发基础',
          description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
          cover_image: null,
          teacher_name: '张教授',
          category: '计算机科学',
          start_date: '2023-09-01',
          end_date: '2024-01-15',
          student_count: 25
        }

        // 假设用户已选修ID为1的课程
        this.isEnrolled = this.courseId === 1
      } finally {
        this.loading = false
      }
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD')
    },
    async enrollCourse() {
      this.enrolling = true
      try {
        // 调用API选课
        const response = await this.$http.post(`/api/courses/${this.courseId}/enroll`)

        if (response.data.success) {
          this.isEnrolled = true
          this.$message.success('选修课程成功')
        } else {
          throw new Error(response.data.message || '选修课程失败')
        }
      } catch (error) {
        console.error('选修课程失败:', error)

        // 处理常见错误
        if (error.response && error.response.status === 400) {
          if (error.response.data && error.response.data.message) {
            this.$message.warning(error.response.data.message)
          } else {
            this.$message.warning('您可能已经选修了该课程')
          }
        } else {
          this.$message.error(error.message || '选修课程失败，请稍后再试')
        }

        // 刷新选课状态
        this.fetchCourseDetails()
      } finally {
        this.enrolling = false
      }
    },
    async unenrollCourse() {
      try {
        await this.$confirm('确定要退选此课程吗？您将无法访问课程资源和参加测试。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.unenrolling = true

        // 调用API退课
        const response = await this.$http.delete(`/api/courses/${this.courseId}/enroll`)

        if (response.data.success) {
          this.isEnrolled = false
          this.$message.success('退选课程成功')
        } else {
          throw new Error(response.data.message || '退选课程失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退选课程失败:', error)

          // 处理常见错误
          if (error.response && error.response.status === 400) {
            if (error.response.data && error.response.data.message) {
              this.$message.warning(error.response.data.message)
            } else {
              this.$message.warning('您可能尚未选修该课程')
            }
          } else {
            this.$message.error(error.message || '退选课程失败，请稍后再试')
          }

          // 刷新选课状态
          this.fetchCourseDetails()
        }
      } finally {
        this.unenrolling = false
      }
    }
  },
  created() {
    this.fetchCourseDetails()
  }
}
</script>

<style scoped>
.course-detail {
  padding: 20px;
}

.loading-container {
  padding: 20px;
}

.course-header {
  display: flex;
  margin-bottom: 30px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
}

.course-cover {
  width: 200px;
  height: 150px;
  overflow: hidden;
  margin-right: 20px;
  border-radius: 4px;
}

.course-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-info {
  flex: 1;
}

.course-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.course-title-row h1 {
  margin: 0;
  margin-right: 10px;
}

.course-description {
  margin-bottom: 15px;
  color: #606266;
}

.course-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #909399;
}

.course-actions {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 10px;
}

.not-enrolled-message {
  margin-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .course-header {
    flex-direction: column;
  }

  .course-cover {
    width: 100%;
    margin-right: 0;
    margin-bottom: 15px;
  }

  .course-actions {
    margin-top: 15px;
    flex-direction: row;
  }
}
</style>
