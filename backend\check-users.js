const { pool } = require('./config/db');

async function checkUsers() {
  try {
    // 测试数据库连接
    console.log('正在连接数据库...');
    const [connection] = await pool.query('SELECT 1');
    console.log('数据库连接成功!');
    
    // 查询所有用户
    console.log('\n所有用户:');
    const [users] = await pool.query('SELECT id, username, email, role, created_at FROM users');
    console.table(users);
    
    // 查询教师用户
    console.log('\n教师用户:');
    const [teachers] = await pool.query("SELECT id, username, email, created_at FROM users WHERE role = 'teacher'");
    console.table(teachers);
    
    // 查询学生用户
    console.log('\n学生用户:');
    const [students] = await pool.query("SELECT id, username, email, created_at FROM users WHERE role = 'student'");
    console.table(students);
    
    // 查询管理员用户
    console.log('\n管理员用户:');
    const [admins] = await pool.query("SELECT id, username, email, created_at FROM users WHERE role = 'admin'");
    console.table(admins);
    
    process.exit(0);
  } catch (error) {
    console.error('查询用户失败:', error);
    process.exit(1);
  }
}

checkUsers();
