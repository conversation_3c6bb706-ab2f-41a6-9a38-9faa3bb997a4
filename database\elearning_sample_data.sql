-- E-learning平台示例数据 (MySQL 8.0+)
USE elearning;

-- 清空现有数据（如果需要）
-- SET FOREIGN_KEY_CHECKS = 0;
-- TRUNCATE TABLE student_attendances;
-- TRUNCATE TABLE attendances;
-- TRUNCATE TABLE reply_likes;
-- TRUNCATE TABLE discussion_replies;
-- TRUNCATE TABLE discussion_likes;
-- TRUNCATE TABLE discussion_tags;
-- TRUNCATE TABLE discussions;
-- TRUNCATE TABLE test_answers;
-- TRUNCATE TABLE test_submissions;
-- TRUNCATE TABLE question_options;
-- TRUNCATE TABLE test_questions;
-- TRUNCATE TABLE tests;
-- TRUNCATE TABLE announcements;
-- TRUNCATE TABLE resources;
-- TRUNCATE TABLE course_students;
-- TRUNCATE TABLE course_tags;
-- TRUNCATE TABLE courses;
-- TRUNCATE TABLE users;
-- SET FOREIGN_KEY_CHECKS = 1;

-- 插入用户数据
-- 注意：密码都是 'password'，在实际应用中应该使用加密后的密码
INSERT INTO users (username, email, password, role, avatar, created_at) VALUES
('admin', '<EMAIL>', '$2a$10$NlUO.wJRDJmjgKbTQOcS5eKpZ.P.T7wUO89.jBzgsr9N1jlpIQF4W', 'admin', 'admin-avatar.jpg', NOW()),
('teacher1', '<EMAIL>', '$2a$10$NlUO.wJRDJmjgKbTQOcS5eKpZ.P.T7wUO89.jBzgsr9N1jlpIQF4W', 'teacher', 'teacher1-avatar.jpg', NOW()),
('teacher2', '<EMAIL>', '$2a$10$NlUO.wJRDJmjgKbTQOcS5eKpZ.P.T7wUO89.jBzgsr9N1jlpIQF4W', 'teacher', 'teacher2-avatar.jpg', NOW()),
('student1', '<EMAIL>', '$2a$10$NlUO.wJRDJmjgKbTQOcS5eKpZ.P.T7wUO89.jBzgsr9N1jlpIQF4W', 'student', 'student1-avatar.jpg', NOW()),
('student2', '<EMAIL>', '$2a$10$NlUO.wJRDJmjgKbTQOcS5eKpZ.P.T7wUO89.jBzgsr9N1jlpIQF4W', 'student', 'student2-avatar.jpg', NOW()),
('student3', '<EMAIL>', '$2a$10$NlUO.wJRDJmjgKbTQOcS5eKpZ.P.T7wUO89.jBzgsr9N1jlpIQF4W', 'student', 'student3-avatar.jpg', NOW());

-- 更新教师信息
UPDATE users 
SET teacher_title = '教授', teacher_department = '计算机科学系', teacher_bio = '拥有10年教学经验，专注于Web开发和数据库技术。'
WHERE username = 'teacher1';

UPDATE users 
SET teacher_title = '副教授', teacher_department = '软件工程系', teacher_bio = '专注于移动应用开发和用户体验设计。'
WHERE username = 'teacher2';

-- 更新学生信息
UPDATE users 
SET student_id = '2023001', student_grade = '大三', student_major = '计算机科学'
WHERE username = 'student1';

UPDATE users 
SET student_id = '2023002', student_grade = '大二', student_major = '软件工程'
WHERE username = 'student2';

UPDATE users 
SET student_id = '2023003', student_grade = '大四', student_major = '人工智能'
WHERE username = 'student3';

-- 插入课程数据
INSERT INTO courses (title, description, cover_image, teacher_id, status, category, start_date, end_date, course_code, created_at) VALUES
('Web开发基础', '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。', 'web-dev.jpg', 
 (SELECT id FROM users WHERE username = 'teacher1'), 'published', '计算机科学', 
 '2023-09-01', '2024-01-15', 'WEB101', NOW()),
 
('数据库系统', '关系型数据库设计、SQL查询和数据库管理系统。', 'database.jpg', 
 (SELECT id FROM users WHERE username = 'teacher1'), 'published', '计算机科学', 
 '2023-09-01', '2024-01-15', 'DB201', NOW()),
 
('移动应用开发', '使用React Native开发跨平台移动应用。', 'mobile-dev.jpg', 
 (SELECT id FROM users WHERE username = 'teacher2'), 'published', '软件工程', 
 '2023-09-01', '2024-01-15', 'MOB301', NOW());

-- 插入课程标签
INSERT INTO course_tags (course_id, tag) VALUES
((SELECT id FROM courses WHERE title = 'Web开发基础'), 'HTML'),
((SELECT id FROM courses WHERE title = 'Web开发基础'), 'CSS'),
((SELECT id FROM courses WHERE title = 'Web开发基础'), 'JavaScript'),
((SELECT id FROM courses WHERE title = 'Web开发基础'), '前端'),
((SELECT id FROM courses WHERE title = '数据库系统'), 'SQL'),
((SELECT id FROM courses WHERE title = '数据库系统'), 'MySQL'),
((SELECT id FROM courses WHERE title = '数据库系统'), '数据建模'),
((SELECT id FROM courses WHERE title = '移动应用开发'), 'React Native'),
((SELECT id FROM courses WHERE title = '移动应用开发'), 'JavaScript'),
((SELECT id FROM courses WHERE title = '移动应用开发'), '移动开发');

-- 学生选课
INSERT INTO course_students (course_id, student_id, enrollment_date) VALUES
((SELECT id FROM courses WHERE title = 'Web开发基础'), 
 (SELECT id FROM users WHERE username = 'student1'), NOW()),
((SELECT id FROM courses WHERE title = 'Web开发基础'), 
 (SELECT id FROM users WHERE username = 'student2'), NOW()),
((SELECT id FROM courses WHERE title = '数据库系统'), 
 (SELECT id FROM users WHERE username = 'student1'), NOW()),
((SELECT id FROM courses WHERE title = '数据库系统'), 
 (SELECT id FROM users WHERE username = 'student3'), NOW()),
((SELECT id FROM courses WHERE title = '移动应用开发'), 
 (SELECT id FROM users WHERE username = 'student2'), NOW()),
((SELECT id FROM courses WHERE title = '移动应用开发'), 
 (SELECT id FROM users WHERE username = 'student3'), NOW());

-- 插入资源数据
INSERT INTO resources (title, description, type, url, size, mime_type, course_id, uploader_id, download_count, is_public, created_at) VALUES
('HTML基础教程', 'HTML入门指南，包含基本标签和结构。', 'document', '/uploads/documents/html-basics.pdf', 1024000, 'application/pdf',
 (SELECT id FROM courses WHERE title = 'Web开发基础'), 
 (SELECT id FROM users WHERE username = 'teacher1'), 15, TRUE, NOW()),
 
('CSS样式指南', 'CSS选择器、布局和响应式设计指南。', 'document', '/uploads/documents/css-guide.pdf', 1536000, 'application/pdf',
 (SELECT id FROM courses WHERE title = 'Web开发基础'), 
 (SELECT id FROM users WHERE username = 'teacher1'), 12, TRUE, NOW()),
 
('JavaScript入门视频', 'JavaScript基础概念和语法讲解。', 'video', '/uploads/videos/js-intro.mp4', 52428800, 'video/mp4',
 (SELECT id FROM courses WHERE title = 'Web开发基础'), 
 (SELECT id FROM users WHERE username = 'teacher1'), 25, TRUE, NOW()),
 
('SQL基础教程', 'SQL查询语言入门，包含基本语法和示例。', 'document', '/uploads/documents/sql-basics.pdf', 2048000, 'application/pdf',
 (SELECT id FROM courses WHERE title = '数据库系统'), 
 (SELECT id FROM users WHERE username = 'teacher1'), 18, TRUE, NOW()),
 
('数据库设计实践', '数据库设计原则和实践案例分析。', 'document', '/uploads/documents/db-design.pdf', 3072000, 'application/pdf',
 (SELECT id FROM courses WHERE title = '数据库系统'), 
 (SELECT id FROM users WHERE username = 'teacher1'), 14, TRUE, NOW()),
 
('React Native基础', 'React Native组件和开发环境设置指南。', 'document', '/uploads/documents/react-native-basics.pdf', 2560000, 'application/pdf',
 (SELECT id FROM courses WHERE title = '移动应用开发'), 
 (SELECT id FROM users WHERE username = 'teacher2'), 20, TRUE, NOW()),
 
('移动UI设计原则', '移动应用UI/UX设计最佳实践。', 'document', '/uploads/documents/mobile-ui.pdf', 1843200, 'application/pdf',
 (SELECT id FROM courses WHERE title = '移动应用开发'), 
 (SELECT id FROM users WHERE username = 'teacher2'), 16, TRUE, NOW());

-- 插入公告数据
INSERT INTO announcements (title, content, type, course_id, author_id, is_pinned, is_important, publish_date, created_at) VALUES
('欢迎使用E-learning平台', '欢迎所有师生使用我们的在线学习平台！请熟悉各项功能，有任何问题请联系管理员。', 'system', NULL,
 (SELECT id FROM users WHERE username = 'admin'), TRUE, TRUE, NOW(), NOW()),
 
('Web开发课程开始', 'Web开发基础课程将于下周一开始，请所有学生提前准备好开发环境。', 'course',
 (SELECT id FROM courses WHERE title = 'Web开发基础'),
 (SELECT id FROM users WHERE username = 'teacher1'), TRUE, FALSE, NOW(), NOW()),
 
('数据库期中考试通知', '数据库系统课程的期中考试将于10月15日进行，请做好准备。', 'course',
 (SELECT id FROM courses WHERE title = '数据库系统'),
 (SELECT id FROM users WHERE username = 'teacher1'), FALSE, TRUE, NOW(), NOW()),
 
('移动应用开发项目分组', '移动应用开发课程的项目分组已经完成，请查看课程详情页面。', 'course',
 (SELECT id FROM courses WHERE title = '移动应用开发'),
 (SELECT id FROM users WHERE username = 'teacher2'), FALSE, FALSE, NOW(), NOW());

-- 插入测试数据
INSERT INTO tests (title, description, course_id, creator_id, time_limit, start_time, end_time, randomize_questions, show_results, status, created_at) VALUES
('HTML和CSS基础测验', 'HTML标签和CSS选择器的基础知识测试。', 
 (SELECT id FROM courses WHERE title = 'Web开发基础'),
 (SELECT id FROM users WHERE username = 'teacher1'),
 30, -- 30分钟
 '2023-09-15 10:00:00', '2023-09-15 11:00:00',
 FALSE, TRUE, 'published', NOW()),
 
('SQL基础测试', 'SQL查询语言基础知识测试。', 
 (SELECT id FROM courses WHERE title = '数据库系统'),
 (SELECT id FROM users WHERE username = 'teacher1'),
 45, -- 45分钟
 '2023-10-15 14:00:00', '2023-10-15 15:30:00',
 TRUE, TRUE, 'published', NOW()),
 
('React Native组件测验', 'React Native基础组件和生命周期测试。', 
 (SELECT id FROM courses WHERE title = '移动应用开发'),
 (SELECT id FROM users WHERE username = 'teacher2'),
 40, -- 40分钟
 '2023-10-20 09:00:00', '2023-10-20 10:30:00',
 FALSE, TRUE, 'draft', NOW());

-- 插入测试问题
INSERT INTO test_questions (test_id, type, content, answer, score, sort_order, created_at) VALUES
-- HTML和CSS基础测验的问题
((SELECT id FROM tests WHERE title = 'HTML和CSS基础测验'), 'single',
 'HTML文档的根元素是什么？',
 '{"correct": "html"}', 2, 1, NOW()),
 
((SELECT id FROM tests WHERE title = 'HTML和CSS基础测验'), 'multiple',
 '以下哪些是块级元素？',
 '{"correct": ["div", "p", "h1"]}', 3, 2, NOW()),
 
((SELECT id FROM tests WHERE title = 'HTML和CSS基础测验'), 'truefalse',
 'CSS中，class选择器使用#符号。',
 '{"correct": false}', 1, 3, NOW()),
 
((SELECT id FROM tests WHERE title = 'HTML和CSS基础测验'), 'essay',
 '简述CSS盒模型的组成部分及其作用。',
 NULL, 4, 4, NOW()),
 
-- SQL基础测试的问题
((SELECT id FROM tests WHERE title = 'SQL基础测试'), 'single',
 'SQL中用于从表中检索数据的语句是？',
 '{"correct": "SELECT"}', 2, 1, NOW()),
 
((SELECT id FROM tests WHERE title = 'SQL基础测试'), 'multiple',
 '以下哪些是SQL的数据操作语言(DML)？',
 '{"correct": ["INSERT", "UPDATE", "DELETE"]}', 3, 2, NOW()),
 
((SELECT id FROM tests WHERE title = 'SQL基础测试'), 'truefalse',
 'SQL中的JOIN操作用于合并两个表中的行。',
 '{"correct": true}', 1, 3, NOW()),
 
((SELECT id FROM tests WHERE title = 'SQL基础测试'), 'essay',
 '解释SQL中的主键和外键概念，并给出示例。',
 NULL, 4, 4, NOW()),
 
-- React Native组件测验的问题
((SELECT id FROM tests WHERE title = 'React Native组件测验'), 'single',
 'React Native中，哪个组件用于显示文本？',
 '{"correct": "Text"}', 2, 1, NOW()),
 
((SELECT id FROM tests WHERE title = 'React Native组件测验'), 'multiple',
 '以下哪些是React Native的核心组件？',
 '{"correct": ["View", "Text", "Image"]}', 3, 2, NOW()),
 
((SELECT id FROM tests WHERE title = 'React Native组件测验'), 'truefalse',
 'React Native使用WebView渲染所有UI组件。',
 '{"correct": false}', 1, 3, NOW()),
 
((SELECT id FROM tests WHERE title = 'React Native组件测验'), 'essay',
 '描述React Native的组件生命周期及其主要方法。',
 NULL, 4, 4, NOW());

-- 插入问题选项
INSERT INTO question_options (question_id, label, value, sort_order) VALUES
-- HTML根元素问题的选项
((SELECT id FROM test_questions WHERE content = 'HTML文档的根元素是什么？'), 'html', 'html', 1),
((SELECT id FROM test_questions WHERE content = 'HTML文档的根元素是什么？'), 'body', 'body', 2),
((SELECT id FROM test_questions WHERE content = 'HTML文档的根元素是什么？'), 'head', 'head', 3),
((SELECT id FROM test_questions WHERE content = 'HTML文档的根元素是什么？'), 'document', 'document', 4),

-- 块级元素问题的选项
((SELECT id FROM test_questions WHERE content = '以下哪些是块级元素？'), 'div', 'div', 1),
((SELECT id FROM test_questions WHERE content = '以下哪些是块级元素？'), 'span', 'span', 2),
((SELECT id FROM test_questions WHERE content = '以下哪些是块级元素？'), 'p', 'p', 3),
((SELECT id FROM test_questions WHERE content = '以下哪些是块级元素？'), 'a', 'a', 4),
((SELECT id FROM test_questions WHERE content = '以下哪些是块级元素？'), 'h1', 'h1', 5),

-- SQL语句问题的选项
((SELECT id FROM test_questions WHERE content = 'SQL中用于从表中检索数据的语句是？'), 'SELECT', 'SELECT', 1),
((SELECT id FROM test_questions WHERE content = 'SQL中用于从表中检索数据的语句是？'), 'INSERT', 'INSERT', 2),
((SELECT id FROM test_questions WHERE content = 'SQL中用于从表中检索数据的语句是？'), 'UPDATE', 'UPDATE', 3),
((SELECT id FROM test_questions WHERE content = 'SQL中用于从表中检索数据的语句是？'), 'DELETE', 'DELETE', 4),

-- SQL DML问题的选项
((SELECT id FROM test_questions WHERE content = '以下哪些是SQL的数据操作语言(DML)？'), 'SELECT', 'SELECT', 1),
((SELECT id FROM test_questions WHERE content = '以下哪些是SQL的数据操作语言(DML)？'), 'INSERT', 'INSERT', 2),
((SELECT id FROM test_questions WHERE content = '以下哪些是SQL的数据操作语言(DML)？'), 'CREATE', 'CREATE', 3),
((SELECT id FROM test_questions WHERE content = '以下哪些是SQL的数据操作语言(DML)？'), 'UPDATE', 'UPDATE', 4),
((SELECT id FROM test_questions WHERE content = '以下哪些是SQL的数据操作语言(DML)？'), 'DELETE', 'DELETE', 5),
((SELECT id FROM test_questions WHERE content = '以下哪些是SQL的数据操作语言(DML)？'), 'ALTER', 'ALTER', 6),

-- React Native文本组件问题的选项
((SELECT id FROM test_questions WHERE content = 'React Native中，哪个组件用于显示文本？'), 'Text', 'Text', 1),
((SELECT id FROM test_questions WHERE content = 'React Native中，哪个组件用于显示文本？'), 'Label', 'Label', 2),
((SELECT id FROM test_questions WHERE content = 'React Native中，哪个组件用于显示文本？'), 'TextInput', 'TextInput', 3),
((SELECT id FROM test_questions WHERE content = 'React Native中，哪个组件用于显示文本？'), 'TextView', 'TextView', 4),

-- React Native核心组件问题的选项
((SELECT id FROM test_questions WHERE content = '以下哪些是React Native的核心组件？'), 'View', 'View', 1),
((SELECT id FROM test_questions WHERE content = '以下哪些是React Native的核心组件？'), 'Text', 'Text', 2),
((SELECT id FROM test_questions WHERE content = '以下哪些是React Native的核心组件？'), 'Button', 'Button', 3),
((SELECT id FROM test_questions WHERE content = '以下哪些是React Native的核心组件？'), 'Image', 'Image', 4),
((SELECT id FROM test_questions WHERE content = '以下哪些是React Native的核心组件？'), 'Dialog', 'Dialog', 5),
((SELECT id FROM test_questions WHERE content = '以下哪些是React Native的核心组件？'), 'Fragment', 'Fragment', 6);
