const mysql = require('mysql2');
require('dotenv').config();

// 打印环境变量
console.log('环境变量:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_PASSWORD长度:', process.env.DB_PASSWORD ? process.env.DB_PASSWORD.length : 0);

// 创建连接 - 手动指定密码
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '123456', // 手动指定密码
  database: 'elearning'
});

// 连接到数据库
connection.connect(err => {
  if (err) {
    console.error('数据库连接失败:', err.message);
    console.error('错误代码:', err.code);
    console.error('错误状态:', err.sqlState);
    console.error('错误详情:', err);

    // 尝试不指定数据库名称连接
    console.log('\n尝试不指定数据库名称连接...');
    const rootConnection = mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || ''
    });

    rootConnection.connect(rootErr => {
      if (rootErr) {
        console.error('无法连接到MySQL服务器:', rootErr.message);
        process.exit(1);
      }

      console.log('成功连接到MySQL服务器！');
      console.log('检查数据库是否存在...');

      // 检查数据库是否存在
      rootConnection.query(`SHOW DATABASES LIKE '${process.env.DB_NAME || 'elearning'}'`, (showErr, results) => {
        if (showErr) {
          console.error('查询数据库失败:', showErr.message);
          rootConnection.end();
          process.exit(1);
        }

        if (results.length === 0) {
          console.log(`数据库 '${process.env.DB_NAME || 'elearning'}' 不存在，需要创建。`);
          console.log('请运行 database/elearning_complete.sql 脚本创建数据库和表。');
        } else {
          console.log(`数据库 '${process.env.DB_NAME || 'elearning'}' 已存在。`);
          console.log('请确保您有正确的访问权限。');
        }

        rootConnection.end();
      });
    });

    return;
  }

  console.log('数据库连接成功！');

  // 测试查询
  connection.query('SELECT 1 + 1 AS solution', (err, results) => {
    if (err) {
      console.error('查询失败:', err.message);
    } else {
      console.log('查询结果:', results[0].solution);
    }

    // 关闭连接
    connection.end();
  });
});
