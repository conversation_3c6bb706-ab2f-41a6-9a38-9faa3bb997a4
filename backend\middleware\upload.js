const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const createUploadDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    let uploadPath = '';
    
    // 根据文件类型确定存储路径
    if (file.mimetype.startsWith('image/')) {
      uploadPath = path.join(process.env.UPLOAD_PATH, 'images');
    } else if (file.mimetype.startsWith('video/')) {
      uploadPath = path.join(process.env.UPLOAD_PATH, 'videos');
    } else if (file.mimetype === 'application/pdf') {
      uploadPath = path.join(process.env.UPLOAD_PATH, 'documents/pdf');
    } else {
      uploadPath = path.join(process.env.UPLOAD_PATH, 'documents/other');
    }
    
    // 确保目录存在
    createUploadDir(uploadPath);
    
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名：时间戳-原始文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedMimeTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'video/mp4',
    'video/webm',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain'
  ];
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

// 配置上传限制
const limits = {
  fileSize: 50 * 1024 * 1024, // 50MB
  files: 5 // 最多5个文件
};

// 创建multer实例
const upload = multer({
  storage,
  fileFilter,
  limits
});

module.exports = upload;
