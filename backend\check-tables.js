const mysql = require('mysql2');

// 创建连接
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'elearning'
});

// 连接到数据库
connection.connect(err => {
  if (err) {
    console.error('数据库连接失败:', err.message);
    process.exit(1);
  }
  
  console.log('数据库连接成功！');
  
  // 检查数据库中的表
  connection.query('SHOW TABLES', (err, results) => {
    if (err) {
      console.error('查询失败:', err.message);
      connection.end();
      process.exit(1);
    }
    
    if (results.length === 0) {
      console.log('数据库中没有表，需要创建表。');
      console.log('请运行 database/elearning_complete.sql 脚本创建表。');
    } else {
      console.log('数据库中的表:');
      results.forEach(row => {
        console.log(`- ${row[Object.keys(row)[0]]}`);
      });
    }
    
    // 关闭连接
    connection.end();
  });
});
