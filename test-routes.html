<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .route-link {
            display: block;
            padding: 10px 15px;
            margin: 5px 0;
            background-color: #409EFF;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .route-link:hover {
            background-color: #337ecc;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 10px;
        }
        .note {
            background-color: #f0f9ff;
            border: 1px solid #409EFF;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>E-Learning 平台路由测试</h1>

        <div class="note">
            <strong>注意：</strong>前端开发服务器正在运行在 http://localhost:8081/，后端服务器运行在 http://localhost:5000/
        </div>

        <div class="section">
            <h2>认证页面</h2>
            <a href="http://localhost:8081/login" class="route-link" target="_blank">登录页面</a>
            <a href="http://localhost:8081/register" class="route-link" target="_blank">注册页面</a>
        </div>

        <div class="section">
            <h2>管理员页面</h2>
            <a href="http://localhost:8081/admin" class="route-link" target="_blank">管理员仪表盘</a>
            <a href="http://localhost:8081/admin/announcements" class="route-link" target="_blank">公告管理</a>
            <a href="http://localhost:8081/admin/comments" class="route-link" target="_blank">评论管理</a>
            <a href="http://localhost:8081/admin/discussions" class="route-link" target="_blank">讨论管理</a>
            <a href="http://localhost:8081/admin/users" class="route-link" target="_blank">用户管理</a>
        </div>

        <div class="section">
            <h2>教师页面</h2>
            <a href="http://localhost:8081/teacher" class="route-link" target="_blank">教师仪表盘</a>
            <a href="http://localhost:8081/teacher/courses" class="route-link" target="_blank">课程管理</a>
            <a href="http://localhost:8081/teacher/resources" class="route-link" target="_blank">资源管理</a>
            <a href="http://localhost:8081/teacher/tests" class="route-link" target="_blank">测试管理</a>
        </div>

        <div class="section">
            <h2>学生页面</h2>
            <a href="http://localhost:8081/student" class="route-link" target="_blank">学生仪表盘</a>
            <a href="http://localhost:8081/student/courses" class="route-link" target="_blank">课程列表</a>
        </div>

        <div class="section">
            <h2>公共页面</h2>
            <a href="http://localhost:8081/" class="route-link" target="_blank">首页</a>
            <a href="http://localhost:8081/announcements" class="route-link" target="_blank">公告页面</a>
        </div>

        <div class="section">
            <h2>测试步骤</h2>
            <ol>
                <li>首先访问登录页面，使用以下凭据登录：
                    <ul>
                        <li><strong>管理员：</strong> admin / 123456</li>
                        <li><strong>教师：</strong> teacher / 123456</li>
                        <li><strong>学生：</strong> student1 / 123456</li>
                    </ul>
                </li>
                <li>登录成功后，测试相应角色的页面链接</li>
                <li>检查页面是否正常加载，没有404错误</li>
                <li>测试各个管理功能的增删查改操作</li>
            </ol>
        </div>

        <div class="section">
            <h2>后端API测试</h2>
            <p>后端服务器应该运行在：<a href="http://localhost:5000" target="_blank">http://localhost:5000</a></p>
            <p>API测试页面：<a href="http://localhost:5000/test.html" target="_blank">http://localhost:5000/test.html</a></p>
        </div>
    </div>
</body>
</html>
