const mongoose = require('mongoose');

// 问题模式
const QuestionSchema = new mongoose.Schema({
  // 问题类型：single(单选), multiple(多选), truefalse(判断), essay(简答)
  type: {
    type: String,
    enum: ['single', 'multiple', 'truefalse', 'essay'],
    required: [true, '请指定问题类型']
  },
  content: {
    type: String,
    required: [true, '请提供问题内容']
  },
  // 选项（对于单选、多选题）
  options: [{
    label: String,
    value: String
  }],
  // 正确答案
  answer: {
    type: mongoose.Schema.Types.Mixed,
    required: function() {
      return this.type !== 'essay'; // 简答题不需要预设答案
    }
  },
  // 分值
  score: {
    type: Number,
    required: [true, '请指定问题分值'],
    default: 1
  }
});

// 测试模式
const TestSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, '请提供测试标题'],
    trim: true,
    maxlength: [100, '测试标题不能超过100个字符']
  },
  description: {
    type: String
  },
  // 所属课程
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, '请指定所属课程']
  },
  // 创建者
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定创建者']
  },
  // 问题列表
  questions: [QuestionSchema],
  // 总分
  totalScore: {
    type: Number,
    default: 0
  },
  // 时间限制（分钟）
  timeLimit: {
    type: Number
  },
  // 开始时间
  startTime: {
    type: Date,
    required: [true, '请指定开始时间']
  },
  // 结束时间
  endTime: {
    type: Date,
    required: [true, '请指定结束时间']
  },
  // 是否随机排序问题
  randomizeQuestions: {
    type: Boolean,
    default: false
  },
  // 是否公开结果
  showResults: {
    type: Boolean,
    default: true
  },
  // 测试状态：draft(草稿), published(已发布), closed(已关闭)
  status: {
    type: String,
    enum: ['draft', 'published', 'closed'],
    default: 'draft'
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 计算总分
TestSchema.pre('save', function(next) {
  if (this.questions && this.questions.length > 0) {
    this.totalScore = this.questions.reduce((sum, question) => sum + question.score, 0);
  }
  next();
});

// 虚拟字段：提交数量
TestSchema.virtual('submissionCount', {
  ref: 'TestSubmission',
  localField: '_id',
  foreignField: 'test',
  count: true
});

module.exports = mongoose.model('Test', TestSchema);
