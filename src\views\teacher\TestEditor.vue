<template>
  <div class="test-editor">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else>
      <!-- 测试头部信息 -->
      <div class="test-header">
        <div class="test-info">
          <div class="test-title-row">
            <h1>{{ test.title }}</h1>
            <el-tag :type="getStatusType(test.status)">
              {{ getStatusText(test.status) }}
            </el-tag>
          </div>
          <p class="test-description">{{ test.description }}</p>
          <div class="test-meta">
            <span><i class="el-icon-date"></i> {{ formatDateTime(test.start_time) }} - {{ formatDateTime(test.end_time) }}</span>
            <span><i class="el-icon-time"></i> {{ test.time_limit ? `${test.time_limit}分钟` : '无限制' }}</span>
            <span><i class="el-icon-reading"></i> {{ test.course_title }}</span>
            <span><i class="el-icon-medal"></i> 总分: {{ test.total_score || 0 }}</span>
          </div>
        </div>
        <div class="test-actions">
          <el-button type="primary" @click="editTest">编辑测试</el-button>
          <el-dropdown trigger="click" @command="handleCommand">
            <el-button type="default">
              更多操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="publish" v-if="test.status === 'draft'">发布测试</el-dropdown-item>
              <el-dropdown-item command="close" v-if="test.status === 'published'">关闭测试</el-dropdown-item>
              <el-dropdown-item command="reopen" v-if="test.status === 'closed'">重新开放</el-dropdown-item>
              <el-dropdown-item command="submissions">查看提交</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除测试</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <!-- 问题管理 -->
      <div class="questions-section">
        <div class="section-header">
          <h2>测试问题</h2>
          <div class="section-actions">
            <el-button type="success" @click="showAddQuestionDialog">添加问题</el-button>
            <el-button type="primary" @click="showBatchAddDialog">批量添加</el-button>
          </div>
        </div>

        <div v-if="test.questions && test.questions.length === 0" class="empty-data">
          <el-empty description="暂无问题">
            <el-button type="primary" @click="showAddQuestionDialog">添加问题</el-button>
          </el-empty>
        </div>

        <div v-else class="questions-list">
          <div v-for="(question, index) in test.questions" :key="question.id" class="question-card">
            <div class="question-header">
              <div class="question-type">
                <el-tag :type="getQuestionTypeTag(question.type)">{{ getQuestionTypeText(question.type) }}</el-tag>
              </div>
              <div class="question-score">{{ question.score }}分</div>
            </div>
            <div class="question-content">
              <div class="question-number">问题 {{ index + 1 }}</div>
              <div class="question-text" v-html="question.content"></div>
            </div>
            <div class="question-options" v-if="['single', 'multiple'].includes(question.type)">
              <template v-if="question.type === 'single'">
                <div v-for="option in getQuestionOptions(question)" :key="option.value" class="option">
                  <el-radio disabled :label="option.value" :class="{ 'correct-option': isCorrectOption(question, option.value) }">
                    {{ option.label }}
                  </el-radio>
                </div>
              </template>
              <template v-else>
                <div v-for="option in getQuestionOptions(question)" :key="option.value" class="option">
                  <el-checkbox disabled :label="option.value" :class="{ 'correct-option': isCorrectOption(question, option.value) }">
                    {{ option.label }}
                  </el-checkbox>
                </div>
              </template>
            </div>
            <div class="question-answer" v-else-if="question.type === 'truefalse'">
              <el-radio-group disabled :value="getCorrectAnswer(question)">
                <el-radio label="true">正确</el-radio>
                <el-radio label="false">错误</el-radio>
              </el-radio-group>
            </div>
            <div class="question-answer" v-else-if="question.type === 'essay'">
              <div class="essay-placeholder">简答题 - 需要手动评分</div>
            </div>
            <div class="question-actions">
              <el-button type="text" @click="editQuestion(question)">编辑</el-button>
              <el-button type="text" class="danger-button" @click="deleteQuestion(question)">删除</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑测试对话框 -->
      <el-dialog title="编辑测试" :visible.sync="testDialogVisible" width="50%">
        <el-form :model="testForm" :rules="testRules" ref="testForm" label-width="100px">
          <el-form-item label="测试标题" prop="title">
            <el-input v-model="testForm.title" placeholder="请输入测试标题"></el-input>
          </el-form-item>
          <el-form-item label="测试描述" prop="description">
            <el-input
              type="textarea"
              v-model="testForm.description"
              :rows="3"
              placeholder="请输入测试描述">
            </el-input>
          </el-form-item>
          <el-form-item label="时间限制" prop="timeLimit">
            <el-input-number
              v-model="testForm.timeLimit"
              :min="0"
              :step="5"
              placeholder="分钟，0表示无限制">
            </el-input-number>
            <span class="form-tip">分钟，0表示无限制</span>
          </el-form-item>
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="testForm.startTime"
              type="datetime"
              placeholder="选择开始时间"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="testForm.endTime"
              type="datetime"
              placeholder="选择结束时间"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="随机排序" prop="randomizeQuestions">
            <el-switch v-model="testForm.randomizeQuestions"></el-switch>
            <span class="form-tip">开启后，每个学生看到的题目顺序将随机排列</span>
          </el-form-item>
          <el-form-item label="显示结果" prop="showResults">
            <el-switch v-model="testForm.showResults"></el-switch>
            <span class="form-tip">开启后，学生提交后可以查看自己的得分</span>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="testForm.status" placeholder="请选择测试状态" style="width: 100%">
              <el-option label="草稿" value="draft"></el-option>
              <el-option label="已发布" value="published"></el-option>
              <el-option label="已关闭" value="closed"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="testDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTest" :loading="saving">保存</el-button>
        </span>
      </el-dialog>

      <!-- 添加问题对话框 -->
      <el-dialog :title="questionDialogTitle" :visible.sync="questionDialogVisible" width="60%">
        <question-editor
          :question="currentQuestion"
          @save="saveQuestion"
          @cancel="questionDialogVisible = false">
        </question-editor>
      </el-dialog>

      <!-- 批量添加问题对话框 -->
      <el-dialog title="批量添加问题" :visible.sync="batchAddDialogVisible" width="70%">
        <batch-question-editor
          :test-id="id"
          @save="handleBatchSave"
          @cancel="batchAddDialogVisible = false">
        </batch-question-editor>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// 导入子组件
import QuestionEditor from '@/components/teacher/QuestionEditor.vue'
import BatchQuestionEditor from '@/components/teacher/BatchQuestionEditor.vue'

export default {
  name: 'TestEditor',
  components: {
    QuestionEditor,
    BatchQuestionEditor
  },
  props: {
    id: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      testId: parseInt(this.id),
      test: {
        questions: []
      },
      loading: true,
      testDialogVisible: false,
      questionDialogVisible: false,
      batchAddDialogVisible: false,
      questionDialogTitle: '添加问题',
      currentQuestion: null,
      testForm: {
        title: '',
        description: '',
        timeLimit: 30,
        startTime: '',
        endTime: '',
        randomizeQuestions: false,
        showResults: true,
        status: 'draft'
      },
      testRules: {
        title: [
          { required: true, message: '请输入测试标题', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ]
      },
      saving: false
    }
  },
  methods: {
    async fetchTestDetails() {
      this.loading = true
      try {
        const response = await this.$http.get(`/api/tests/${this.testId}`)

        if (response.data.success) {
          this.test = response.data.data
        } else {
          throw new Error(response.data.message || '获取测试详情失败')
        }
      } catch (error) {
        console.error('获取测试详情失败:', error)
        this.$message.error('获取测试详情失败，请稍后再试')
      } finally {
        this.loading = false
      }
    },
    formatDateTime(dateTime) {
      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm')
    },
    getStatusType(status) {
      const types = {
        'draft': 'info',
        'published': 'success',
        'closed': 'warning'
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        'draft': '草稿',
        'published': '已发布',
        'closed': '已关闭'
      }
      return texts[status] || status
    },
    getQuestionTypeTag(type) {
      const types = {
        'single': '',
        'multiple': 'success',
        'truefalse': 'info',
        'essay': 'warning'
      }
      return types[type] || ''
    },
    getQuestionTypeText(type) {
      const texts = {
        'single': '单选题',
        'multiple': '多选题',
        'truefalse': '判断题',
        'essay': '简答题'
      }
      return texts[type] || type
    },
    getQuestionOptions(question) {
      try {
        if (question.type === 'single' || question.type === 'multiple') {
          const answer = JSON.parse(question.answer)
          return answer.options || []
        }
        return []
      } catch (error) {
        console.error('解析问题选项失败:', error)
        return []
      }
    },
    isCorrectOption(question, value) {
      try {
        if (question.type === 'single') {
          const answer = JSON.parse(question.answer)
          return answer.correct === value
        } else if (question.type === 'multiple') {
          const answer = JSON.parse(question.answer)
          return answer.correct.includes(value)
        }
        return false
      } catch (error) {
        console.error('检查正确选项失败:', error)
        return false
      }
    },
    getCorrectAnswer(question) {
      try {
        if (question.type === 'truefalse') {
          const answer = JSON.parse(question.answer)
          return answer.correct
        }
        return null
      } catch (error) {
        console.error('获取正确答案失败:', error)
        return null
      }
    },
    editTest() {
      this.testForm = {
        title: this.test.title,
        description: this.test.description,
        timeLimit: this.test.time_limit,
        startTime: this.test.start_time,
        endTime: this.test.end_time,
        randomizeQuestions: Boolean(this.test.randomize_questions),
        showResults: Boolean(this.test.show_results),
        status: this.test.status
      }
      this.testDialogVisible = true
    },
    async saveTest() {
      this.$refs.testForm.validate(async valid => {
        if (valid) {
          // 检查开始时间和结束时间
          if (new Date(this.testForm.endTime) <= new Date(this.testForm.startTime)) {
            this.$message.error('结束时间必须晚于开始时间')
            return
          }

          this.saving = true

          try {
            const testData = {
              title: this.testForm.title,
              description: this.testForm.description,
              timeLimit: this.testForm.timeLimit,
              startTime: this.testForm.startTime,
              endTime: this.testForm.endTime,
              randomizeQuestions: this.testForm.randomizeQuestions,
              showResults: this.testForm.showResults,
              status: this.testForm.status
            }

            const response = await this.$http.put(`/api/tests/${this.testId}`, testData)

            if (response.data.success) {
              this.$message.success('测试更新成功')
              this.testDialogVisible = false

              // 更新本地数据
              Object.assign(this.test, response.data.data)
            } else {
              throw new Error(response.data.message || '更新测试失败')
            }
          } catch (error) {
            console.error('保存测试失败:', error)
            this.$message.error(error.message || '保存测试失败，请稍后再试')
          } finally {
            this.saving = false
          }
        }
      })
    },
    async handleCommand(command) {
      try {
        if (command === 'publish') {
          await this.changeTestStatus('published', '发布')
        } else if (command === 'close') {
          await this.changeTestStatus('closed', '关闭')
        } else if (command === 'reopen') {
          await this.changeTestStatus('published', '重新开放')
        } else if (command === 'submissions') {
          this.$router.push(`/teacher/tests/${this.testId}/submissions`)
        } else if (command === 'delete') {
          await this.deleteTest()
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败，请稍后再试')
      }
    },
    async changeTestStatus(status, actionText) {
      try {
        await this.$confirm(`确定要${actionText}测试 "${this.test.title}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await this.$http.put(`/api/tests/${this.testId}`, {
          title: this.test.title,
          description: this.test.description,
          timeLimit: this.test.time_limit,
          startTime: this.test.start_time,
          endTime: this.test.end_time,
          randomizeQuestions: Boolean(this.test.randomize_questions),
          showResults: Boolean(this.test.show_results),
          status: status
        })

        if (response.data.success) {
          this.$message.success(`测试${actionText}成功`)

          // 更新本地数据
          this.test.status = status
        } else {
          throw new Error(response.data.message || `${actionText}测试失败`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          throw error
        }
      }
    },
    async deleteTest() {
      try {
        await this.$confirm(`确定要删除测试 "${this.test.title}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await this.$http.delete(`/api/tests/${this.testId}`)

        if (response.data.success) {
          this.$message.success('测试删除成功')

          // 返回课程详情页面
          this.$router.push(`/teacher/courses/${this.test.course_id}`)
        } else {
          throw new Error(response.data.message || '删除测试失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          throw error
        }
      }
    },
    showAddQuestionDialog() {
      this.questionDialogTitle = '添加问题'
      this.currentQuestion = {
        testId: this.testId,
        type: 'single',
        content: '',
        options: [
          { label: '选项A', value: 'A' },
          { label: '选项B', value: 'B' },
          { label: '选项C', value: 'C' },
          { label: '选项D', value: 'D' }
        ],
        answer: 'A',
        score: 2,
        sortOrder: this.test.questions.length
      }
      this.questionDialogVisible = true
    },
    editQuestion(question) {
      this.questionDialogTitle = '编辑问题'

      // 解析问题数据
      let parsedQuestion = {
        id: question.id,
        testId: this.testId,
        type: question.type,
        content: question.content,
        score: question.score,
        sortOrder: question.sort_order
      }

      try {
        if (question.type === 'single' || question.type === 'multiple') {
          const answer = JSON.parse(question.answer)
          parsedQuestion.options = answer.options || []
          parsedQuestion.answer = answer.correct
        } else if (question.type === 'truefalse') {
          const answer = JSON.parse(question.answer)
          parsedQuestion.answer = answer.correct
        } else if (question.type === 'essay') {
          // 简答题没有预设答案
          parsedQuestion.answer = ''
        }
      } catch (error) {
        console.error('解析问题数据失败:', error)
      }

      this.currentQuestion = parsedQuestion
      this.questionDialogVisible = true
    },
    async saveQuestion(questionData) {
      try {
        let response

        if (questionData.id) {
          // 更新现有问题
          response = await this.$http.put(`/api/questions/questions/${questionData.id}`, questionData)
        } else {
          // 添加新问题
          response = await this.$http.post(`/api/questions/tests/${this.testId}/questions`, questionData)
        }

        if (response.data.success) {
          this.$message.success(questionData.id ? '问题更新成功' : '问题添加成功')
          this.questionDialogVisible = false

          // 重新获取测试详情
          await this.fetchTestDetails()
        } else {
          throw new Error(response.data.message || '保存问题失败')
        }
      } catch (error) {
        console.error('保存问题失败:', error)
        this.$message.error(error.message || '保存问题失败，请稍后再试')
      }
    },
    async deleteQuestion(question) {
      try {
        await this.$confirm('确定要删除此问题吗？此操作不可逆。', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await this.$http.delete(`/api/questions/questions/${question.id}`)

        if (response.data.success) {
          this.$message.success('问题删除成功')

          // 重新获取测试详情
          await this.fetchTestDetails()
        } else {
          throw new Error(response.data.message || '删除问题失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除问题失败:', error)
          this.$message.error(error.message || '删除问题失败，请稍后再试')
        }
      }
    },
    showBatchAddDialog() {
      this.batchAddDialogVisible = true
    },
    async handleBatchSave() {
      this.batchAddDialogVisible = false
      await this.fetchTestDetails()
      this.$message.success('批量添加问题成功')
    }
  },
  created() {
    this.fetchTestDetails()
  }
}
</script>

<style scoped>
.test-editor {
  padding: 20px;
}

.loading-container {
  padding: 20px;
}

.test-header {
  display: flex;
  margin-bottom: 30px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
}

.test-info {
  flex: 1;
}

.test-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.test-title-row h1 {
  margin: 0;
  margin-right: 10px;
}

.test-description {
  margin-bottom: 15px;
  color: #606266;
}

.test-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #909399;
}

.test-actions {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 10px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 10px;
}

.questions-section {
  margin-top: 30px;
}

.empty-data {
  padding: 40px 0;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.question-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.question-content {
  margin-bottom: 15px;
}

.question-number {
  font-weight: bold;
  margin-bottom: 5px;
  color: #409EFF;
}

.question-text {
  font-size: 16px;
}

.question-options {
  margin-top: 10px;
  margin-bottom: 15px;
}

.option {
  margin-bottom: 5px;
}

.correct-option {
  color: #67C23A;
  font-weight: bold;
}

.question-answer {
  margin-top: 10px;
  margin-bottom: 15px;
}

.essay-placeholder {
  color: #909399;
  font-style: italic;
}

.question-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.danger-button {
  color: #F56C6C;
}

.danger-button:hover {
  color: #F78989;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .test-header {
    flex-direction: column;
  }

  .test-actions {
    margin-top: 15px;
    flex-direction: row;
  }
}
</style>
