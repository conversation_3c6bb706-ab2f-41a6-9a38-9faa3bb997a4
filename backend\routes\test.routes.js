const express = require('express');
const { pool } = require('../config/db');

const router = express.Router();

// GET 测试路由 - 测试数据库连接
router.get('/db-connection', async (req, res) => {
  try {
    const connection = await pool.getConnection();
    connection.release();
    res.status(200).json({
      success: true,
      message: '数据库连接成功！'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '数据库连接失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取所有用户
router.get('/users', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT id, username, email, role, avatar FROM users');
    res.status(200).json({
      success: true,
      count: rows.length,
      data: rows
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取用户详情
router.get('/users/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const [rows] = await pool.query(
      'SELECT id, username, email, role, avatar, teacher_title, teacher_department, teacher_bio, student_id, student_grade, student_major FROM users WHERE id = ?',
      [userId]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到用户'
      });
    }

    res.status(200).json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户详情失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取所有课程
router.get('/courses', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT c.*, u.username as teacher_name
      FROM courses c
      JOIN users u ON c.teacher_id = u.id
    `);
    res.status(200).json({
      success: true,
      count: rows.length,
      data: rows
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取课程失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取公告
router.get('/announcements', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT a.*, u.username as author_name
      FROM announcements a
      JOIN users u ON a.author_id = u.id
      ORDER BY a.is_pinned DESC, a.publish_date DESC
    `);

    // 获取课程信息
    for (let i = 0; i < rows.length; i++) {
      if (rows[i].course_id) {
        const [courseRows] = await pool.query(
          'SELECT id, title FROM courses WHERE id = ?',
          [rows[i].course_id]
        );
        if (courseRows.length > 0) {
          rows[i].course = courseRows[0];
        }
      }
    }

    res.status(200).json({
      success: true,
      count: rows.length,
      data: rows
    });
  } catch (error) {
    console.error('获取公告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公告失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取教师课程
router.get('/teacher-courses', async (req, res) => {
  try {
    // 模拟数据
    const data = [
      {
        id: 1,
        title: 'Web开发基础',
        description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
        category: '计算机科学',
        status: 'published',
        student_count: 25,
        resource_count: 12,
        test_count: 3,
        start_date: '2023-09-01',
        end_date: '2024-01-15'
      },
      {
        id: 2,
        title: '数据库系统',
        description: '关系型数据库设计、SQL查询和数据库管理系统。',
        category: '计算机科学',
        status: 'published',
        student_count: 18,
        resource_count: 8,
        test_count: 2,
        start_date: '2023-09-01',
        end_date: '2024-01-15'
      }
    ];

    res.status(200).json({
      success: true,
      count: data.length,
      data: data
    });
  } catch (error) {
    console.error('获取教师课程失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教师课程失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取教师测试提交
router.get('/teacher-submissions', async (req, res) => {
  try {
    // 模拟数据
    const data = [
      {
        id: 1,
        student_name: '张三',
        test_title: 'JavaScript基础测试',
        course_id: 1,
        test_id: 1,
        status: 'submitted',
        submit_time: '2023-09-15T10:30:00Z'
      },
      {
        id: 2,
        student_name: '李四',
        test_title: 'CSS布局测试',
        course_id: 1,
        test_id: 2,
        status: 'graded',
        submit_time: '2023-09-14T14:20:00Z',
        score: 85
      },
      {
        id: 3,
        student_name: '王五',
        test_title: 'SQL基础测试',
        course_id: 2,
        test_id: 3,
        status: 'submitted',
        submit_time: '2023-09-15T09:10:00Z'
      }
    ];

    res.status(200).json({
      success: true,
      count: data.length,
      data: data
    });
  } catch (error) {
    console.error('获取教师测试提交失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教师测试提交失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取教师统计数据
router.get('/teacher-stats', async (req, res) => {
  try {
    // 模拟数据
    const data = {
      courseCount: 2,
      studentCount: 43,
      resourceCount: 20,
      testCount: 5
    };

    res.status(200).json({
      success: true,
      data: data
    });
  } catch (error) {
    console.error('获取教师统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教师统计数据失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取管理员统计数据
router.get('/admin-stats', async (req, res) => {
  try {
    // 模拟数据
    const data = {
      userCount: 36,
      courseCount: 13,
      resourceCount: 87,
      discussionCount: 45
    };

    res.status(200).json({
      success: true,
      data: data
    });
  } catch (error) {
    console.error('获取管理员统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取管理员统计数据失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取管理员用户列表
router.get('/admin-users', async (req, res) => {
  try {
    // 模拟数据
    const data = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        created_at: '2023-08-01T08:00:00Z',
        last_login: '2023-09-15T10:30:00Z',
        login_count: 42,
        last_ip: '127.0.0.1'
      },
      {
        id: 2,
        username: 'teacher1',
        email: '<EMAIL>',
        role: 'teacher',
        status: 'active',
        created_at: '2023-08-15T10:30:00Z',
        last_login: '2023-09-14T14:20:00Z',
        login_count: 28,
        last_ip: '*************'
      },
      {
        id: 3,
        username: 'student1',
        email: '<EMAIL>',
        role: 'student',
        status: 'active',
        created_at: '2023-08-20T09:15:00Z',
        last_login: '2023-09-15T09:10:00Z',
        login_count: 15,
        last_ip: '*************'
      }
    ];

    res.status(200).json({
      success: true,
      count: data.length,
      data: data
    });
  } catch (error) {
    console.error('获取管理员用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取管理员用户列表失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取管理员日志
router.get('/admin-logs', async (req, res) => {
  try {
    // 模拟数据
    const data = [
      {
        level: 'info',
        message: '系统启动',
        time: new Date(Date.now() - 3600000 * 24)
      },
      {
        level: 'success',
        message: '新用户注册',
        details: '<EMAIL>',
        time: new Date(Date.now() - 3600000 * 12)
      },
      {
        level: 'warning',
        message: '登录尝试失败',
        details: 'IP: *************',
        time: new Date(Date.now() - 3600000 * 6)
      },
      {
        level: 'error',
        message: '数据库连接错误',
        details: 'Connection timeout',
        time: new Date(Date.now() - 3600000 * 2)
      }
    ];

    res.status(200).json({
      success: true,
      count: data.length,
      data: data
    });
  } catch (error) {
    console.error('获取管理员日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取管理员日志失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取课程详情
router.get('/courses/:id', async (req, res) => {
  try {
    const courseId = req.params.id;

    // 获取课程基本信息
    const [courseRows] = await pool.query(`
      SELECT c.*, u.username as teacher_name
      FROM courses c
      JOIN users u ON c.teacher_id = u.id
      WHERE c.id = ?
    `, [courseId]);

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到课程'
      });
    }

    const course = courseRows[0];

    // 获取课程标签
    const [tagRows] = await pool.query(
      'SELECT tag FROM course_tags WHERE course_id = ?',
      [courseId]
    );

    // 获取课程学生
    const [studentRows] = await pool.query(`
      SELECT u.id, u.username, u.email, u.avatar, cs.enrollment_date
      FROM course_students cs
      JOIN users u ON cs.student_id = u.id
      WHERE cs.course_id = ?
    `, [courseId]);

    // 获取课程资源数量
    const [resourceCountRows] = await pool.query(
      'SELECT COUNT(*) as count FROM resources WHERE course_id = ?',
      [courseId]
    );

    // 组合数据
    course.tags = tagRows.map(row => row.tag);
    course.students = studentRows;
    course.resourceCount = resourceCountRows[0].count;

    res.status(200).json({
      success: true,
      data: course
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取课程详情失败',
      error: error.message
    });
  }
});

// GET 测试路由 - 获取课程资源
router.get('/courses/:id/resources', async (req, res) => {
  try {
    const courseId = req.params.id;

    const [rows] = await pool.query(`
      SELECT r.*, u.username as uploader_name
      FROM resources r
      JOIN users u ON r.uploader_id = u.id
      WHERE r.course_id = ?
      ORDER BY r.created_at DESC
    `, [courseId]);

    res.status(200).json({
      success: true,
      count: rows.length,
      data: rows
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取课程资源失败',
      error: error.message
    });
  }
});

// POST 测试路由 - 创建测试公告
router.post('/announcement', async (req, res) => {
  try {
    const { title, content } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '请提供标题和内容'
      });
    }

    // 获取管理员用户ID
    const [adminRows] = await pool.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');

    if (adminRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到管理员用户'
      });
    }

    const adminId = adminRows[0].id;

    // 插入公告
    const [result] = await pool.query(
      'INSERT INTO announcements (title, content, type, author_id, is_important, publish_date) VALUES (?, ?, ?, ?, ?, NOW())',
      [title, content, 'system', adminId, true]
    );

    res.status(201).json({
      success: true,
      message: '公告创建成功',
      data: {
        id: result.insertId,
        title,
        content
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建公告失败',
      error: error.message
    });
  }
});

// POST 测试路由 - 学生选课
router.post('/enroll', async (req, res) => {
  try {
    const { studentId, courseCode } = req.body;

    if (!studentId || !courseCode) {
      return res.status(400).json({
        success: false,
        message: '请提供学生ID和课程代码'
      });
    }

    // 查找课程
    const [courseRows] = await pool.query('SELECT id FROM courses WHERE course_code = ?', [courseCode]);

    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到课程'
      });
    }

    const courseId = courseRows[0].id;

    // 检查是否已经选课
    const [enrollmentRows] = await pool.query(
      'SELECT id FROM course_students WHERE course_id = ? AND student_id = ?',
      [courseId, studentId]
    );

    if (enrollmentRows.length > 0) {
      return res.status(400).json({
        success: false,
        message: '已经选修了该课程'
      });
    }

    // 添加选课记录
    await pool.query(
      'INSERT INTO course_students (course_id, student_id, enrollment_date) VALUES (?, ?, NOW())',
      [courseId, studentId]
    );

    res.status(201).json({
      success: true,
      message: '选课成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '选课失败',
      error: error.message
    });
  }
});

module.exports = router;
