const express = require('express');
const { check } = require('express-validator');
const {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  updateProfile
} = require('../controllers/user.controller');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// 保护所有路由
router.use(protect);

// 个人资料路由
router.put(
  '/profile',
  [
    check('username', '用户名长度应在3-20个字符之间').optional().isLength({ min: 3, max: 20 }),
    check('email', '请提供有效的邮箱').optional().isEmail()
  ],
  updateProfile
);

// 管理员路由
router.use(authorize('admin'));

router
  .route('/')
  .get(getUsers)
  .post(
    [
      check('username', '用户名是必需的').not().isEmpty(),
      check('username', '用户名长度应在3-20个字符之间').isLength({ min: 3, max: 20 }),
      check('email', '请提供有效的邮箱').isEmail(),
      check('password', '密码长度应至少为6个字符').isLength({ min: 6 }),
      check('role', '角色是必需的').not().isEmpty(),
      check('role', '角色无效').isIn(['admin', 'teacher', 'student'])
    ],
    createUser
  );

router
  .route('/:id')
  .get(getUser)
  .put(
    [
      check('username', '用户名长度应在3-20个字符之间').optional().isLength({ min: 3, max: 20 }),
      check('email', '请提供有效的邮箱').optional().isEmail(),
      check('role', '角色无效').optional().isIn(['admin', 'teacher', 'student'])
    ],
    updateUser
  )
  .delete(deleteUser);

module.exports = router;
