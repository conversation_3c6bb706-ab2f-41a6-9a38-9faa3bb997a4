<template>
  <div class="course-attendance">
    <div class="filter-bar">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions"
        @change="fetchAttendance">
      </el-date-picker>
      <el-select v-model="statusFilter" placeholder="出勤状态" clearable @change="fetchAttendance">
        <el-option label="出席" value="present"></el-option>
        <el-option label="缺席" value="absent"></el-option>
        <el-option label="迟到" value="late"></el-option>
        <el-option label="请假" value="excused"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchAttendance">搜索</el-button>
    </div>
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>
    
    <div v-else>
      <!-- 出勤统计 -->
      <el-card class="attendance-stats">
        <div slot="header" class="card-header">
          <span>出勤统计</span>
        </div>
        <el-row :gutter="20">
          <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总课时</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
            <div class="stat-item present">
              <div class="stat-value">{{ stats.present }}</div>
              <div class="stat-label">出席</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
            <div class="stat-item absent">
              <div class="stat-value">{{ stats.absent }}</div>
              <div class="stat-label">缺席</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
            <div class="stat-item late">
              <div class="stat-value">{{ stats.late }}</div>
              <div class="stat-label">迟到</div>
            </div>
          </el-col>
        </el-row>
        <div class="attendance-rate">
          <div class="rate-label">出勤率</div>
          <el-progress 
            :percentage="attendanceRate" 
            :color="getAttendanceRateColor()"
            :format="formatAttendanceRate">
          </el-progress>
        </div>
      </el-card>
      
      <!-- 出勤记录 -->
      <el-card class="attendance-records">
        <div slot="header" class="card-header">
          <span>出勤记录</span>
        </div>
        
        <div v-if="records.length === 0" class="empty-data">
          <el-empty description="暂无出勤记录"></el-empty>
        </div>
        
        <el-table v-else :data="records" style="width: 100%">
          <el-table-column prop="date" label="日期" width="150">
            <template slot-scope="scope">
              {{ formatDate(scope.row.date) }}
            </template>
          </el-table-column>
          <el-table-column prop="class_time" label="时间" width="150">
            <template slot-scope="scope">
              {{ scope.row.start_time }} - {{ scope.row.end_time }}
            </template>
          </el-table-column>
          <el-table-column prop="topic" label="课程主题" min-width="200"></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="check_in_time" label="签到时间" width="150">
            <template slot-scope="scope">
              <span v-if="scope.row.check_in_time">{{ scope.row.check_in_time }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="note" label="备注" min-width="200">
            <template slot-scope="scope">
              <span v-if="scope.row.note">{{ scope.row.note }}</span>
              <span v-else class="no-data">无备注</span>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            @current-change="handlePageChange">
          </el-pagination>
        </div>
      </el-card>
      
      <!-- 出勤日历 -->
      <el-card class="attendance-calendar">
        <div slot="header" class="card-header">
          <span>出勤日历</span>
        </div>
        <div class="calendar-container">
          <el-calendar v-model="calendarDate">
            <template slot="dateCell" slot-scope="{date, data}">
              <div class="calendar-day" :class="getCalendarDayClass(date)">
                <div class="day-number">{{ data.day.split('-').slice(-1)[0] }}</div>
                <div class="day-status" v-if="hasAttendanceRecord(date)">
                  <el-tag 
                    size="mini" 
                    :type="getStatusType(getAttendanceStatus(date))"
                    :effect="getTagEffect(date)">
                    {{ getStatusText(getAttendanceStatus(date)) }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-calendar>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CourseAttendance',
  props: {
    courseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      records: [],
      loading: false,
      dateRange: null,
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      stats: {
        total: 0,
        present: 0,
        absent: 0,
        late: 0,
        excused: 0
      },
      calendarDate: new Date(),
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  computed: {
    attendanceRate() {
      if (this.stats.total === 0) return 0
      return Math.round((this.stats.present / this.stats.total) * 100)
    }
  },
  methods: {
    async fetchAttendance() {
      this.loading = true
      try {
        // 实际应该从API获取数据
        // const response = await this.$http.get(`/student/courses/${this.courseId}/attendance`, {
        //   params: {
        //     start_date: this.dateRange ? this.$moment(this.dateRange[0]).format('YYYY-MM-DD') : null,
        //     end_date: this.dateRange ? this.$moment(this.dateRange[1]).format('YYYY-MM-DD') : null,
        //     status: this.statusFilter,
        //     page: this.currentPage,
        //     limit: this.pageSize
        //   }
        // })
        // this.records = response.data.data
        // this.total = response.data.total
        // this.stats = response.data.stats
        
        // 使用模拟数据
        setTimeout(() => {
          this.records = [
            {
              id: 1,
              date: '2023-09-01',
              start_time: '09:00',
              end_time: '10:30',
              topic: 'JavaScript基础：变量和数据类型',
              status: 'present',
              check_in_time: '08:55',
              note: null
            },
            {
              id: 2,
              date: '2023-09-03',
              start_time: '09:00',
              end_time: '10:30',
              topic: 'JavaScript基础：函数和作用域',
              status: 'present',
              check_in_time: '09:05',
              note: null
            },
            {
              id: 3,
              date: '2023-09-08',
              start_time: '09:00',
              end_time: '10:30',
              topic: 'JavaScript基础：对象和原型',
              status: 'late',
              check_in_time: '09:15',
              note: '迟到15分钟'
            },
            {
              id: 4,
              date: '2023-09-10',
              start_time: '09:00',
              end_time: '10:30',
              topic: 'JavaScript基础：DOM操作',
              status: 'absent',
              check_in_time: null,
              note: '未请假'
            },
            {
              id: 5,
              date: '2023-09-15',
              start_time: '09:00',
              end_time: '10:30',
              topic: 'JavaScript基础：事件处理',
              status: 'excused',
              check_in_time: null,
              note: '因病请假'
            }
          ]
          
          this.total = 5
          
          this.stats = {
            total: 5,
            present: 2,
            absent: 1,
            late: 1,
            excused: 1
          }
          
          this.loading = false
        }, 1000)
      } catch (error) {
        console.error('获取出勤记录失败:', error)
        this.$message.error('获取出勤记录失败，请稍后再试')
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchAttendance()
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD')
    },
    getStatusType(status) {
      const types = {
        'present': 'success',
        'absent': 'danger',
        'late': 'warning',
        'excused': 'info'
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        'present': '出席',
        'absent': '缺席',
        'late': '迟到',
        'excused': '请假'
      }
      return texts[status] || status
    },
    formatAttendanceRate(percentage) {
      return percentage + '%'
    },
    getAttendanceRateColor() {
      if (this.attendanceRate >= 90) {
        return '#67C23A' // 绿色
      } else if (this.attendanceRate >= 75) {
        return '#E6A23C' // 黄色
      } else {
        return '#F56C6C' // 红色
      }
    },
    hasAttendanceRecord(date) {
      const dateStr = this.$moment(date).format('YYYY-MM-DD')
      return this.records.some(record => record.date === dateStr)
    },
    getAttendanceStatus(date) {
      const dateStr = this.$moment(date).format('YYYY-MM-DD')
      const record = this.records.find(record => record.date === dateStr)
      return record ? record.status : null
    },
    getCalendarDayClass(date) {
      const dateStr = this.$moment(date).format('YYYY-MM-DD')
      const record = this.records.find(record => record.date === dateStr)
      
      if (!record) return ''
      
      return `has-attendance ${record.status}`
    },
    getTagEffect(date) {
      const today = this.$moment().format('YYYY-MM-DD')
      const dateStr = this.$moment(date).format('YYYY-MM-DD')
      
      return dateStr === today ? 'dark' : 'light'
    }
  },
  created() {
    this.fetchAttendance()
  }
}
</script>

<style scoped>
.course-attendance {
  padding: 10px 0;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.no-data {
  color: #909399;
  font-style: italic;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.attendance-stats, .attendance-records, .attendance-calendar {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.stat-item.present {
  background-color: #f0f9eb;
}

.stat-item.absent {
  background-color: #fef0f0;
}

.stat-item.late {
  background-color: #fdf6ec;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  color: #606266;
}

.attendance-rate {
  margin-top: 20px;
}

.rate-label {
  margin-bottom: 10px;
  font-weight: bold;
}

.calendar-container {
  margin-top: 10px;
}

.calendar-day {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.day-number {
  text-align: right;
  padding: 5px;
}

.day-status {
  text-align: center;
  padding-bottom: 5px;
}

.has-attendance.present {
  background-color: rgba(103, 194, 58, 0.1);
}

.has-attendance.absent {
  background-color: rgba(245, 108, 108, 0.1);
}

.has-attendance.late {
  background-color: rgba(230, 162, 60, 0.1);
}

.has-attendance.excused {
  background-color: rgba(144, 147, 153, 0.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
  }
  
  .filter-bar .el-date-editor {
    width: 100% !important;
  }
}
</style>
