const { pool } = require('./config/db');

async function importDiscussions() {
  try {
    console.log('开始导入讨论数据...');

    // 获取课程ID
    const [courses] = await pool.query('SELECT id, title FROM courses LIMIT 5');
    if (courses.length === 0) {
      console.error('没有找到课程数据，请先导入课程数据');
      process.exit(1);
    }

    // 获取用户ID
    const [users] = await pool.query('SELECT id, username, role FROM users WHERE role IN ("student", "teacher")');
    if (users.length === 0) {
      console.error('没有找到用户数据，请先导入用户数据');
      process.exit(1);
    }

    const students = users.filter(user => user.role === 'student');
    const teachers = users.filter(user => user.role === 'teacher');

    if (students.length === 0 || teachers.length === 0) {
      console.error('没有找到学生或教师用户，请确保用户数据中包含学生和教师角色');
      process.exit(1);
    }

    // 清空现有数据
    await pool.query('DELETE FROM discussion_replies');
    await pool.query('DELETE FROM discussions');

    // 重置自增ID
    await pool.query('ALTER TABLE discussions AUTO_INCREMENT = 1');
    await pool.query('ALTER TABLE discussion_replies AUTO_INCREMENT = 1');

    // 打印课程和用户信息以进行调试
    console.log('课程信息:', courses);
    console.log('学生信息:', students);
    console.log('教师信息:', teachers);

    // 使用已知存在的用户ID
    const studentId = students.length > 0 ? students[0].id : 1;
    const teacherId = teachers.length > 0 ? teachers[0].id : 2;

    // 插入讨论数据
    const discussionData = [
      {
        title: 'Web开发中的响应式设计问题',
        content: '我在实现响应式设计时遇到了一些问题，特别是在移动设备上的显示不正确。有没有好的解决方案？',
        course_id: courses.length > 0 ? courses[0].id : 1,
        author_id: studentId,
        is_pinned: 0,
        view_count: 15,
        likes: 3,
        created_at: new Date('2025-05-10 14:30:00'),
        last_activity: new Date('2025-05-11 09:45:00')
      },
      {
        title: '关于JavaScript异步编程的讨论',
        content: '我对Promise和async/await的使用有些困惑，特别是在处理多个异步操作时。谁能分享一些最佳实践？',
        course_id: courses.length > 0 ? courses[0].id : 1,
        author_id: studentId,
        is_pinned: 1,
        view_count: 28,
        likes: 7,
        created_at: new Date('2025-05-12 10:15:00'),
        last_activity: new Date('2025-05-13 16:20:00')
      },
      {
        title: '数据库索引优化问题',
        content: '在处理大量数据时，我发现查询性能很差。如何正确使用索引来优化查询性能？',
        course_id: courses.length > 1 ? courses[1].id : 2,
        author_id: studentId,
        is_pinned: 0,
        view_count: 12,
        likes: 2,
        created_at: new Date('2025-05-14 09:30:00'),
        last_activity: new Date('2025-05-14 14:45:00')
      },
      {
        title: '期末项目合作伙伴招募',
        content: '我正在寻找期末项目的合作伙伴，项目主题是开发一个在线学习平台。有兴趣的同学请回复。',
        course_id: courses.length > 2 ? courses[2].id : 3,
        author_id: studentId,
        is_pinned: 1,
        view_count: 35,
        likes: 10,
        created_at: new Date('2025-05-15 11:00:00'),
        last_activity: new Date('2025-05-16 15:30:00')
      },
      {
        title: '关于课程作业截止日期的疑问',
        content: '老师，我想确认一下第三次作业的截止日期是否为5月20日？',
        course_id: courses.length > 0 ? courses[0].id : 1,
        author_id: studentId,
        is_pinned: 0,
        view_count: 8,
        likes: 0,
        created_at: new Date('2025-05-17 16:45:00'),
        last_activity: new Date('2025-05-17 17:30:00')
      }
    ];

    for (const discussion of discussionData) {
      const [result] = await pool.query(
        `INSERT INTO discussions
         (title, content, course_id, author_id, is_pinned, view_count, likes, created_at, last_activity)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          discussion.title,
          discussion.content,
          discussion.course_id,
          discussion.author_id,
          discussion.is_pinned,
          discussion.view_count,
          discussion.likes,
          discussion.created_at,
          discussion.last_activity
        ]
      );

      console.log(`已插入讨论: ${discussion.title}, ID: ${result.insertId}`);
    }

    // 插入讨论回复数据
    const replyData = [
      {
        discussion_id: 1,
        author_id: teacherId,
        content: '你可以尝试使用媒体查询和弹性布局来解决响应式设计问题。我建议查看Bootstrap或Tailwind CSS的文档，它们提供了很好的响应式设计解决方案。',
        likes: 5,
        created_at: new Date('2025-05-10 15:45:00')
      },
      {
        discussion_id: 1,
        author_id: studentId,
        content: '我也遇到过类似的问题，使用Flexbox和Grid布局解决了大部分问题。',
        likes: 2,
        created_at: new Date('2025-05-10 16:30:00')
      },
      {
        discussion_id: 2,
        author_id: teacherId,
        content: 'async/await是处理异步操作的强大工具。关键是理解它们是基于Promise的语法糖。我建议先掌握Promise的基本用法，然后再学习async/await。',
        likes: 8,
        created_at: new Date('2025-05-12 11:20:00')
      },
      {
        discussion_id: 2,
        author_id: studentId,
        content: '我发现使用async/await比直接使用Promise更容易理解和维护代码。',
        likes: 3,
        created_at: new Date('2025-05-12 14:15:00')
      },
      {
        discussion_id: 3,
        author_id: teacherId,
        content: '索引优化是一个复杂的话题。首先，确保为经常在WHERE子句中使用的列创建索引。其次，避免在索引列上使用函数，这会阻止使用索引。',
        likes: 4,
        created_at: new Date('2025-05-14 10:45:00')
      },
      {
        discussion_id: 4,
        author_id: studentId,
        content: '我有兴趣参与你的项目！我在前端开发方面有一些经验，特别是Vue.js。',
        likes: 1,
        created_at: new Date('2025-05-15 13:30:00')
      },
      {
        discussion_id: 4,
        author_id: studentId,
        content: '我也想加入！我擅长后端开发，特别是Node.js和Express。',
        likes: 1,
        created_at: new Date('2025-05-15 14:45:00')
      },
      {
        discussion_id: 5,
        author_id: teacherId,
        content: '是的，第三次作业的截止日期是5月20日。请确保在截止日期前提交。',
        likes: 0,
        created_at: new Date('2025-05-17 17:15:00')
      }
    ];

    for (const reply of replyData) {
      const [result] = await pool.query(
        `INSERT INTO discussion_replies
         (discussion_id, author_id, content, likes, created_at)
         VALUES (?, ?, ?, ?, ?)`,
        [
          reply.discussion_id,
          reply.author_id,
          reply.content,
          reply.likes,
          reply.created_at
        ]
      );

      console.log(`已插入回复: ID: ${result.insertId}, 讨论ID: ${reply.discussion_id}`);
    }

    // 检查discussions表是否有reply_count列
    try {
      const [columns] = await pool.query('DESCRIBE discussions');
      const hasReplyCount = columns.some(col => col.Field === 'reply_count');

      if (hasReplyCount) {
        // 更新讨论的回复数
        for (let i = 1; i <= discussionData.length; i++) {
          const [replies] = await pool.query('SELECT COUNT(*) as count FROM discussion_replies WHERE discussion_id = ?', [i]);
          await pool.query('UPDATE discussions SET reply_count = ? WHERE id = ?', [replies[0].count, i]);
          console.log(`已更新讨论ID ${i} 的回复数为 ${replies[0].count}`);
        }
      } else {
        console.log('discussions表中没有reply_count列，跳过更新回复数');
      }
    } catch (error) {
      console.error('检查reply_count列失败:', error);
    }

    console.log('讨论数据导入完成！');
    process.exit(0);
  } catch (error) {
    console.error('导入讨论数据失败:', error);
    process.exit(1);
  }
}

importDiscussions();
