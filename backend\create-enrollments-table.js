const { pool } = require('./config/db');

async function createEnrollmentsTable() {
  try {
    console.log('正在连接数据库...');

    // 测试数据库连接
    const [connection] = await pool.query('SELECT 1');
    console.log('数据库连接成功!');

    // 创建选课表
    console.log('正在创建 enrollments 表...');

    await pool.query(`
      CREATE TABLE IF NOT EXISTS enrollments (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        course_id BIGINT UNSIGNED NOT NULL,
        student_id BIGINT UNSIGNED NOT NULL,
        enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'completed', 'dropped') DEFAULT 'active',
        grade FLOAT DEFAULT NULL,
        completion_date DATETIME DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_enrollment (course_id, student_id),
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    console.log('enrollments 表创建成功!');

    // 插入示例选课数据
    console.log('正在插入示例选课数据...');

    // 获取学生ID
    const [students] = await pool.query("SELECT id FROM users WHERE role = 'student' LIMIT 1");
    if (students.length === 0) {
      console.error('未找到学生用户，请先创建学生用户');
      process.exit(1);
    }

    const studentId = students[0].id;
    console.log(`使用学生ID: ${studentId}`);

    // 获取课程ID
    const [courses] = await pool.query("SELECT id FROM courses WHERE status = 'published' LIMIT 2");
    if (courses.length === 0) {
      console.error('未找到已发布的课程，请先创建课程');
      process.exit(1);
    }

    // 为学生选修前两门课程
    for (const course of courses) {
      try {
        await pool.query(
          'INSERT INTO enrollments (course_id, student_id, enrollment_date) VALUES (?, ?, NOW())',
          [course.id, studentId]
        );
        console.log(`学生 ${studentId} 成功选修课程 ${course.id}`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`学生 ${studentId} 已经选修了课程 ${course.id}，跳过`);
        } else {
          throw error;
        }
      }
    }

    // 获取所有选课记录
    const [enrollments] = await pool.query(
      `SELECT e.*, c.title as course_title, u.username as student_name
       FROM enrollments e
       JOIN courses c ON e.course_id = c.id
       JOIN users u ON e.student_id = u.id
       ORDER BY e.id`
    );

    console.log('\n当前所有选课记录:');
    console.table(enrollments);

    process.exit(0);
  } catch (error) {
    console.error('创建选课表失败:', error);
    process.exit(1);
  }
}

createEnrollmentsTable();
