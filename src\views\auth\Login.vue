<template>
  <div class="login-container">
    <el-card class="login-card">
      <div slot="header" class="header">
        <h2>E-learning平台登录</h2>
      </div>
      <el-form :model="loginForm" :rules="rules" ref="loginForm" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="loginForm.username" prefix-icon="el-icon-user"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="loginForm.password" type="password" prefix-icon="el-icon-lock"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading" class="submit-btn">登录</el-button>
        </el-form-item>
      </el-form>
      <div class="register-link">
        <p>还没有账号？<router-link to="/register">立即注册</router-link></p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Login',
  // 确保组件能够正确加载
  beforeCreate() {
    console.log('Login组件正在加载...');
  },
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    submitForm() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            await this.$store.dispatch('login', this.loginForm)
            this.$message.success('登录成功')

            // 根据用户角色重定向
            const userRole = this.$store.getters.userRole
            if (userRole === 'admin') {
              this.$router.push('/admin')
            } else if (userRole === 'teacher') {
              this.$router.push('/teacher')
            } else if (userRole === 'student') {
              this.$router.push('/student')
            } else {
              this.$router.push('/')
            }
          } catch (error) {
            this.$message.error(error.response?.data?.message || '登录失败，请检查用户名和密码')
          } finally {
            this.loading = false
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

.header {
  text-align: center;
}

.submit-btn {
  width: 100%;
}

.register-link {
  text-align: center;
  margin-top: 15px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-card {
    width: 90%;
  }
}
</style>
