<template>
  <div class="course-overview">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-user"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ course.student_count || 0 }}</div>
            <div class="stat-label">学生</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-document"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ course.resource_count || 0 }}</div>
            <div class="stat-label">资源</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-edit-outline"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ course.test_count || 0 }}</div>
            <div class="stat-label">测试</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-chat-line-square"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ course.discussion_count || 0 }}</div>
            <div class="stat-label">讨论</div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最近活动 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="overview-card">
          <div slot="header" class="card-header">
            <span>最近活动</span>
          </div>
          <div v-if="activities.length > 0">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :timestamp="formatDate(activity.time)"
                placement="top"
                :type="getTimelineItemType(index)">
                {{ activity.content }}
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else class="empty-data">
            <p>暂无活动记录</p>
          </div>
        </el-card>
      </el-col>
      
      <!-- 即将到来的测试 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="overview-card">
          <div slot="header" class="card-header">
            <span>即将到来的测试</span>
            <el-button type="text" @click="navigateToTests">查看全部</el-button>
          </div>
          <div v-if="upcomingTests.length > 0">
            <el-table :data="upcomingTests" style="width: 100%">
              <el-table-column prop="title" label="测试名称"></el-table-column>
              <el-table-column prop="start_time" label="开始时间">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.start_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="end_time" label="结束时间">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.end_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <el-tag :type="getTestStatusType(scope.row.status)">
                    {{ getTestStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无即将到来的测试</p>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最新学生 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="overview-card">
          <div slot="header" class="card-header">
            <span>最新加入的学生</span>
            <el-button type="text" @click="navigateToStudents">查看全部</el-button>
          </div>
          <div v-if="recentStudents.length > 0">
            <el-table :data="recentStudents" style="width: 100%">
              <el-table-column prop="username" label="用户名"></el-table-column>
              <el-table-column prop="email" label="邮箱"></el-table-column>
              <el-table-column prop="enrollment_date" label="加入时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.enrollment_date) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无学生加入</p>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最新资源 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="overview-card">
          <div slot="header" class="card-header">
            <span>最新上传的资源</span>
            <el-button type="text" @click="navigateToResources">查看全部</el-button>
          </div>
          <div v-if="recentResources.length > 0">
            <el-table :data="recentResources" style="width: 100%">
              <el-table-column prop="title" label="资源名称"></el-table-column>
              <el-table-column prop="type" label="类型">
                <template slot-scope="scope">
                  <el-tag :type="getResourceTypeType(scope.row.type)">
                    {{ getResourceTypeText(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="上传时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column prop="download_count" label="下载次数" width="100" align="center"></el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-data">
            <p>暂无资源上传</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'CourseOverview',
  props: {
    course: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      activities: [],
      upcomingTests: [],
      recentStudents: [],
      recentResources: []
    }
  },
  methods: {
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatDateTime(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    getTimelineItemType(index) {
      const types = ['primary', 'success', 'warning', 'danger']
      return types[index % types.length]
    },
    getTestStatusType(status) {
      const types = {
        'draft': 'info',
        'published': 'success',
        'closed': 'warning'
      }
      return types[status] || 'info'
    },
    getTestStatusText(status) {
      const texts = {
        'draft': '草稿',
        'published': '已发布',
        'closed': '已关闭'
      }
      return texts[status] || status
    },
    getResourceTypeType(type) {
      const types = {
        'document': 'primary',
        'video': 'success',
        'link': 'info',
        'other': 'warning'
      }
      return types[type] || 'info'
    },
    getResourceTypeText(type) {
      const texts = {
        'document': '文档',
        'video': '视频',
        'link': '链接',
        'other': '其他'
      }
      return texts[type] || type
    },
    navigateToTests() {
      this.$emit('navigate', 'tests')
    },
    navigateToStudents() {
      this.$emit('navigate', 'students')
    },
    navigateToResources() {
      this.$emit('navigate', 'resources')
    },
    async fetchData() {
      try {
        // 实际应该从API获取数据
        // const activitiesResponse = await this.$http.get(`/teacher/courses/${this.course.id}/activities`)
        // this.activities = activitiesResponse.data.data
        
        // 使用模拟数据
        this.activities = [
          { content: '学生张三加入了课程', time: new Date(Date.now() - 3600000 * 24) },
          { content: '您上传了新资源"JavaScript基础教程"', time: new Date(Date.now() - 3600000 * 48) },
          { content: '您创建了新测试"HTML基础测试"', time: new Date(Date.now() - 3600000 * 72) },
          { content: '学生李四提交了测试', time: new Date(Date.now() - 3600000 * 96) }
        ]
        
        // 即将到来的测试
        this.upcomingTests = [
          {
            id: 1,
            title: 'JavaScript基础测试',
            start_time: new Date(Date.now() + 3600000 * 24),
            end_time: new Date(Date.now() + 3600000 * 48),
            status: 'published'
          },
          {
            id: 2,
            title: 'CSS布局测试',
            start_time: new Date(Date.now() + 3600000 * 72),
            end_time: new Date(Date.now() + 3600000 * 96),
            status: 'draft'
          }
        ]
        
        // 最新学生
        this.recentStudents = [
          {
            id: 3,
            username: '张三',
            email: '<EMAIL>',
            enrollment_date: new Date(Date.now() - 3600000 * 24)
          },
          {
            id: 4,
            username: '李四',
            email: '<EMAIL>',
            enrollment_date: new Date(Date.now() - 3600000 * 48)
          },
          {
            id: 5,
            username: '王五',
            email: '<EMAIL>',
            enrollment_date: new Date(Date.now() - 3600000 * 72)
          }
        ]
        
        // 最新资源
        this.recentResources = [
          {
            id: 1,
            title: 'JavaScript基础教程',
            type: 'document',
            created_at: new Date(Date.now() - 3600000 * 48),
            download_count: 15
          },
          {
            id: 2,
            title: 'HTML5视频教程',
            type: 'video',
            created_at: new Date(Date.now() - 3600000 * 72),
            download_count: 23
          },
          {
            id: 3,
            title: 'CSS参考手册',
            type: 'link',
            created_at: new Date(Date.now() - 3600000 * 96),
            download_count: 10
          }
        ]
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败，请稍后再试')
      }
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style scoped>
.course-overview {
  padding: 10px 0;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 20px;
  height: 100%;
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
  color: #409EFF;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
}

.overview-card {
  margin-bottom: 20px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-data {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .stat-card {
    margin-bottom: 15px;
  }
}
</style>
