<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
    <el-breadcrumb-item 
      v-for="(item, index) in breadcrumbs" 
      :key="index"
      :to="item.path">
      {{ item.name }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script>
export default {
  name: 'Breadcrumb',
  data() {
    return {
      breadcrumbs: []
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.getBreadcrumbs(route)
      },
      immediate: true
    }
  },
  methods: {
    getBreadcrumbs(route) {
      // 重置面包屑
      this.breadcrumbs = []
      
      // 获取匹配的路由
      const matched = route.matched.filter(item => item.name)
      
      // 处理管理员路由
      if (route.path.includes('/admin')) {
        this.breadcrumbs.push({ name: '管理员', path: '/admin' })
      }
      
      // 处理教师路由
      else if (route.path.includes('/teacher')) {
        this.breadcrumbs.push({ name: '教师', path: '/teacher' })
      }
      
      // 处理学生路由
      else if (route.path.includes('/student')) {
        this.breadcrumbs.push({ name: '学生', path: '/student' })
      }
      
      // 添加匹配的路由到面包屑
      matched.forEach(item => {
        // 跳过首页和布局组件
        if (item.name === 'home' || 
            item.name === 'admin-layout' || 
            item.name === 'teacher-layout' || 
            item.name === 'student-layout') {
          return
        }
        
        // 处理路由名称
        let name = item.name
        
        // 替换路由名称中的前缀
        if (name.startsWith('admin-')) {
          name = name.replace('admin-', '')
        } else if (name.startsWith('teacher-')) {
          name = name.replace('teacher-', '')
        } else if (name.startsWith('student-')) {
          name = name.replace('student-', '')
        }
        
        // 格式化名称
        name = name
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join('')
        
        // 中文化处理
        const nameMap = {
          'Dashboard': '仪表盘',
          'Users': '用户管理',
          'Courses': '课程管理',
          'CourseDetail': '课程详情',
          'Resources': '资源管理',
          'Tests': '测试管理',
          'Students': '学生管理',
          'Attendance': '考勤管理',
          'Discussions': '讨论管理',
          'Announcements': '公告管理',
          'Profile': '个人资料',
          'TakeTest': '参加测试'
        }
        
        name = nameMap[name] || name
        
        this.breadcrumbs.push({
          name: name,
          path: item.path
        })
      })
    }
  }
}
</script>
