const { pool } = require('./config/db');

async function createResourcesAndTestsTables() {
  try {
    console.log('正在连接数据库...');
    
    // 测试数据库连接
    const [connection] = await pool.query('SELECT 1');
    console.log('数据库连接成功!');
    
    // 创建资源表
    console.log('正在创建 resources 表...');
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS resources (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(100) NOT NULL,
        description TEXT,
        file_path VARCHAR(255),
        file_type ENUM('document', 'video', 'image', 'link', 'other') NOT NULL,
        file_size INT UNSIGNED,
        course_id BIGINT UNSIGNED NOT NULL,
        teacher_id BIGINT UNSIGNED NOT NULL,
        is_public BOOLEAN DEFAULT TRUE,
        download_count INT UNSIGNED DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    
    console.log('resources 表创建成功!');
    
    // 创建测试表
    console.log('正在创建 tests 表...');
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS tests (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(100) NOT NULL,
        description TEXT,
        course_id BIGINT UNSIGNED NOT NULL,
        teacher_id BIGINT UNSIGNED NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        duration INT UNSIGNED, -- 单位：分钟
        total_score INT UNSIGNED DEFAULT 100,
        passing_score INT UNSIGNED DEFAULT 60,
        is_published BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    
    console.log('tests 表创建成功!');
    
    // 创建测试题目表
    console.log('正在创建 test_questions 表...');
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS test_questions (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        test_id BIGINT UNSIGNED NOT NULL,
        question_text TEXT NOT NULL,
        question_type ENUM('single_choice', 'multiple_choice', 'true_false', 'short_answer', 'essay') NOT NULL,
        options JSON,
        correct_answer TEXT,
        score INT UNSIGNED DEFAULT 10,
        order_num INT UNSIGNED,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE
      )
    `);
    
    console.log('test_questions 表创建成功!');
    
    // 创建学生测试答案表
    console.log('正在创建 test_submissions 表...');
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS test_submissions (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        test_id BIGINT UNSIGNED NOT NULL,
        student_id BIGINT UNSIGNED NOT NULL,
        start_time DATETIME NOT NULL,
        submit_time DATETIME,
        score INT UNSIGNED,
        is_graded BOOLEAN DEFAULT FALSE,
        status ENUM('in_progress', 'submitted', 'graded', 'expired') DEFAULT 'in_progress',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_test_student (test_id, student_id),
        FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    
    console.log('test_submissions 表创建成功!');
    
    // 创建学生答案详情表
    console.log('正在创建 test_answers 表...');
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS test_answers (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        submission_id BIGINT UNSIGNED NOT NULL,
        question_id BIGINT UNSIGNED NOT NULL,
        answer_text TEXT,
        score INT UNSIGNED,
        is_correct BOOLEAN,
        teacher_comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_submission_question (submission_id, question_id),
        FOREIGN KEY (submission_id) REFERENCES test_submissions(id) ON DELETE CASCADE,
        FOREIGN KEY (question_id) REFERENCES test_questions(id) ON DELETE CASCADE
      )
    `);
    
    console.log('test_answers 表创建成功!');
    
    // 获取所有表
    const [tables] = await pool.query('SHOW TABLES');
    
    console.log('\n当前所有表:');
    console.table(tables);
    
    process.exit(0);
  } catch (error) {
    console.error('创建表失败:', error);
    process.exit(1);
  }
}

createResourcesAndTestsTables();
