-- 使用elearning数据库
USE elearning;

-- 检查是否已存在admin用户
SET @admin_exists = (SELECT COUNT(*) FROM users WHERE username = 'admin');

-- 如果不存在，则添加admin用户
-- 密码123456的bcrypt哈希值
INSERT INTO users (username, email, password, role, created_at)
SELECT 'admin', '<EMAIL>', '$2b$10$3euPcmQFCiblsZeEu5s7p.9MUZWg1IkNjYwQZXHTLq1rGnck0QF9W', 'admin', NOW()
WHERE @admin_exists = 0;

-- 显示结果
SELECT 'Admin用户已添加' AS message WHERE @admin_exists = 0
UNION
SELECT 'Admin用户已存在，未进行操作' AS message WHERE @admin_exists > 0;
