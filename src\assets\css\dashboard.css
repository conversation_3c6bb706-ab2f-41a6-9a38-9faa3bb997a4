/* 仪表盘通用样式 */

/* 主容器 */
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面标题 */
.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.dashboard-title i {
  margin-right: 10px;
  font-size: 28px;
  color: #409EFF;
}

/* 卡片样式 */
.dashboard-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  height: 100%;
}

.dashboard-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.card-header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.card-header-title i {
  margin-right: 8px;
  font-size: 18px;
}

.card-body {
  padding: 20px;
}

/* 欢迎卡片 */
.welcome-card {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #67C23A 0%, #409EFF 100%);
  color: white;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.welcome-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url('../images/pattern.svg') repeat;
  opacity: 0.1;
  z-index: 0;
}

.welcome-header {
  display: flex;
  align-items: center;
  padding: 20px;
  position: relative;
  z-index: 1;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-right: 20px;
  border: 3px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.welcome-text h2 {
  margin-top: 0;
  margin-bottom: 10px;
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
}

/* 统计卡片 */
.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 20px;
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
  line-height: 1.2;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

/* 统计卡片颜色变体 */
.stat-card.primary {
  background-color: #ecf5ff;
}
.stat-card.primary .stat-icon {
  color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
}
.stat-card.primary .stat-value {
  color: #409EFF;
}

.stat-card.success {
  background-color: #f0f9eb;
}
.stat-card.success .stat-icon {
  color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
}
.stat-card.success .stat-value {
  color: #67C23A;
}

.stat-card.warning {
  background-color: #fdf6ec;
}
.stat-card.warning .stat-icon {
  color: #E6A23C;
  background-color: rgba(230, 162, 60, 0.1);
}
.stat-card.warning .stat-value {
  color: #E6A23C;
}

.stat-card.danger {
  background-color: #fef0f0;
}
.stat-card.danger .stat-icon {
  color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
}
.stat-card.danger .stat-value {
  color: #F56C6C;
}

.stat-card.info {
  background-color: #f4f4f5;
}
.stat-card.info .stat-icon {
  color: #909399;
  background-color: rgba(144, 147, 153, 0.1);
}
.stat-card.info .stat-value {
  color: #909399;
}

/* 空数据提示 */
.empty-data {
  text-align: center;
  padding: 30px 0;
  color: #909399;
}

.empty-data i {
  font-size: 48px;
  margin-bottom: 10px;
  color: #DCDFE6;
}

.empty-data p {
  margin: 10px 0;
  font-size: 14px;
}

/* 公告样式 */
.announcement-card {
  margin-bottom: 20px;
}

.announcement-item {
  border-left: 4px solid #409EFF;
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.announcement-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.announcement-item.is-pinned {
  border-left: 4px solid #E6A23C;
  background-color: #fdf6ec;
}

.announcement-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
}

.announcement-content {
  margin-bottom: 10px;
  line-height: 1.5;
  color: #606266;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 12px;
}

/* 表格样式增强 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}

/* 时间线样式增强 */
.el-timeline-item__tail {
  border-left: 2px solid #e4e7ed;
}

.el-timeline-item__node--normal {
  width: 14px;
  height: 14px;
}

.el-timeline-item__content {
  padding-bottom: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 15px;
  }
  
  .welcome-header {
    flex-direction: column;
    text-align: center;
  }
  
  .avatar {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .stat-card {
    margin-bottom: 15px;
  }
  
  .card-header {
    padding: 10px 15px;
  }
  
  .card-body {
    padding: 15px;
  }
}
