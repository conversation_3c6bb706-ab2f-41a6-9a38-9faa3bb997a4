<template>
  <div class="comments">
    <div class="page-header">
      <h1>我的评论</h1>
    </div>

    <!-- 筛选选项 -->
    <div class="filter-bar">
      <el-select v-model="typeFilter" placeholder="评论类型" clearable @change="fetchComments">
        <el-option label="课程评论" value="course"></el-option>
        <el-option label="公告评论" value="announcement"></el-option>
        <el-option label="资源评论" value="resource"></el-option>
      </el-select>
      
      <el-input
        placeholder="搜索评论内容"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchComments"
        @keyup.enter.native="fetchComments"
        style="width: 300px;">
      </el-input>
      
      <el-button type="primary" @click="fetchComments">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="comments.length === 0" class="empty-data">
      <el-empty description="暂无评论数据"></el-empty>
    </div>

    <div v-else class="comments-list">
      <el-card
        v-for="comment in comments"
        :key="comment.id"
        class="comment-card"
        shadow="hover">
        <div class="comment-header">
          <div class="comment-meta">
            <el-tag :type="getTypeTagType(comment.type)" size="mini">
              {{ getTypeLabel(comment.type) }}
            </el-tag>
            <span class="target-info" v-if="comment.target_info">
              {{ comment.target_info.title }}
            </span>
            <span class="comment-time">{{ formatDate(comment.created_at) }}</span>
          </div>
          <div class="comment-actions">
            <el-button
              size="mini"
              type="primary"
              @click="editComment(comment)">
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="deleteComment(comment)">
              删除
            </el-button>
          </div>
        </div>
        
        <div class="comment-content">
          {{ comment.content }}
        </div>
        
        <div v-if="comment.parent_comment" class="parent-comment">
          <div class="parent-label">回复：</div>
          <div class="parent-content">{{ comment.parent_comment.content }}</div>
          <div class="parent-author">- {{ comment.parent_comment.username }}</div>
        </div>
        
        <div class="comment-status">
          <el-tag v-if="comment.is_approved" type="success" size="mini">已审核</el-tag>
          <el-tag v-else type="warning" size="mini">待审核</el-tag>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="comments.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>

    <!-- 编辑评论对话框 -->
    <el-dialog
      title="编辑评论"
      :visible.sync="editDialogVisible"
      width="600px"
      @close="resetEditForm">
      <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="100px">
        <el-form-item label="评论内容" prop="content">
          <el-input
            type="textarea"
            v-model="editForm.content"
            placeholder="请输入评论内容"
            :rows="4">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateComment" :loading="updating">保 存</el-button>
      </span>
    </el-dialog>

    <!-- 快速评论组件 -->
    <div class="quick-comment-section">
      <h2>快速评论</h2>
      <div class="quick-comment-tabs">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="课程评论" name="courses">
            <div v-if="enrolledCourses.length > 0">
              <el-select v-model="selectedCourse" placeholder="选择课程" style="width: 100%; margin-bottom: 15px;">
                <el-option
                  v-for="course in enrolledCourses"
                  :key="course.id"
                  :label="course.title"
                  :value="course.id">
                </el-option>
              </el-select>
              <el-input
                type="textarea"
                v-model="quickCommentContent"
                placeholder="写下你对这门课程的评价..."
                :rows="3"
                style="margin-bottom: 15px;">
              </el-input>
              <el-button type="primary" @click="submitQuickComment('course')" :loading="submitting">
                发表评论
              </el-button>
            </div>
            <div v-else>
              <el-empty description="您还没有选修任何课程"></el-empty>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="公告评论" name="announcements">
            <div v-if="recentAnnouncements.length > 0">
              <el-select v-model="selectedAnnouncement" placeholder="选择公告" style="width: 100%; margin-bottom: 15px;">
                <el-option
                  v-for="announcement in recentAnnouncements"
                  :key="announcement.id"
                  :label="announcement.title"
                  :value="announcement.id">
                </el-option>
              </el-select>
              <el-input
                type="textarea"
                v-model="quickCommentContent"
                placeholder="写下你对这个公告的看法..."
                :rows="3"
                style="margin-bottom: 15px;">
              </el-input>
              <el-button type="primary" @click="submitQuickComment('announcement')" :loading="submitting">
                发表评论
              </el-button>
            </div>
            <div v-else>
              <el-empty description="暂无最新公告"></el-empty>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StudentComments',
  data() {
    return {
      comments: [],
      enrolledCourses: [],
      recentAnnouncements: [],
      loading: false,
      updating: false,
      submitting: false,
      searchQuery: '',
      typeFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      editDialogVisible: false,
      activeTab: 'courses',
      selectedCourse: '',
      selectedAnnouncement: '',
      quickCommentContent: '',
      editForm: {
        id: '',
        content: ''
      },
      editRules: {
        content: [
          { required: true, message: '请输入评论内容', trigger: 'blur' },
          { min: 5, message: '评论内容至少5个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async fetchComments() {
      this.loading = true
      try {
        const response = await this.$http.get('/api/comments', {
          params: {
            search: this.searchQuery,
            type: this.typeFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })
        
        if (response.data.success) {
          this.comments = response.data.data.filter(comment => 
            comment.user_id === this.$store.state.user.id
          )
          this.total = this.comments.length
        } else {
          throw new Error(response.data.message || '获取评论失败')
        }
      } catch (error) {
        console.error('获取评论失败:', error)
        this.$message.error('获取评论列表失败')
        this.comments = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    async fetchEnrolledCourses() {
      try {
        const response = await this.$http.get('/api/courses/enrolled')
        if (response.data.success) {
          this.enrolledCourses = response.data.data
        }
      } catch (error) {
        console.error('获取已选课程失败:', error)
      }
    },
    async fetchRecentAnnouncements() {
      try {
        const response = await this.$http.get('/api/announcements', {
          params: { limit: 10 }
        })
        if (response.data.success) {
          this.recentAnnouncements = response.data.data
        }
      } catch (error) {
        console.error('获取公告失败:', error)
      }
    },
    editComment(comment) {
      this.editForm = {
        id: comment.id,
        content: comment.content
      }
      this.editDialogVisible = true
      this.$nextTick(() => {
        this.$refs.editForm.clearValidate()
      })
    },
    resetEditForm() {
      this.editForm = {
        id: '',
        content: ''
      }
    },
    async updateComment() {
      this.$refs.editForm.validate(async valid => {
        if (valid) {
          this.updating = true
          try {
            const response = await this.$http.put(`/api/comments/${this.editForm.id}`, {
              content: this.editForm.content
            })
            
            if (response.data.success) {
              this.$message.success('评论更新成功')
              this.editDialogVisible = false
              this.fetchComments()
            } else {
              throw new Error(response.data.message || '更新评论失败')
            }
          } catch (error) {
            console.error('更新评论失败:', error)
            this.$message.error(error.message || '更新评论失败')
          } finally {
            this.updating = false
          }
        }
      })
    },
    async deleteComment(comment) {
      try {
        await this.$confirm(`确定要删除这条评论吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await this.$http.delete(`/api/comments/${comment.id}`)
        
        if (response.data.success) {
          this.$message.success('评论删除成功')
          this.fetchComments()
        } else {
          throw new Error(response.data.message || '删除评论失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除评论失败:', error)
          this.$message.error(error.message || '删除评论失败')
        }
      }
    },
    async submitQuickComment(type) {
      if (!this.quickCommentContent.trim()) {
        this.$message.warning('请输入评论内容')
        return
      }
      
      let targetId
      if (type === 'course') {
        if (!this.selectedCourse) {
          this.$message.warning('请选择课程')
          return
        }
        targetId = this.selectedCourse
      } else if (type === 'announcement') {
        if (!this.selectedAnnouncement) {
          this.$message.warning('请选择公告')
          return
        }
        targetId = this.selectedAnnouncement
      }
      
      this.submitting = true
      try {
        const response = await this.$http.post('/api/comments', {
          content: this.quickCommentContent,
          type: type,
          target_id: targetId
        })
        
        if (response.data.success) {
          this.$message.success('评论发表成功')
          this.quickCommentContent = ''
          this.selectedCourse = ''
          this.selectedAnnouncement = ''
          this.fetchComments()
        } else {
          throw new Error(response.data.message || '发表评论失败')
        }
      } catch (error) {
        console.error('发表评论失败:', error)
        this.$message.error(error.message || '发表评论失败')
      } finally {
        this.submitting = false
      }
    },
    handleTabClick(tab) {
      this.quickCommentContent = ''
      this.selectedCourse = ''
      this.selectedAnnouncement = ''
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchComments()
    },
    getTypeLabel(type) {
      const labels = {
        course: '课程评论',
        announcement: '公告评论',
        resource: '资源评论',
        test: '测试评论'
      }
      return labels[type] || type
    },
    getTypeTagType(type) {
      const types = {
        course: 'primary',
        announcement: 'success',
        resource: 'warning',
        test: 'info'
      }
      return types[type] || 'default'
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    }
  },
  created() {
    this.fetchComments()
    this.fetchEnrolledCourses()
    this.fetchRecentAnnouncements()
  }
}
</script>

<style scoped>
.comments {
  padding: 20px;
  color: white;
}

.page-header h1 {
  color: white;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
}

.filter-bar .el-select {
  width: 150px;
}

.loading-container, .empty-data {
  padding: 40px;
  text-align: center;
}

.comments-list {
  margin-bottom: 20px;
}

.comment-card {
  margin-bottom: 15px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.target-info {
  font-weight: bold;
  color: #409EFF;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-content {
  color: #333;
  line-height: 1.6;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.parent-comment {
  background-color: #f0f0f0;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
  border-left: 3px solid #409EFF;
}

.parent-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.parent-content {
  color: #333;
  font-size: 14px;
  margin-bottom: 5px;
}

.parent-author {
  font-size: 12px;
  color: #999;
  text-align: right;
}

.comment-status {
  text-align: right;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.quick-comment-section {
  margin-top: 40px;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.quick-comment-section h2 {
  color: white;
  margin-bottom: 20px;
}

.quick-comment-tabs {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
}
</style>
