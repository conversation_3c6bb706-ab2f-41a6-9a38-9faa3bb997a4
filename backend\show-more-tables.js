const { pool } = require('./config/db');

async function showMoreTables() {
  try {
    // 查询resources表结构
    const [resourceColumns] = await pool.query('DESCRIBE resources');
    console.log('\nresources表结构:');
    resourceColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
    });
    
    // 查询tests表结构
    const [testColumns] = await pool.query('DESCRIBE tests');
    console.log('\ntests表结构:');
    testColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
    });
    
    // 查询test_questions表结构
    const [questionColumns] = await pool.query('DESCRIBE test_questions');
    console.log('\ntest_questions表结构:');
    questionColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
    });
    
    // 查询discussions表结构
    const [discussionColumns] = await pool.query('DESCRIBE discussions');
    console.log('\ndiscussions表结构:');
    discussionColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
    });
    
    // 查询course_students表结构
    const [courseStudentColumns] = await pool.query('DESCRIBE course_students');
    console.log('\ncourse_students表结构:');
    courseStudentColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : ''} ${col.Key === 'PRI' ? 'PRIMARY KEY' : ''}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('查询失败:', error);
    process.exit(1);
  }
}

showMoreTables();
