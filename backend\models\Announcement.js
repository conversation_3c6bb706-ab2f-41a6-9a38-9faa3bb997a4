const mongoose = require('mongoose');

const AnnouncementSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, '请提供公告标题'],
    trim: true,
    maxlength: [100, '公告标题不能超过100个字符']
  },
  content: {
    type: String,
    required: [true, '请提供公告内容']
  },
  // 公告类型：system(系统公告), course(课程公告)
  type: {
    type: String,
    enum: ['system', 'course'],
    default: 'system'
  },
  // 如果是课程公告，关联到课程
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  },
  // 发布者
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定发布者']
  },
  // 是否置顶
  isPinned: {
    type: Boolean,
    default: false
  },
  // 是否重要
  isImportant: {
    type: Boolean,
    default: false
  },
  // 发布日期
  publishDate: {
    type: Date,
    default: Date.now
  },
  // 过期日期（可选）
  expiryDate: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// 检查公告是否已过期
AnnouncementSchema.virtual('isExpired').get(function() {
  if (!this.expiryDate) return false;
  return new Date() > this.expiryDate;
});

module.exports = mongoose.model('Announcement', AnnouncementSchema);
