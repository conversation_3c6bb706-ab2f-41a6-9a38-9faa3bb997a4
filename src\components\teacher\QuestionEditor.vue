<template>
  <div class="question-editor">
    <el-form :model="questionForm" :rules="questionRules" ref="questionForm" label-width="100px">
      <el-form-item label="问题类型" prop="type">
        <el-select v-model="questionForm.type" placeholder="请选择问题类型" style="width: 100%" @change="handleTypeChange">
          <el-option label="单选题" value="single"></el-option>
          <el-option label="多选题" value="multiple"></el-option>
          <el-option label="判断题" value="truefalse"></el-option>
          <el-option label="简答题" value="essay"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="问题内容" prop="content">
        <el-input 
          type="textarea" 
          v-model="questionForm.content" 
          :rows="3"
          placeholder="请输入问题内容">
        </el-input>
      </el-form-item>
      
      <!-- 单选题和多选题选项 -->
      <template v-if="['single', 'multiple'].includes(questionForm.type)">
        <el-form-item label="选项" prop="options">
          <div v-for="(option, index) in questionForm.options" :key="index" class="option-item">
            <el-input v-model="option.label" placeholder="选项内容" style="width: calc(100% - 100px)">
              <template slot="prepend">{{ option.value }}</template>
            </el-input>
            <el-button 
              type="danger" 
              icon="el-icon-delete" 
              circle 
              size="mini" 
              @click="removeOption(index)"
              :disabled="questionForm.options.length <= 2">
            </el-button>
          </div>
          <div class="option-actions">
            <el-button type="text" icon="el-icon-plus" @click="addOption">添加选项</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="正确答案" prop="answer">
          <el-select 
            v-if="questionForm.type === 'single'" 
            v-model="questionForm.answer" 
            placeholder="请选择正确答案" 
            style="width: 100%">
            <el-option 
              v-for="option in questionForm.options" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value">
            </el-option>
          </el-select>
          <el-checkbox-group 
            v-else 
            v-model="questionForm.answer" 
            style="width: 100%">
            <el-checkbox 
              v-for="option in questionForm.options" 
              :key="option.value" 
              :label="option.value">
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </template>
      
      <!-- 判断题答案 -->
      <el-form-item label="正确答案" prop="answer" v-if="questionForm.type === 'truefalse'">
        <el-radio-group v-model="questionForm.answer">
          <el-radio label="true">正确</el-radio>
          <el-radio label="false">错误</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <!-- 简答题参考答案 -->
      <el-form-item label="参考答案" v-if="questionForm.type === 'essay'">
        <el-input 
          type="textarea" 
          v-model="questionForm.answer" 
          :rows="3"
          placeholder="请输入参考答案（可选）">
        </el-input>
        <div class="form-tip">参考答案仅供教师参考，不会显示给学生</div>
      </el-form-item>
      
      <el-form-item label="分值" prop="score">
        <el-input-number v-model="questionForm.score" :min="0.5" :step="0.5" :precision="1"></el-input-number>
      </el-form-item>
      
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number v-model="questionForm.sortOrder" :min="0" :step="1"></el-input-number>
        <div class="form-tip">数字越小排序越靠前</div>
      </el-form-item>
    </el-form>
    
    <div class="form-actions">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="save" :loading="saving">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuestionEditor',
  props: {
    question: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      questionForm: {
        id: this.question.id,
        testId: this.question.testId,
        type: this.question.type || 'single',
        content: this.question.content || '',
        options: this.question.options || [
          { label: '选项A', value: 'A' },
          { label: '选项B', value: 'B' },
          { label: '选项C', value: 'C' },
          { label: '选项D', value: 'D' }
        ],
        answer: this.question.answer || (this.question.type === 'multiple' ? [] : 'A'),
        score: this.question.score || 2,
        sortOrder: this.question.sortOrder || 0
      },
      questionRules: {
        type: [
          { required: true, message: '请选择问题类型', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入问题内容', trigger: 'blur' }
        ],
        answer: [
          { 
            required: true, 
            message: '请选择正确答案', 
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.questionForm.type === 'essay') {
                callback();
              } else if (this.questionForm.type === 'multiple' && Array.isArray(value) && value.length === 0) {
                callback(new Error('请至少选择一个正确答案'));
              } else if (!value && this.questionForm.type !== 'essay') {
                callback(new Error('请选择正确答案'));
              } else {
                callback();
              }
            }
          }
        ],
        score: [
          { required: true, message: '请输入分值', trigger: 'blur' }
        ]
      },
      saving: false
    }
  },
  methods: {
    handleTypeChange(type) {
      // 重置答案
      if (type === 'single') {
        this.questionForm.answer = this.questionForm.options[0]?.value || 'A'
      } else if (type === 'multiple') {
        this.questionForm.answer = []
      } else if (type === 'truefalse') {
        this.questionForm.answer = 'true'
      } else if (type === 'essay') {
        this.questionForm.answer = ''
      }
    },
    addOption() {
      // 生成新选项的值
      const lastOption = this.questionForm.options[this.questionForm.options.length - 1]
      const lastValue = lastOption ? lastOption.value : 'A'
      const newValue = String.fromCharCode(lastValue.charCodeAt(0) + 1)
      
      this.questionForm.options.push({
        label: `选项${newValue}`,
        value: newValue
      })
    },
    removeOption(index) {
      // 至少保留两个选项
      if (this.questionForm.options.length <= 2) {
        return
      }
      
      const removedOption = this.questionForm.options[index]
      this.questionForm.options.splice(index, 1)
      
      // 如果删除的是正确答案，重置答案
      if (this.questionForm.type === 'single' && this.questionForm.answer === removedOption.value) {
        this.questionForm.answer = this.questionForm.options[0]?.value || 'A'
      } else if (this.questionForm.type === 'multiple' && Array.isArray(this.questionForm.answer)) {
        this.questionForm.answer = this.questionForm.answer.filter(value => value !== removedOption.value)
      }
    },
    cancel() {
      this.$emit('cancel')
    },
    save() {
      this.$refs.questionForm.validate(valid => {
        if (valid) {
          this.saving = true
          
          try {
            // 准备问题数据
            const questionData = {
              id: this.questionForm.id,
              testId: this.questionForm.testId,
              type: this.questionForm.type,
              content: this.questionForm.content,
              score: this.questionForm.score,
              sortOrder: this.questionForm.sortOrder
            }
            
            // 根据问题类型准备答案数据
            if (this.questionForm.type === 'single') {
              questionData.answer = {
                correct: this.questionForm.answer,
                options: this.questionForm.options
              }
            } else if (this.questionForm.type === 'multiple') {
              questionData.answer = {
                correct: this.questionForm.answer,
                options: this.questionForm.options
              }
            } else if (this.questionForm.type === 'truefalse') {
              questionData.answer = {
                correct: this.questionForm.answer
              }
            } else if (this.questionForm.type === 'essay') {
              questionData.answer = {
                reference: this.questionForm.answer
              }
            }
            
            // 发送保存事件
            this.$emit('save', questionData)
          } catch (error) {
            console.error('准备问题数据失败:', error)
            this.$message.error('保存问题失败，请稍后再试')
          } finally {
            this.saving = false
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.question-editor {
  padding: 10px 0;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.option-actions {
  margin-top: 10px;
}

.form-tip {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}
</style>
