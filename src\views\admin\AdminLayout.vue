<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="aside">
        <div class="logo-container">
          <img src="@/assets/logo.svg" alt="Logo" class="logo" v-if="!isCollapse">
          <img src="@/assets/logo-small.svg" alt="Logo" class="logo-small" v-else>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          :collapse="isCollapse"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF">
          <el-menu-item index="/admin" @click="$router.push('/admin')">
            <i class="el-icon-s-home"></i>
            <span slot="title">仪表盘</span>
          </el-menu-item>
          <el-menu-item index="/admin/announcements" @click="$router.push('/admin/announcements')">
            <i class="el-icon-bell"></i>
            <span slot="title">公告管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/comments" @click="$router.push('/admin/comments')">
            <i class="el-icon-chat-line-square"></i>
            <span slot="title">评论管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/discussions" @click="$router.push('/admin/discussions')">
            <i class="el-icon-s-comment"></i>
            <span slot="title">讨论管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主要内容区域 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-left">
            <i
              :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
              @click="toggleSidebar"
              class="collapse-btn">
            </i>
            <breadcrumb />
          </div>
          <div class="header-right">
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="user-dropdown">
                <el-avatar :size="32" :src="userAvatar"></el-avatar>
                <span class="username">{{ userName }}</span>
                <i class="el-icon-arrow-down"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 内容区域 -->
        <el-main class="main">
          <router-view />
        </el-main>

        <!-- 页脚 -->
        <el-footer class="footer">
          <p>© {{ new Date().getFullYear() }} E-learning平台 | 管理员系统</p>
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import Breadcrumb from '@/components/common/Breadcrumb.vue'

export default {
  name: 'AdminLayout',
  components: {
    Breadcrumb
  },
  data() {
    return {
      isCollapse: false
    }
  },
  computed: {
    activeMenu() {
      return this.$route.path
    },
    userName() {
      return this.$store.getters.userName || '管理员'
    },
    userAvatar() {
      const user = this.$store.state.user
      return user && user.avatar ? user.avatar : require('@/assets/default-avatar.svg')
    }
  },
  methods: {
    toggleSidebar() {
      this.isCollapse = !this.isCollapse
    },
    handleCommand(command) {
      if (command === 'logout') {
        this.$confirm('确定要退出登录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 显示加载中提示
          const loading = this.$loading({
            lock: true,
            text: '正在退出...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 延迟执行退出操作，确保UI状态更新
          setTimeout(() => {
            this.$store.dispatch('logout')
            loading.close();
          }, 500);
        }).catch(() => {})
      } else if (command === 'profile') {
        this.$router.push('/profile')
      }
    }
  }
}
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap');

.admin-layout {
  height: 100vh;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #d63031; /* 更改基础字体颜色为鲜红色 */
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
}

.logo-container {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #263445;
}

.logo {
  height: 40px;
}

.logo-small {
  height: 30px;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 200px;
}

/* 自定义菜单字体 */
.el-menu-item, .el-submenu__title {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  letter-spacing: 0.3px;
  color: #f0f2f5; /* 菜单项文字颜色为浅灰色 */
}

.el-menu-item.is-active {
  color: #fd79a8 !important; /* 激活的菜单项颜色为粉色 */
}

/* 自定义标题字体 */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: #e84393; /* 标题颜色为亮粉色 */
}

/* 页面标题样式 */
.page-header h1 {
  color: #e84393; /* 页面标题颜色为亮粉色 */
  margin-bottom: 20px;
}

.header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  color: #e84393; /* 用户名颜色为亮粉色 */
}

.main {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
}

.footer {
  text-align: center;
  background-color: #fff;
  color: #e84393; /* 页脚颜色为亮粉色 */
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .aside {
    position: fixed;
    z-index: 1000;
    height: 100%;
  }

  .header {
    padding: 0 10px;
  }

  .username {
    display: none;
  }

  .main {
    padding: 10px;
  }
}
</style>
