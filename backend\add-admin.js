const bcrypt = require('bcryptjs');
const { pool } = require('./config/db');

// 管理员账号信息
const adminUser = {
  username: 'admin',
  email: '<EMAIL>',
  password: '123456',
  role: 'admin'
};

// 添加管理员账号
async function addAdmin() {
  try {
    console.log('开始添加管理员账号...');

    // 检查用户名是否已存在
    const [usernameRows] = await pool.query('SELECT id FROM users WHERE username = ?', [adminUser.username]);
    if (usernameRows.length > 0) {
      console.log('管理员账号已存在，无需添加');
      return;
    }

    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(adminUser.password, salt);

    // 插入用户数据
    const [result] = await pool.query(
      'INSERT INTO users (username, email, password, role, created_at) VALUES (?, ?, ?, ?, NOW())',
      [adminUser.username, adminUser.email, hashedPassword, adminUser.role]
    );

    console.log(`管理员账号添加成功，ID: ${result.insertId}`);
  } catch (error) {
    console.error('添加管理员账号失败:', error);
  } finally {
    // 关闭数据库连接
    pool.end();
  }
}

// 执行添加管理员账号
addAdmin();
