const mysql = require('mysql2/promise');

// 直接创建连接
const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'elearning',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

async function checkAnnouncements() {
  try {
    console.log('测试数据库连接...');
    await pool.query('SELECT 1');
    console.log('✅ 数据库连接成功');

    console.log('检查announcements表...');

    // 检查表是否存在
    const [tables] = await pool.query('SHOW TABLES LIKE "announcements"');

    if (tables.length === 0) {
      console.log('❌ announcements表不存在，正在创建...');

      // 创建announcements表
      await pool.query(`
        CREATE TABLE announcements (
          id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          content TEXT NOT NULL,
          type ENUM('system', 'course') DEFAULT 'system',
          course_id BIGINT UNSIGNED NULL,
          author_id BIGINT UNSIGNED NOT NULL,
          is_pinned BOOLEAN DEFAULT FALSE,
          is_important BOOLEAN DEFAULT FALSE,
          publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
          INDEX idx_type (type),
          INDEX idx_publish_date (publish_date),
          INDEX idx_pinned (is_pinned),
          INDEX idx_important (is_important)
        )
      `);

      console.log('✅ announcements表创建成功');

      // 插入一些示例数据
      console.log('插入示例公告数据...');

      // 获取管理员用户ID
      const [adminUsers] = await pool.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');
      const adminId = adminUsers.length > 0 ? adminUsers[0].id : 1;

      // 获取课程ID
      const [courses] = await pool.query('SELECT id FROM courses LIMIT 2');

      const sampleAnnouncements = [
        {
          title: '系统维护通知',
          content: '系统将于本周六晚上10点进行维护，预计维护时间2小时，期间可能无法正常访问，请提前安排学习计划。',
          type: 'system',
          course_id: null,
          is_pinned: true,
          is_important: true
        },
        {
          title: '欢迎使用在线学习平台',
          content: '欢迎大家使用我们的在线学习平台！平台提供丰富的课程资源和互动功能，希望大家能够充分利用平台资源，提升学习效果。',
          type: 'system',
          course_id: null,
          is_pinned: false,
          is_important: false
        },
        {
          title: '期末考试安排',
          content: '各位同学请注意，期末考试将于下月进行，请及时关注考试安排和复习资料。',
          type: 'system',
          course_id: null,
          is_pinned: true,
          is_important: true
        }
      ];

      // 如果有课程，添加课程公告
      if (courses.length > 0) {
        sampleAnnouncements.push({
          title: '课程作业提交提醒',
          content: '请各位同学按时提交本周的课程作业，截止时间为本周日晚上11:59。',
          type: 'course',
          course_id: courses[0].id,
          is_pinned: false,
          is_important: true
        });

        if (courses.length > 1) {
          sampleAnnouncements.push({
            title: '课程资料更新',
            content: '本课程的学习资料已更新，请同学们及时下载查看。',
            type: 'course',
            course_id: courses[1].id,
            is_pinned: false,
            is_important: false
          });
        }
      }

      // 插入示例数据
      for (const announcement of sampleAnnouncements) {
        await pool.query(
          `INSERT INTO announcements (title, content, type, course_id, author_id, is_pinned, is_important, publish_date)
           VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
          [
            announcement.title,
            announcement.content,
            announcement.type,
            announcement.course_id,
            adminId,
            announcement.is_pinned,
            announcement.is_important
          ]
        );
      }

      console.log(`✅ 插入了 ${sampleAnnouncements.length} 条示例公告`);
    } else {
      console.log('✅ announcements表已存在');
    }

    // 显示表结构
    const [columns] = await pool.query('DESCRIBE announcements');
    console.log('\n📋 announcements表结构:');
    console.table(columns);

    // 显示现有数据
    const [announcements] = await pool.query('SELECT * FROM announcements ORDER BY publish_date DESC');
    console.log(`\n📢 现有公告数量: ${announcements.length}`);

    if (announcements.length > 0) {
      console.log('\n公告列表:');
      console.table(announcements.map(a => ({
        id: a.id,
        title: a.title,
        type: a.type,
        author_id: a.author_id,
        is_pinned: a.is_pinned,
        is_important: a.is_important,
        publish_date: a.publish_date
      })));
    }

    console.log('\n✅ 检查完成！');
    process.exit(0);

  } catch (error) {
    console.error('❌ 检查失败:', error);
    process.exit(1);
  }
}

checkAnnouncements();
