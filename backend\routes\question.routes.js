const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect } = require('../middleware/auth');

// 授权中间件 - 检查用户是否为教师或管理员
const authorizeTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'teacher' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: '权限不足，需要教师权限'
    });
  }
};

// 添加测试问题
router.post('/tests/:id/questions', protect, authorizeTeacher, async (req, res) => {
  try {
    const testId = req.params.id;
    const { type, content, answer, score, sortOrder } = req.body;
    
    // 检查测试是否存在
    const [testRows] = await pool.query(
      'SELECT * FROM tests WHERE id = ?',
      [testId]
    );
    
    if (testRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }
    
    // 检查权限
    if (req.user.role === 'teacher' && testRows[0].creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权添加问题'
      });
    }
    
    // 插入问题
    const [result] = await pool.query(
      `INSERT INTO test_questions (test_id, type, content, answer, score, sort_order)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [testId, type, content, JSON.stringify(answer), score, sortOrder || 0]
    );
    
    // 获取新创建的问题
    const [newQuestion] = await pool.query(
      'SELECT * FROM test_questions WHERE id = ?',
      [result.insertId]
    );
    
    // 更新测试总分
    await updateTestTotalScore(testId);
    
    res.status(201).json({
      success: true,
      message: '问题添加成功',
      data: newQuestion[0]
    });
  } catch (error) {
    console.error('添加问题失败:', error);
    res.status(500).json({
      success: false,
      message: '添加问题失败',
      error: error.message
    });
  }
});

// 更新测试问题
router.put('/questions/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const questionId = req.params.id;
    const { type, content, answer, score, sortOrder } = req.body;
    
    // 获取问题信息
    const [questionRows] = await pool.query(
      'SELECT q.*, t.creator_id FROM test_questions q JOIN tests t ON q.test_id = t.id WHERE q.id = ?',
      [questionId]
    );
    
    if (questionRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '问题不存在'
      });
    }
    
    const question = questionRows[0];
    
    // 检查权限
    if (req.user.role === 'teacher' && question.creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权修改问题'
      });
    }
    
    // 更新问题
    await pool.query(
      `UPDATE test_questions 
       SET type = ?, content = ?, answer = ?, score = ?, sort_order = ?, updated_at = NOW()
       WHERE id = ?`,
      [type, content, JSON.stringify(answer), score, sortOrder, questionId]
    );
    
    // 获取更新后的问题
    const [updatedQuestion] = await pool.query(
      'SELECT * FROM test_questions WHERE id = ?',
      [questionId]
    );
    
    // 更新测试总分
    await updateTestTotalScore(question.test_id);
    
    res.status(200).json({
      success: true,
      message: '问题更新成功',
      data: updatedQuestion[0]
    });
  } catch (error) {
    console.error('更新问题失败:', error);
    res.status(500).json({
      success: false,
      message: '更新问题失败',
      error: error.message
    });
  }
});

// 删除测试问题
router.delete('/questions/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const questionId = req.params.id;
    
    // 获取问题信息
    const [questionRows] = await pool.query(
      'SELECT q.*, t.creator_id FROM test_questions q JOIN tests t ON q.test_id = t.id WHERE q.id = ?',
      [questionId]
    );
    
    if (questionRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '问题不存在'
      });
    }
    
    const question = questionRows[0];
    
    // 检查权限
    if (req.user.role === 'teacher' && question.creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权删除问题'
      });
    }
    
    // 删除问题
    await pool.query('DELETE FROM test_questions WHERE id = ?', [questionId]);
    
    // 更新测试总分
    await updateTestTotalScore(question.test_id);
    
    res.status(200).json({
      success: true,
      message: '问题删除成功'
    });
  } catch (error) {
    console.error('删除问题失败:', error);
    res.status(500).json({
      success: false,
      message: '删除问题失败',
      error: error.message
    });
  }
});

// 批量添加测试问题
router.post('/tests/:id/questions/batch', protect, authorizeTeacher, async (req, res) => {
  try {
    const testId = req.params.id;
    const { questions } = req.body;
    
    if (!Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的问题数组'
      });
    }
    
    // 检查测试是否存在
    const [testRows] = await pool.query(
      'SELECT * FROM tests WHERE id = ?',
      [testId]
    );
    
    if (testRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }
    
    // 检查权限
    if (req.user.role === 'teacher' && testRows[0].creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权添加问题'
      });
    }
    
    // 开始事务
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    
    try {
      const addedQuestions = [];
      
      // 批量插入问题
      for (const question of questions) {
        const { type, content, answer, score, sortOrder } = question;
        
        const [result] = await connection.query(
          `INSERT INTO test_questions (test_id, type, content, answer, score, sort_order)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [testId, type, content, JSON.stringify(answer), score, sortOrder || 0]
        );
        
        const [newQuestion] = await connection.query(
          'SELECT * FROM test_questions WHERE id = ?',
          [result.insertId]
        );
        
        addedQuestions.push(newQuestion[0]);
      }
      
      // 更新测试总分
      const [questionsResult] = await connection.query(
        'SELECT SUM(score) as total_score FROM test_questions WHERE test_id = ?',
        [testId]
      );
      
      const totalScore = questionsResult[0].total_score || 0;
      
      await connection.query(
        'UPDATE tests SET total_score = ?, updated_at = NOW() WHERE id = ?',
        [totalScore, testId]
      );
      
      // 提交事务
      await connection.commit();
      
      res.status(201).json({
        success: true,
        message: '问题批量添加成功',
        count: addedQuestions.length,
        data: addedQuestions
      });
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('批量添加问题失败:', error);
    res.status(500).json({
      success: false,
      message: '批量添加问题失败',
      error: error.message
    });
  }
});

// 更新测试总分的辅助函数
async function updateTestTotalScore(testId) {
  try {
    const [questionsResult] = await pool.query(
      'SELECT SUM(score) as total_score FROM test_questions WHERE test_id = ?',
      [testId]
    );
    
    const totalScore = questionsResult[0].total_score || 0;
    
    await pool.query(
      'UPDATE tests SET total_score = ?, updated_at = NOW() WHERE id = ?',
      [totalScore, testId]
    );
  } catch (error) {
    console.error('更新测试总分失败:', error);
    throw error;
  }
}

module.exports = router;
