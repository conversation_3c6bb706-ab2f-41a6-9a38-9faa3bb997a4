const { pool } = require('./config/db');

async function publishCourses() {
  try {
    console.log('正在连接数据库...');
    
    // 测试数据库连接
    const [connection] = await pool.query('SELECT 1');
    console.log('数据库连接成功!');
    
    // 获取所有草稿状态的课程
    const [draftCourses] = await pool.query(
      "SELECT id, title, status FROM courses WHERE status = 'draft'"
    );
    
    console.log('草稿状态的课程:');
    console.table(draftCourses);
    
    // 更新课程状态为已发布
    for (const course of draftCourses) {
      await pool.query(
        "UPDATE courses SET status = 'published' WHERE id = ?",
        [course.id]
      );
      console.log(`课程 "${course.title}" (ID: ${course.id}) 已发布`);
    }
    
    // 获取所有课程
    const [allCourses] = await pool.query(
      "SELECT id, title, status FROM courses ORDER BY id"
    );
    
    console.log('\n更新后的所有课程:');
    console.table(allCourses);
    
    process.exit(0);
  } catch (error) {
    console.error('发布课程失败:', error);
    process.exit(1);
  }
}

publishCourses();
