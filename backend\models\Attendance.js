const mongoose = require('mongoose');

// 学生考勤记录模式
const StudentAttendanceSchema = new mongoose.Schema({
  // 学生
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定学生']
  },
  // 状态：present(出席), late(迟到), absent(缺席), leave(请假)
  status: {
    type: String,
    enum: ['present', 'late', 'absent', 'leave'],
    default: 'absent'
  },
  // 签到时间
  checkInTime: {
    type: Date
  },
  // 备注
  note: {
    type: String
  }
});

// 考勤记录模式
const AttendanceSchema = new mongoose.Schema({
  // 所属课程
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, '请指定所属课程']
  },
  // 考勤标题
  title: {
    type: String,
    required: [true, '请提供考勤标题'],
    trim: true
  },
  // 考勤日期
  date: {
    type: Date,
    required: [true, '请指定考勤日期'],
    default: Date.now
  },
  // 开始时间
  startTime: {
    type: Date,
    required: [true, '请指定开始时间']
  },
  // 结束时间
  endTime: {
    type: Date,
    required: [true, '请指定结束时间']
  },
  // 考勤码（用于学生签到）
  attendanceCode: {
    type: String
  },
  // 学生考勤记录
  records: [StudentAttendanceSchema],
  // 创建者
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定创建者']
  },
  // 状态：active(进行中), closed(已关闭)
  status: {
    type: String,
    enum: ['active', 'closed'],
    default: 'active'
  },
  // 位置要求（可选）
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number],
      default: [0, 0]
    },
    radius: {
      type: Number,
      default: 100 // 默认100米范围内
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 生成随机考勤码
AttendanceSchema.pre('save', function(next) {
  if (!this.attendanceCode) {
    // 生成6位随机数字
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    this.attendanceCode = code;
  }
  next();
});

// 虚拟字段：出席率
AttendanceSchema.virtual('attendanceRate').get(function() {
  if (!this.records || this.records.length === 0) return 0;
  
  const presentCount = this.records.filter(
    record => record.status === 'present' || record.status === 'late'
  ).length;
  
  return (presentCount / this.records.length) * 100;
});

// 索引位置字段以支持地理空间查询
AttendanceSchema.index({ location: '2dsphere' });

module.exports = mongoose.model('Attendance', AttendanceSchema);
