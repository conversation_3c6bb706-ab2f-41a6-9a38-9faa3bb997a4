const mongoose = require('mongoose');

// 答案模式
const AnswerSchema = new mongoose.Schema({
  // 关联的问题ID
  question: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, '请指定问题ID']
  },
  // 学生的答案
  answer: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, '请提供答案']
  },
  // 得分
  score: {
    type: Number,
    default: 0
  },
  // 评语（对于简答题）
  comment: {
    type: String
  },
  // 是否已评分
  isGraded: {
    type: Boolean,
    default: false
  }
});

// 测试提交模式
const TestSubmissionSchema = new mongoose.Schema({
  // 关联的测试
  test: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Test',
    required: [true, '请指定测试ID']
  },
  // 提交学生
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '请指定学生ID']
  },
  // 答案列表
  answers: [AnswerSchema],
  // 开始时间
  startTime: {
    type: Date,
    default: Date.now
  },
  // 提交时间
  submitTime: {
    type: Date
  },
  // 总得分
  totalScore: {
    type: Number,
    default: 0
  },
  // 是否已完成评分
  isFullyGraded: {
    type: Boolean,
    default: false
  },
  // 状态：in_progress(进行中), submitted(已提交), graded(已评分)
  status: {
    type: String,
    enum: ['in_progress', 'submitted', 'graded'],
    default: 'in_progress'
  },
  // 评语
  feedback: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// 计算总分
TestSubmissionSchema.pre('save', function(next) {
  if (this.answers && this.answers.length > 0) {
    this.totalScore = this.answers.reduce((sum, answer) => sum + answer.score, 0);
    
    // 检查是否所有答案都已评分
    const allGraded = this.answers.every(answer => answer.isGraded);
    this.isFullyGraded = allGraded;
    
    // 如果全部评分完成，更新状态
    if (allGraded) {
      this.status = 'graded';
    }
  }
  next();
});

module.exports = mongoose.model('TestSubmission', TestSubmissionSchema);
