const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录错误信息
  console.error(err);

  // Mongoose错误处理
  // 无效ID错误
  if (err.name === 'CastError') {
    const message = '找不到资源';
    error = { message, statusCode: 404 };
  }

  // 重复键错误
  if (err.code === 11000) {
    const message = '该字段值已存在，请使用其他值';
    error = { message, statusCode: 400 };
  }

  // 验证错误
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = { message, statusCode: 400 };
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    const message = '无效的令牌，请重新登录';
    error = { message, statusCode: 401 };
  }

  // JWT过期错误
  if (err.name === 'TokenExpiredError') {
    const message = '令牌已过期，请重新登录';
    error = { message, statusCode: 401 };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || '服务器错误'
  });
};

module.exports = errorHandler;
