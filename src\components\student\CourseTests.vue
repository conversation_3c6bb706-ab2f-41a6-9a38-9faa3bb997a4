<template>
  <div class="course-tests">
    <div class="filter-bar">
      <el-input
        placeholder="搜索测试"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchTests">
      </el-input>
      <el-select v-model="statusFilter" placeholder="测试状态" clearable @change="fetchTests">
        <el-option label="未开始" value="not_started"></el-option>
        <el-option label="进行中" value="in_progress"></el-option>
        <el-option label="已完成" value="completed"></el-option>
        <el-option label="已过期" value="expired"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchTests">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="tests.length === 0" class="empty-data">
      <el-empty description="暂无测试"></el-empty>
    </div>

    <div v-else>
      <!-- 测试列表 -->
      <el-table :data="tests" style="width: 100%">
        <el-table-column prop="title" label="测试名称" min-width="200"></el-table-column>
        <el-table-column prop="description" label="描述" min-width="300">
          <template slot-scope="scope">
            <span v-if="scope.row.description">{{ scope.row.description }}</span>
            <span v-else class="no-data">无描述</span>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="150">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.start_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="end_time" label="结束时间" width="150">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.end_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长" width="100">
          <template slot-scope="scope">
            {{ scope.row.duration }} 分钟
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getTestStatusType(scope.row.status)">
              {{ getTestStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="成绩" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.status === 'completed'">
              {{ scope.row.score }} / {{ scope.row.total_points }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="startTest(scope.row)"
              v-if="scope.row.status === 'not_started' || scope.row.status === 'in_progress'"
              :disabled="!isTestAvailable(scope.row)">
              {{ scope.row.status === 'in_progress' ? '继续' : '开始' }}
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="viewTestResult(scope.row)"
              v-if="scope.row.status === 'completed'">
              查看结果
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange">
        </el-pagination>
      </div>
    </div>

    <!-- 测试结果对话框 -->
    <el-dialog :title="'测试结果: ' + (currentTest.title || '')" :visible.sync="resultVisible" width="70%">
      <div v-if="resultLoading" class="result-loading">
        <el-skeleton :rows="10" animated />
      </div>
      <div v-else class="test-result">
        <div class="result-summary">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="result-item">
                <div class="result-label">得分</div>
                <div class="result-value">{{ currentTest.score }} / {{ currentTest.total_points }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="result-item">
                <div class="result-label">正确率</div>
                <div class="result-value">{{ Math.round(currentTest.score / currentTest.total_points * 100) }}%</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="result-item">
                <div class="result-label">用时</div>
                <div class="result-value">{{ formatDuration(currentTest.time_spent) }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="result-details">
          <div v-for="(question, index) in testQuestions" :key="index" class="question-item">
            <div class="question-header">
              <div class="question-number">问题 {{ index + 1 }}</div>
              <div class="question-points">{{ question.points }} 分</div>
              <div class="question-result" :class="question.is_correct ? 'correct' : 'incorrect'">
                {{ question.is_correct ? '正确' : '错误' }}
              </div>
            </div>
            <div class="question-content">{{ question.content }}</div>
            <div class="question-options" v-if="question.type === 'single_choice' || question.type === 'multiple_choice'">
              <div
                v-for="(option, optIndex) in question.options"
                :key="optIndex"
                class="option-item"
                :class="{
                  'user-selected': question.user_answer.includes(option.id),
                  'correct-answer': question.correct_answer.includes(option.id),
                  'wrong-answer': question.user_answer.includes(option.id) && !question.correct_answer.includes(option.id)
                }">
                {{ optIndex + 1 }}. {{ option.content }}
              </div>
            </div>
            <div class="question-answer" v-else>
              <div class="answer-label">您的答案:</div>
              <div class="user-answer">{{ question.user_answer }}</div>
              <div class="answer-label">正确答案:</div>
              <div class="correct-answer">{{ question.correct_answer }}</div>
            </div>
            <div class="question-feedback" v-if="question.feedback">
              <div class="feedback-label">反馈:</div>
              <div class="feedback-content">{{ question.feedback }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CourseTests',
  props: {
    courseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      tests: [],
      loading: false,
      searchQuery: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      resultVisible: false,
      resultLoading: false,
      currentTest: {},
      testQuestions: []
    }
  },
  methods: {
    async fetchTests() {
      this.loading = true
      try {
        // 从API获取数据
        const response = await this.$http.get(`/api/tests/courses/${this.courseId}/tests`, {
          params: {
            search: this.searchQuery,
            status: this.statusFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.tests = response.data.data
          this.total = response.data.total

          // 处理测试状态
          this.tests.forEach(test => {
            // 如果后端没有提供status字段，根据时间计算状态
            if (!test.status) {
              const now = new Date()
              const startTime = new Date(test.start_time)
              const endTime = new Date(test.end_time)

              if (now < startTime) {
                test.status = 'not_started'
              } else if (now > endTime) {
                test.status = 'expired'
              } else {
                test.status = 'in_progress'
              }
            }

            // 设置时长（如果后端返回的是time_limit而不是duration）
            if (test.time_limit && !test.duration) {
              test.duration = test.time_limit
            }

            // 设置总分（如果后端没有提供）
            if (!test.total_points) {
              test.total_points = 100
            }
          })
        } else {
          throw new Error(response.data.message || '获取测试失败')
        }
      } catch (error) {
        console.error('获取测试失败:', error)
        this.$message.error('获取测试失败，请稍后再试')
        this.tests = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchTests()
    },
    formatDateTime(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}分${remainingSeconds}秒`
    },
    getTestStatusType(status) {
      const types = {
        'not_started': 'info',
        'in_progress': 'warning',
        'completed': 'success',
        'expired': 'danger'
      }
      return types[status] || 'info'
    },
    getTestStatusText(status) {
      const texts = {
        'not_started': '未开始',
        'in_progress': '进行中',
        'completed': '已完成',
        'expired': '已过期'
      }
      return texts[status] || status
    },
    isTestAvailable(test) {
      const now = new Date()
      return new Date(test.start_time) <= now && new Date(test.end_time) >= now
    },
    startTest(test) {
      // 实际应该跳转到测试页面
      this.$router.push(`/student/courses/${this.courseId}/test/${test.id}`)
    },
    async viewTestResult(test) {
      this.currentTest = test
      this.resultVisible = true
      this.resultLoading = true

      try {
        // 从API获取测试结果
        const response = await this.$http.get(`/api/tests/${test.id}/result`)

        if (response.data.success) {
          this.testQuestions = response.data.questions || []

          // 如果没有问题数据，显示一个提示
          if (this.testQuestions.length === 0) {
            this.$message.info('暂无详细的测试结果数据')
          }
        } else {
          throw new Error(response.data.message || '获取测试结果失败')
        }
      } catch (error) {
        console.error('获取测试结果失败:', error)
        this.$message.error('获取测试结果失败，请稍后再试')
        this.testQuestions = []
      } finally {
        this.resultLoading = false
      }
    }
  },
  created() {
    this.fetchTests()
  }
}
</script>

<style scoped>
.course-tests {
  padding: 10px 0;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container, .result-loading {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.no-data {
  color: #909399;
  font-style: italic;
}

.result-summary {
  margin-bottom: 30px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
}

.result-item {
  text-align: center;
}

.result-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.result-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.question-item {
  margin-bottom: 30px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-number {
  font-weight: bold;
}

.question-points {
  color: #909399;
}

.question-result {
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 12px;
}

.question-result.correct {
  background-color: #f0f9eb;
  color: #67c23a;
}

.question-result.incorrect {
  background-color: #fef0f0;
  color: #f56c6c;
}

.question-content {
  font-size: 16px;
  margin-bottom: 15px;
}

.question-options {
  margin-bottom: 15px;
}

.option-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.option-item.user-selected {
  background-color: #ecf5ff;
  border: 1px solid #d9ecff;
}

.option-item.correct-answer {
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
}

.option-item.wrong-answer {
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
}

.question-answer {
  margin-bottom: 15px;
}

.answer-label, .feedback-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.user-answer, .correct-answer, .feedback-content {
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #f5f7fa;
  margin-bottom: 10px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
  }

  .filter-bar .el-input {
    width: 100%;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .question-number, .question-points, .question-result {
    margin-bottom: 5px;
  }
}
</style>
