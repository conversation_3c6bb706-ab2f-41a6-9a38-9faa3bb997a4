const { pool } = require('./config/db');

async function insertCourses() {
  try {
    console.log('正在连接数据库...');
    
    // 测试数据库连接
    const [connection] = await pool.query('SELECT 1');
    console.log('数据库连接成功!');
    
    // 获取教师ID
    const [teachers] = await pool.query("SELECT id FROM users WHERE role = 'teacher' LIMIT 1");
    if (teachers.length === 0) {
      console.error('未找到教师用户，请先创建教师用户');
      process.exit(1);
    }
    
    const teacherId = teachers[0].id;
    console.log(`使用教师ID: ${teacherId}`);
    
    // 示例课程数据
    const courses = [
      {
        title: 'Web开发基础',
        description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
        category: '计算机科学',
        cover_image: '',
        teacher_id: teacherId,
        start_date: '2023-09-01',
        end_date: '2024-01-15',
        status: 'published'
      },
      {
        title: '数据库系统',
        description: '关系型数据库设计、SQL查询和数据库管理系统。',
        category: '计算机科学',
        cover_image: '',
        teacher_id: teacherId,
        start_date: '2023-09-01',
        end_date: '2024-01-15',
        status: 'published'
      },
      {
        title: '高级Web开发',
        description: '学习前端框架和后端技术，构建完整的Web应用。',
        category: '软件工程',
        cover_image: '',
        teacher_id: teacherId,
        start_date: '2024-02-01',
        end_date: '2024-06-15',
        status: 'draft'
      },
      {
        title: '人工智能导论',
        description: '人工智能基础理论、机器学习算法和应用案例分析。',
        category: '人工智能',
        cover_image: '',
        teacher_id: teacherId,
        start_date: '2023-09-01',
        end_date: '2024-01-15',
        status: 'published'
      },
      {
        title: '网络安全基础',
        description: '网络安全原理、常见攻击手段和防御策略。',
        category: '网络安全',
        cover_image: '',
        teacher_id: teacherId,
        start_date: '2023-09-01',
        end_date: '2024-01-15',
        status: 'published'
      }
    ];
    
    // 插入课程数据
    for (const course of courses) {
      // 检查课程是否已存在
      const [existingCourses] = await pool.query(
        'SELECT id FROM courses WHERE title = ? AND teacher_id = ?',
        [course.title, course.teacher_id]
      );
      
      if (existingCourses.length > 0) {
        console.log(`课程 "${course.title}" 已存在，跳过`);
        continue;
      }
      
      // 插入课程
      const [result] = await pool.query(
        `INSERT INTO courses (
          title, description, category, cover_image, 
          teacher_id, start_date, end_date, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          course.title,
          course.description,
          course.category,
          course.cover_image,
          course.teacher_id,
          course.start_date,
          course.end_date,
          course.status
        ]
      );
      
      console.log(`课程 "${course.title}" 插入成功，ID: ${result.insertId}`);
    }
    
    console.log('所有课程数据插入完成');
    
    // 获取所有课程
    const [allCourses] = await pool.query(
      `SELECT c.*, u.username as teacher_name 
       FROM courses c 
       JOIN users u ON c.teacher_id = u.id 
       ORDER BY c.id`
    );
    
    console.log('\n当前所有课程:');
    console.table(allCourses);
    
    process.exit(0);
  } catch (error) {
    console.error('插入课程数据失败:', error);
    process.exit(1);
  }
}

insertCourses();
