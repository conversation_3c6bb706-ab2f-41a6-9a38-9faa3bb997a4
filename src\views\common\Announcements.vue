<template>
  <div class="announcements-container">
    <h1>公告</h1>

    <div class="filter-bar">
      <el-input
        placeholder="搜索公告"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchAnnouncements">
      </el-input>
      <el-select v-model="typeFilter" placeholder="公告类型" clearable @change="fetchAnnouncements">
        <el-option label="系统公告" value="system"></el-option>
        <el-option label="课程公告" value="course"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchAnnouncements">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="announcements.length === 0" class="empty-data">
      <el-empty description="暂无公告"></el-empty>
    </div>

    <div v-else class="announcements-list">
      <!-- 置顶公告 -->
      <div v-if="pinnedAnnouncements.length > 0" class="pinned-announcements">
        <h2>置顶公告</h2>
        <el-card v-for="announcement in pinnedAnnouncements" :key="announcement.id" class="announcement-card pinned">
          <div class="announcement-header">
            <h3>
              <el-tag type="danger" v-if="announcement.is_important">重要</el-tag>
              <el-tag type="warning" v-if="announcement.is_pinned">置顶</el-tag>
              {{ announcement.title }}
            </h3>
            <div class="announcement-meta">
              <span class="type">{{ getAnnouncementType(announcement.type) }}</span>
              <span class="author">{{ announcement.author_name }}</span>
              <span class="date">{{ formatDate(announcement.publish_date) }}</span>
            </div>
          </div>
          <div class="announcement-content">
            <p>{{ announcement.content }}</p>
          </div>
          <div v-if="announcement.course" class="announcement-course">
            <el-tag size="small">{{ announcement.course.title }}</el-tag>
          </div>
        </el-card>
      </div>

      <!-- 普通公告 -->
      <div class="regular-announcements">
        <h2 v-if="pinnedAnnouncements.length > 0">其他公告</h2>
        <el-card v-for="announcement in regularAnnouncements" :key="announcement.id" class="announcement-card">
          <div class="announcement-header">
            <h3>
              <el-tag type="danger" v-if="announcement.is_important">重要</el-tag>
              {{ announcement.title }}
            </h3>
            <div class="announcement-meta">
              <span class="type">{{ getAnnouncementType(announcement.type) }}</span>
              <span class="author">{{ announcement.author_name }}</span>
              <span class="date">{{ formatDate(announcement.publish_date) }}</span>
            </div>
          </div>
          <div class="announcement-content">
            <p>{{ announcement.content }}</p>
          </div>
          <div v-if="announcement.course" class="announcement-course">
            <el-tag size="small">{{ announcement.course.title }}</el-tag>
          </div>
        </el-card>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Announcements',
  data() {
    return {
      announcements: [],
      loading: false,
      searchQuery: '',
      typeFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  computed: {
    pinnedAnnouncements() {
      return this.announcements.filter(a => a.is_pinned)
    },
    regularAnnouncements() {
      return this.announcements.filter(a => !a.is_pinned)
    }
  },
  methods: {
    async fetchAnnouncements() {
      this.loading = true
      try {
        // 从API获取数据
        const response = await this.$http.get('/api/announcements', {
          params: {
            search: this.searchQuery,
            type: this.typeFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.announcements = response.data.data || []
          this.total = response.data.total || this.announcements.length
        } else {
          throw new Error('获取公告失败')
        }

        this.loading = false
      } catch (error) {
        console.error('获取公告失败:', error)
        this.$message.error('获取公告失败，请稍后再试')
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchAnnouncements()
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    getAnnouncementType(type) {
      return type === 'system' ? '系统公告' : '课程公告'
    }
  },
  created() {
    this.fetchAnnouncements()
  }
}
</script>

<style scoped>
.announcements-container {
  padding: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.announcement-card {
  margin-bottom: 20px;
}

.announcement-card.pinned {
  border: 1px solid #e6a23c;
}

.announcement-header {
  margin-bottom: 15px;
}

.announcement-header h3 {
  margin-top: 0;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.announcement-meta {
  display: flex;
  gap: 15px;
  color: #909399;
  font-size: 14px;
}

.announcement-content {
  margin-bottom: 15px;
}

.announcement-course {
  text-align: right;
}

.pinned-announcements, .regular-announcements {
  margin-bottom: 30px;
}

.pagination-container {
  text-align: center;
  margin-top: 30px;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
  }

  .filter-bar .el-input {
    width: 100%;
  }

  .announcement-meta {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
