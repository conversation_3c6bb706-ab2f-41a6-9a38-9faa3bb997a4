<template>
  <div class="course-management">
    <div class="page-header">
      <h1>课程管理</h1>
      <el-button type="primary" @click="showCreateCourseDialog">创建新课程</el-button>
    </div>

    <div class="filter-bar">
      <el-input
        placeholder="搜索课程"
        v-model="searchQuery"
        prefix-icon="el-icon-search"
        clearable
        @clear="fetchCourses">
      </el-input>
      <el-select v-model="statusFilter" placeholder="课程状态" clearable @change="fetchCourses">
        <el-option label="草稿" value="draft"></el-option>
        <el-option label="已发布" value="published"></el-option>
        <el-option label="已归档" value="archived"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchCourses">搜索</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="courses.length === 0" class="empty-data">
      <el-empty description="暂无课程数据"></el-empty>
    </div>

    <el-table v-else :data="courses" style="width: 100%" border>
      <el-table-column prop="title" label="课程名称" min-width="200">
        <template slot-scope="scope">
          <el-link type="primary" @click="navigateToCourse(scope.row.id)">{{ scope.row.title }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="category" label="类别" width="120"></el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getCourseStatusType(scope.row.status)">
            {{ getCourseStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="student_count" label="学生数" width="100" align="center"></el-table-column>
      <el-table-column prop="resource_count" label="资源数" width="100" align="center"></el-table-column>
      <el-table-column prop="test_count" label="测试数" width="100" align="center"></el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="navigateToCourse(scope.row.id)">管理</el-button>
          <el-button type="text" size="small" @click="editCourse(scope.row)">编辑</el-button>
          <el-button
            type="text"
            size="small"
            @click="changeCourseStatus(scope.row)"
            v-if="scope.row.status === 'draft'">
            发布
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="changeCourseStatus(scope.row)"
            v-else-if="scope.row.status === 'published'">
            归档
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="changeCourseStatus(scope.row)"
            v-else>
            重新发布
          </el-button>
          <el-button type="text" size="small" class="danger-button" @click="deleteCourse(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container" v-if="courses.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>

    <!-- 创建/编辑课程对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
      <el-form :model="courseForm" :rules="courseRules" ref="courseForm" label-width="100px">
        <el-form-item label="课程名称" prop="title">
          <el-input v-model="courseForm.title"></el-input>
        </el-form-item>
        <el-form-item label="课程描述" prop="description">
          <el-input type="textarea" v-model="courseForm.description" rows="4"></el-input>
        </el-form-item>
        <el-form-item label="课程类别" prop="category">
          <el-select v-model="courseForm.category" placeholder="请选择课程类别" style="width: 100%">
            <el-option label="计算机科学" value="计算机科学"></el-option>
            <el-option label="软件工程" value="软件工程"></el-option>
            <el-option label="数据科学" value="数据科学"></el-option>
            <el-option label="人工智能" value="人工智能"></el-option>
            <el-option label="网络安全" value="网络安全"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker v-model="courseForm.startDate" type="date" placeholder="选择开始日期" style="width: 100%"></el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker v-model="courseForm.endDate" type="date" placeholder="选择结束日期" style="width: 100%"></el-date-picker>
        </el-form-item>
        <el-form-item label="课程标签">
          <el-tag
            :key="tag"
            v-for="tag in courseForm.tags"
            closable
            :disable-transitions="false"
            @close="handleTagClose(tag)">
            {{tag}}
          </el-tag>
          <el-input
            class="input-new-tag"
            v-if="inputTagVisible"
            v-model="inputTagValue"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleTagConfirm"
            @blur="handleTagConfirm">
          </el-input>
          <el-button v-else class="button-new-tag" size="small" @click="showTagInput">+ 添加标签</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCourse" :loading="saving">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CourseManagement',
  data() {
    return {
      courses: [],
      loading: false,
      searchQuery: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '创建新课程',
      courseForm: {
        id: null,
        title: '',
        description: '',
        category: '',
        startDate: '',
        endDate: '',
        tags: []
      },
      courseRules: {
        title: [
          { required: true, message: '请输入课程名称', trigger: 'blur' },
          { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入课程描述', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择课程类别', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ]
      },
      inputTagVisible: false,
      inputTagValue: '',
      saving: false
    }
  },
  methods: {
    async fetchCourses() {
      this.loading = true
      try {
        // 从API获取数据
        const response = await this.$http.get('/api/courses', {
          params: {
            search: this.searchQuery,
            status: this.statusFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })

        if (response.data.success) {
          this.courses = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取课程失败')
        }
      } catch (error) {
        console.error('获取课程失败:', error)
        this.$message.error('获取课程失败，请稍后再试')

        // 使用模拟数据（仅在开发环境或API失败时）
        this.courses = [
          {
            id: 1,
            title: 'Web开发基础',
            description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
            category: '计算机科学',
            status: 'published',
            student_count: 25,
            resource_count: 12,
            test_count: 3,
            start_date: '2023-09-01',
            end_date: '2024-01-15'
          },
          {
            id: 2,
            title: '数据库系统',
            description: '关系型数据库设计、SQL查询和数据库管理系统。',
            category: '计算机科学',
            status: 'published',
            student_count: 18,
            resource_count: 8,
            test_count: 2,
            start_date: '2023-09-01',
            end_date: '2024-01-15'
          },
          {
            id: 3,
            title: '高级Web开发',
            description: '学习前端框架和后端技术，构建完整的Web应用。',
            category: '软件工程',
            status: 'draft',
            student_count: 0,
            resource_count: 3,
            test_count: 0,
            start_date: '2024-02-01',
            end_date: '2024-06-15'
          }
        ]
        this.total = 3
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchCourses()
    },
    navigateToCourse(courseId) {
      this.$router.push(`/teacher/courses/${courseId}`)
    },
    showCreateCourseDialog() {
      this.dialogTitle = '创建新课程'
      this.courseForm = {
        id: null,
        title: '',
        description: '',
        category: '',
        startDate: '',
        endDate: '',
        tags: []
      }
      this.dialogVisible = true
    },
    editCourse(course) {
      this.dialogTitle = '编辑课程'
      this.courseForm = {
        id: course.id,
        title: course.title,
        description: course.description,
        category: course.category,
        startDate: course.start_date,
        endDate: course.end_date,
        tags: course.tags || []
      }
      this.dialogVisible = true
    },
    async saveCourse() {
      this.$refs.courseForm.validate(async valid => {
        if (valid) {
          // 检查开始日期和结束日期
          if (new Date(this.courseForm.endDate) <= new Date(this.courseForm.startDate)) {
            this.$message.error('结束日期必须晚于开始日期')
            return
          }

          this.saving = true

          try {
            // 准备课程数据
            const courseData = {
              title: this.courseForm.title,
              description: this.courseForm.description,
              category: this.courseForm.category,
              start_date: this.$moment(this.courseForm.startDate).format('YYYY-MM-DD'),
              end_date: this.$moment(this.courseForm.endDate).format('YYYY-MM-DD'),
              cover_image: '', // 暂时为空
              status: 'draft' // 默认为草稿状态
            }

            let response

            if (this.courseForm.id) {
              // 更新现有课程
              response = await this.$http.put(`/api/courses/${this.courseForm.id}`, courseData)
            } else {
              // 创建新课程
              response = await this.$http.post('/api/courses', courseData)
            }

            if (response.data.success) {
              this.$message.success(this.courseForm.id ? '课程更新成功' : '课程创建成功')
              this.dialogVisible = false
              this.fetchCourses()
            } else {
              throw new Error(response.data.message || '保存课程失败')
            }
          } catch (error) {
            console.error('保存课程失败:', error)
            this.$message.error(error.message || '保存课程失败，请稍后再试')
          } finally {
            this.saving = false
          }
        }
      })
    },
    async changeCourseStatus(course) {
      try {
        let newStatus = ''
        let actionText = ''

        if (course.status === 'draft') {
          newStatus = 'published'
          actionText = '发布'
        } else if (course.status === 'published') {
          newStatus = 'archived'
          actionText = '归档'
        } else {
          newStatus = 'published'
          actionText = '重新发布'
        }

        // 确认操作
        await this.$confirm(`确定要${actionText}课程 "${course.title}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })

        // 调用API更新课程状态
        const response = await this.$http.put(`/api/courses/${course.id}`, {
          title: course.title,
          description: course.description,
          category: course.category,
          cover_image: course.cover_image || '',
          start_date: this.$moment(course.start_date).format('YYYY-MM-DD'),
          end_date: this.$moment(course.end_date).format('YYYY-MM-DD'),
          status: newStatus
        })

        if (response.data.success) {
          this.$message.success(`课程${actionText}成功`)

          // 更新本地数据
          const index = this.courses.findIndex(c => c.id === course.id)
          if (index !== -1) {
            this.courses[index] = response.data.data
          }
        } else {
          throw new Error(response.data.message || `${actionText}课程失败`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('更改课程状态失败:', error)
          this.$message.error(error.message || '更改课程状态失败，请稍后再试')
        }
      }
    },
    async deleteCourse(course) {
      try {
        await this.$confirm(`确定要删除课程 "${course.title}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用API删除课程
        const response = await this.$http.delete(`/api/courses/${course.id}`)

        if (response.data.success) {
          this.$message.success('课程删除成功')

          // 更新本地数据
          this.courses = this.courses.filter(c => c.id !== course.id)
          this.total--
        } else {
          throw new Error(response.data.message || '删除课程失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除课程失败:', error)
          this.$message.error(error.message || '删除课程失败，请稍后再试')
        }
      }
    },
    getCourseStatusType(status) {
      const types = {
        'draft': 'info',
        'published': 'success',
        'archived': 'warning'
      }
      return types[status] || 'info'
    },
    getCourseStatusText(status) {
      const texts = {
        'draft': '草稿',
        'published': '已发布',
        'archived': '已归档'
      }
      return texts[status] || status
    },
    handleTagClose(tag) {
      this.courseForm.tags.splice(this.courseForm.tags.indexOf(tag), 1)
    },
    showTagInput() {
      this.inputTagVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleTagConfirm() {
      if (this.inputTagValue) {
        if (!this.courseForm.tags.includes(this.inputTagValue)) {
          this.courseForm.tags.push(this.inputTagValue)
        }
      }
      this.inputTagVisible = false
      this.inputTagValue = ''
    }
  },
  created() {
    this.fetchCourses()
  }
}
</script>

<style scoped>
.course-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.danger-button {
  color: #F56C6C;
}

.danger-button:hover {
  color: #F78989;
}

.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
  }

  .filter-bar .el-input {
    width: 100%;
  }
}
</style>
