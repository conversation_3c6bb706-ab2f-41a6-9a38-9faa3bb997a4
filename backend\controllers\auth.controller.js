const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');
const { pool } = require('../config/db');

// @desc    注册用户
// @route   POST /api/auth/register
// @access  Public
exports.register = async (req, res, next) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { username, email, password, role } = req.body;

    // 检查用户名是否已存在
    const [usernameRows] = await pool.query('SELECT id FROM users WHERE username = ?', [username]);
    if (usernameRows.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该用户名已被使用'
      });
    }

    // 检查邮箱是否已存在
    const [emailRows] = await pool.query('SELECT id FROM users WHERE email = ?', [email]);
    if (emailRows.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该邮箱已被注册'
      });
    }

    // 临时允许管理员注册（仅用于开发测试）
    // 在生产环境中应该取消注释下面的代码
    /*
    if (role === 'admin') {
      return res.status(403).json({
        success: false,
        message: '不允许注册管理员账号'
      });
    }
    */

    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // 插入用户数据
    const [result] = await pool.query(
      'INSERT INTO users (username, email, password, role, created_at) VALUES (?, ?, ?, ?, NOW())',
      [username, email, hashedPassword, role || 'student']
    );

    // 生成JWT令牌
    const token = jwt.sign(
      { id: result.insertId, role: role || 'student' },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRE || '30d' }
    );

    // 获取新创建的用户信息
    const [userRows] = await pool.query(
      'SELECT id, username, email, role, avatar FROM users WHERE id = ?',
      [result.insertId]
    );

    // 返回用户信息和令牌
    res.status(201).json({
      success: true,
      token,
      user: userRows[0]
    });
  } catch (error) {
    console.error('注册失败:', error);
    res.status(500).json({
      success: false,
      message: '注册失败，请稍后再试',
      error: error.message
    });
  }
};

// @desc    用户登录
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res, next) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { username, password } = req.body;

    // 查询用户
    const [rows] = await pool.query(
      'SELECT id, username, email, password, role, avatar FROM users WHERE username = ?',
      [username]
    );

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: '无效的用户名或密码'
      });
    }

    const user = rows[0];

    // 验证密码
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: '无效的用户名或密码'
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, role: user.role },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRE || '30d' }
    );

    // 移除密码字段
    delete user.password;

    // 返回用户信息和令牌
    res.status(200).json({
      success: true,
      token,
      user
    });
  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后再试',
      error: error.message
    });
  }
};

// @desc    获取当前用户信息
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res, next) => {
  try {
    // 从请求头中获取令牌
    const token = req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未授权访问，请登录'
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret_key');

    // 获取用户信息
    const [rows] = await pool.query(
      'SELECT id, username, email, role, avatar FROM users WHERE id = ?',
      [decoded.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到用户'
      });
    }

    res.status(200).json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(401).json({
      success: false,
      message: '未授权访问，请登录',
      error: error.message
    });
  }
};

// @desc    退出登录
// @route   POST /api/auth/logout
// @access  Private
exports.logout = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      message: '退出登录成功'
    });
  } catch (error) {
    console.error('退出登录失败:', error);
    res.status(500).json({
      success: false,
      message: '退出登录失败',
      error: error.message
    });
  }
};
