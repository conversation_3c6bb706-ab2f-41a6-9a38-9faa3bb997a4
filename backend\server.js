const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const { testConnection } = require('./config/db');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files directory for uploads
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 静态文件目录 - 用于测试页面
app.use(express.static(path.join(__dirname, 'public')));

// 测试路由 - 用于验证数据库连接
app.use('/api/test', require('./routes/test.routes'));

// API Routes
app.use('/api/auth', require('./routes/auth.routes'));
// 资源管理路由
app.use('/api/resources', require('./routes/resource.routes'));
// 测试管理路由
app.use('/api/tests', require('./routes/tests.routes'));
app.use('/api/questions', require('./routes/question.routes'));
app.use('/api/submissions', require('./routes/submission.routes'));
// 考试管理路由
app.use('/api/exams', require('./routes/exam.routes'));
// 课程管理路由
app.use('/api/courses', require('./routes/course.routes'));
// 公告管理路由
app.use('/api/announcements', require('./routes/announcement.routes'));
// 以下路由暂未实现
// app.use('/api/users', require('./routes/user.routes'));
// app.use('/api/discussions', require('./routes/discussion.routes'));
// app.use('/api/attendance', require('./routes/attendance.routes'));

// 初始化上传目录
const { initUploadDirs } = require('./utils/uploadDirs');
initUploadDirs();

// 测试数据库连接
testConnection()
  .then(connected => {
    if (connected) {
      console.log('MySQL数据库连接成功！');
    } else {
      console.error('MySQL数据库连接失败！');
    }
  })
  .catch(err => {
    console.error('测试数据库连接时出错:', err);
  });

// Default route
app.get('/', (req, res) => {
  res.send('E-learning API is running - MySQL版本');
});

// Port configuration
const PORT = process.env.PORT || 5000;

// Start server
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`测试页面: http://localhost:${PORT}/test.html`);
  console.log(`测试API: http://localhost:${PORT}/api/test/db-connection`);
  console.log(`获取用户: http://localhost:${PORT}/api/test/users`);
  console.log(`获取课程: http://localhost:${PORT}/api/test/courses`);
});
