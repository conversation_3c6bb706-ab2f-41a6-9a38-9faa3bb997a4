import Vue from 'vue'
import Vuex from 'vuex'
import axios from 'axios'
import router from '../router'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    token: localStorage.getItem('token') || '',
    user: JSON.parse(localStorage.getItem('user')) || null,
    courses: [],
    currentCourse: null,
    announcements: [],
    resources: [],
    tests: [],
    discussions: []
  },
  getters: {
    isAuthenticated: state => !!state.token,
    isAdmin: state => state.user && state.user.role === 'admin',
    isTeacher: state => state.user && state.user.role === 'teacher',
    isStudent: state => state.user && state.user.role === 'student',
    userRole: state => state.user ? state.user.role : null,
    userId: state => state.user ? state.user._id : null,
    userName: state => state.user ? state.user.username : null
  },
  mutations: {
    // 认证相关
    AUTH_SUCCESS(state, { token, user }) {
      state.token = token
      state.user = user
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(user))
    },
    LOGOUT(state) {
      state.token = ''
      state.user = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    },
    // 课程相关
    SET_COURSES(state, courses) {
      state.courses = courses
    },
    SET_CURRENT_COURSE(state, course) {
      state.currentCourse = course
    },
    ADD_COURSE(state, course) {
      state.courses.push(course)
    },
    UPDATE_COURSE(state, updatedCourse) {
      const index = state.courses.findIndex(c => c._id === updatedCourse._id)
      if (index !== -1) {
        state.courses.splice(index, 1, updatedCourse)
      }
    },
    DELETE_COURSE(state, courseId) {
      state.courses = state.courses.filter(c => c._id !== courseId)
    },
    // 公告相关
    SET_ANNOUNCEMENTS(state, announcements) {
      state.announcements = announcements
    },
    // 资源相关
    SET_RESOURCES(state, resources) {
      state.resources = resources
    },
    // 测试相关
    SET_TESTS(state, tests) {
      state.tests = tests
    },
    // 讨论相关
    SET_DISCUSSIONS(state, discussions) {
      state.discussions = discussions
    }
  },
  actions: {
    // 登录
    async login({ commit }, credentials) {
      try {
        console.log('登录请求:', credentials)
        const response = await axios.post('/api/auth/login', credentials)
        console.log('登录响应:', response.data)
        const { token, user } = response.data
        commit('AUTH_SUCCESS', { token, user })
        return response
      } catch (error) {
        console.error('登录错误:', error.response ? error.response.data : error.message)
        throw error
      }
    },
    // 注册
    async register({ commit }, userData) {
      try {
        const response = await axios.post('/api/auth/register', userData)
        return response
      } catch (error) {
        console.error('注册错误:', error.response ? error.response.data : error.message)
        throw error
      }
    },
    // 登出
    logout({ commit }) {
      // 先清除状态
      commit('LOGOUT')
      // 使用 nextTick 确保状态更新后再导航
      Vue.nextTick(() => {
        // 使用 replace 而不是 push，防止用户可以返回到需要认证的页面
        router.replace({ path: '/login' })
      })
    },
    // 获取课程列表
    async fetchCourses({ commit }) {
      try {
        const response = await axios.get('/api/courses')
        commit('SET_COURSES', response.data.data)
        return response.data
      } catch (error) {
        console.error('获取课程列表失败:', error)
        throw error
      }
    },
    // 获取单个课程详情
    async fetchCourse({ commit }, courseId) {
      try {
        const response = await axios.get(`/api/courses/${courseId}`)
        commit('SET_CURRENT_COURSE', response.data.data)
        return response.data
      } catch (error) {
        console.error('获取课程详情失败:', error)
        throw error
      }
    },
    // 获取公告列表
    async fetchAnnouncements({ commit }, params) {
      try {
        const response = await axios.get('/announcements', { params })
        commit('SET_ANNOUNCEMENTS', response.data)
        return response.data
      } catch (error) {
        throw error
      }
    },
    // 获取资源列表
    async fetchResources({ commit }, courseId) {
      try {
        const response = await axios.get(`/resources?courseId=${courseId}`)
        commit('SET_RESOURCES', response.data)
        return response.data
      } catch (error) {
        throw error
      }
    },
    // 获取测试列表
    async fetchTests({ commit }, courseId) {
      try {
        const response = await axios.get(`/tests?courseId=${courseId}`)
        commit('SET_TESTS', response.data)
        return response.data
      } catch (error) {
        throw error
      }
    },
    // 获取讨论列表
    async fetchDiscussions({ commit }, courseId) {
      try {
        const response = await axios.get(`/discussions?courseId=${courseId}`)
        commit('SET_DISCUSSIONS', response.data)
        return response.data
      } catch (error) {
        throw error
      }
    }
  },
  modules: {
  }
})
