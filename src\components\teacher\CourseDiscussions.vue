<template>
  <div class="course-discussions">
    <div class="discussions-header">
      <div class="filter-bar">
        <el-input
          placeholder="搜索讨论"
          v-model="searchQuery"
          prefix-icon="el-icon-search"
          clearable
          @clear="fetchDiscussions">
        </el-input>
        <el-select v-model="statusFilter" placeholder="状态" clearable @change="fetchDiscussions">
          <el-option label="全部" value=""></el-option>
          <el-option label="已解决" value="answered"></el-option>
          <el-option label="未解决" value="unanswered"></el-option>
        </el-select>
        <el-button type="primary" @click="fetchDiscussions">搜索</el-button>
      </div>
      <el-button type="success" @click="showNewDiscussionDialog">发布公告</el-button>
    </div>
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>
    
    <div v-else-if="discussions.length === 0" class="empty-data">
      <el-empty description="暂无讨论">
        <el-button type="primary" @click="showNewDiscussionDialog">发布第一个公告</el-button>
      </el-empty>
    </div>
    
    <div v-else class="discussions-list">
      <el-card v-for="discussion in discussions" :key="discussion.id" class="discussion-card" shadow="hover">
        <div class="discussion-header">
          <div class="discussion-author">
            <el-avatar :size="40" :src="discussion.author_avatar"></el-avatar>
            <div class="author-info">
              <div class="author-name">{{ discussion.author_name }}</div>
              <div class="post-time">{{ formatDate(discussion.created_at) }}</div>
            </div>
          </div>
          <div class="discussion-stats">
            <el-tag v-if="discussion.is_pinned" type="warning">置顶</el-tag>
            <el-tag v-if="discussion.is_announcement" type="danger">公告</el-tag>
            <span class="stat-item">
              <i class="el-icon-view"></i> {{ discussion.view_count }}
            </span>
            <span class="stat-item">
              <i class="el-icon-chat-line-square"></i> {{ discussion.reply_count }}
            </span>
          </div>
        </div>
        <div class="discussion-content">
          <h3 class="discussion-title" @click="viewDiscussion(discussion)">
            {{ discussion.title }}
          </h3>
          <div class="discussion-preview">{{ discussion.content }}</div>
        </div>
        <div class="discussion-footer">
          <div class="discussion-tags">
            <el-tag size="mini" v-if="discussion.is_answered" type="success">已解决</el-tag>
          </div>
          <div class="discussion-actions">
            <el-button type="text" @click="viewDiscussion(discussion)">查看详情</el-button>
            <el-button type="text" v-if="isCurrentUserAuthor(discussion)" @click="editDiscussion(discussion)">编辑</el-button>
            <el-button type="text" v-if="isCurrentUserAuthor(discussion)" @click="togglePin(discussion)">
              {{ discussion.is_pinned ? '取消置顶' : '置顶' }}
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container" v-if="discussions.length > 0">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange">
      </el-pagination>
    </div>
    
    <!-- 讨论详情对话框 -->
    <el-dialog :title="currentDiscussion.title" :visible.sync="discussionVisible" width="70%">
      <div v-if="discussionLoading" class="discussion-loading">
        <el-skeleton :rows="10" animated />
      </div>
      <div v-else class="discussion-detail">
        <!-- 原始帖子 -->
        <div class="post-item original-post">
          <div class="post-header">
            <div class="post-author">
              <el-avatar :size="40" :src="currentDiscussion.author_avatar"></el-avatar>
              <div class="author-info">
                <div class="author-name">{{ currentDiscussion.author_name }}</div>
                <div class="post-time">{{ formatDate(currentDiscussion.created_at) }}</div>
              </div>
            </div>
            <div class="post-actions">
              <el-button 
                v-if="isCurrentUserAuthor(currentDiscussion)" 
                type="text" 
                size="mini"
                @click="editDiscussion(currentDiscussion)">
                编辑
              </el-button>
              <el-button 
                v-if="isCurrentUserAuthor(currentDiscussion)" 
                type="text" 
                size="mini"
                @click="togglePin(currentDiscussion)">
                {{ currentDiscussion.is_pinned ? '取消置顶' : '置顶' }}
              </el-button>
            </div>
          </div>
          <div class="post-content" v-html="currentDiscussion.content"></div>
        </div>
        
        <!-- 回复列表 -->
        <div class="replies-section">
          <h3>回复 ({{ replies.length }})</h3>
          <div v-if="replies.length === 0" class="empty-replies">
            <p>暂无回复</p>
          </div>
          
          <div v-else class="replies-list">
            <div v-for="reply in replies" :key="reply.id" class="post-item reply-post">
              <div class="post-header">
                <div class="post-author">
                  <el-avatar :size="40" :src="reply.author_avatar"></el-avatar>
                  <div class="author-info">
                    <div class="author-name">{{ reply.author_name }}</div>
                    <div class="post-time">{{ formatDate(reply.created_at) }}</div>
                  </div>
                </div>
                <div class="post-actions">
                  <el-button 
                    v-if="isCurrentUserAuthor(reply)" 
                    type="text" 
                    size="mini"
                    @click="editReply(reply)">
                    编辑
                  </el-button>
                  <el-button 
                    v-if="isCurrentUserAuthor(currentDiscussion) && !currentDiscussion.is_answered" 
                    type="text" 
                    size="mini"
                    @click="markAsAnswer(reply)">
                    标记为答案
                  </el-button>
                  <el-button 
                    type="text" 
                    size="mini"
                    @click="deleteReply(reply)">
                    删除
                  </el-button>
                </div>
              </div>
              <div class="post-content" v-html="reply.content"></div>
            </div>
          </div>
        </div>
        
        <!-- 添加回复 -->
        <div class="add-reply">
          <h3>添加回复</h3>
          <el-form :model="replyForm" :rules="replyRules" ref="replyForm">
            <el-form-item prop="content">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入回复内容"
                v-model="replyForm.content">
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitReply" :loading="submittingReply">发表回复</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
    
    <!-- 新建/编辑讨论对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
      <el-form :model="discussionForm" :rules="discussionRules" ref="discussionForm" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="discussionForm.title" placeholder="请输入标题"></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            type="textarea"
            :rows="6"
            placeholder="请输入内容"
            v-model="discussionForm.content">
          </el-input>
        </el-form-item>
        <el-form-item label="类型">
          <el-radio-group v-model="discussionForm.is_announcement">
            <el-radio :label="true">公告</el-radio>
            <el-radio :label="false">讨论</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="置顶" v-if="discussionForm.is_announcement">
          <el-switch v-model="discussionForm.is_pinned"></el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveDiscussion" :loading="submitting">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CourseDiscussions',
  props: {
    courseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      discussions: [],
      loading: true,
      searchQuery: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      
      // 讨论详情
      currentDiscussion: {},
      discussionVisible: false,
      discussionLoading: false,
      replies: [],
      
      // 回复表单
      replyForm: {
        content: ''
      },
      replyRules: {
        content: [
          { required: true, message: '请输入回复内容', trigger: 'blur' }
        ]
      },
      submittingReply: false,
      
      // 讨论表单
      dialogVisible: false,
      dialogTitle: '发布公告',
      discussionForm: {
        id: '',
        title: '',
        content: '',
        is_announcement: true,
        is_pinned: false
      },
      discussionRules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  methods: {
    async fetchDiscussions() {
      this.loading = true
      try {
        // 模拟API调用
        setTimeout(() => {
          // 模拟数据
          this.discussions = [
            {
              id: 1,
              title: '关于期末项目的要求',
              content: '各位同学，期末项目需要包含以下内容：1. 前端界面设计 2. 数据库设计 3. API实现...',
              author_id: 2,
              author_name: '李教授',
              author_avatar: null,
              created_at: '2023-09-20T10:00:00Z',
              updated_at: null,
              view_count: 45,
              reply_count: 5,
              is_pinned: true,
              is_announcement: true,
              is_answered: false
            },
            {
              id: 2,
              title: '课程资料更新通知',
              content: '我已经上传了新的课程资料，包括第7-10章的PPT和示例代码，请大家查看。',
              author_id: 2,
              author_name: '李教授',
              author_avatar: null,
              created_at: '2023-09-15T14:30:00Z',
              updated_at: null,
              view_count: 38,
              reply_count: 2,
              is_pinned: false,
              is_announcement: true,
              is_answered: false
            },
            {
              id: 3,
              title: '学生提问：React Hooks使用问题',
              content: '我在使用React Hooks时遇到了一些问题，特别是useEffect的依赖数组...',
              author_id: 5,
              author_name: '王五',
              author_avatar: null,
              created_at: '2023-09-18T16:30:00Z',
              updated_at: null,
              view_count: 15,
              reply_count: 3,
              is_pinned: false,
              is_announcement: false,
              is_answered: true
            }
          ]
          
          // 根据搜索条件过滤
          if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase()
            this.discussions = this.discussions.filter(d => 
              d.title.toLowerCase().includes(query) || 
              d.content.toLowerCase().includes(query)
            )
          }
          
          // 根据状态过滤
          if (this.statusFilter === 'answered') {
            this.discussions = this.discussions.filter(d => d.is_answered)
          } else if (this.statusFilter === 'unanswered') {
            this.discussions = this.discussions.filter(d => !d.is_answered)
          }
          
          this.total = this.discussions.length
          this.loading = false
        }, 1000)
      } catch (error) {
        console.error('获取讨论失败:', error)
        this.$message.error('获取讨论失败，请稍后再试')
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchDiscussions()
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD HH:mm')
    },
    isCurrentUserAuthor(item) {
      // 假设当前用户ID为2（教师）
      return item.author_id === 2
    },
    async viewDiscussion(discussion) {
      this.currentDiscussion = discussion
      this.discussionVisible = true
      this.discussionLoading = true
      
      try {
        // 模拟API调用
        setTimeout(() => {
          // 模拟回复数据
          this.replies = [
            {
              id: 1,
              discussion_id: discussion.id,
              author_id: 5,
              author_name: '王五',
              author_avatar: null,
              content: '谢谢老师的详细说明，我会按照要求完成项目。',
              created_at: '2023-09-20T11:30:00Z'
            },
            {
              id: 2,
              discussion_id: discussion.id,
              author_id: 6,
              author_name: '赵六',
              author_avatar: null,
              content: '请问项目截止日期是什么时候？',
              created_at: '2023-09-20T13:45:00Z'
            },
            {
              id: 3,
              discussion_id: discussion.id,
              author_id: 2,
              author_name: '李教授',
              author_avatar: null,
              content: '项目截止日期是12月15日，请大家注意时间安排。',
              created_at: '2023-09-20T14:20:00Z'
            }
          ]
          
          this.discussionLoading = false
        }, 800)
      } catch (error) {
        console.error('获取讨论详情失败:', error)
        this.$message.error('获取讨论详情失败，请稍后再试')
        this.discussionLoading = false
      }
    },
    showNewDiscussionDialog() {
      this.dialogTitle = '发布公告'
      this.discussionForm = {
        id: '',
        title: '',
        content: '',
        is_announcement: true,
        is_pinned: false
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.discussionForm.clearValidate()
      })
    },
    editDiscussion(discussion) {
      this.dialogTitle = '编辑' + (discussion.is_announcement ? '公告' : '讨论')
      this.discussionForm = {
        id: discussion.id,
        title: discussion.title,
        content: discussion.content,
        is_announcement: discussion.is_announcement,
        is_pinned: discussion.is_pinned
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.discussionForm.clearValidate()
      })
    },
    async saveDiscussion() {
      this.$refs.discussionForm.validate(async valid => {
        if (valid) {
          this.submitting = true
          
          try {
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            if (this.discussionForm.id) {
              // 更新现有讨论
              const index = this.discussions.findIndex(d => d.id === this.discussionForm.id)
              if (index !== -1) {
                this.discussions[index] = {
                  ...this.discussions[index],
                  title: this.discussionForm.title,
                  content: this.discussionForm.content,
                  is_announcement: this.discussionForm.is_announcement,
                  is_pinned: this.discussionForm.is_pinned,
                  updated_at: new Date()
                }
                
                // 如果当前正在查看这个讨论，也更新当前讨论
                if (this.currentDiscussion.id === this.discussionForm.id) {
                  this.currentDiscussion = {
                    ...this.currentDiscussion,
                    title: this.discussionForm.title,
                    content: this.discussionForm.content,
                    is_announcement: this.discussionForm.is_announcement,
                    is_pinned: this.discussionForm.is_pinned,
                    updated_at: new Date()
                  }
                }
              }
              
              this.$message.success('更新成功')
            } else {
              // 创建新讨论
              const newDiscussion = {
                id: this.discussions.length + 1,
                title: this.discussionForm.title,
                content: this.discussionForm.content,
                author_id: 2, // 假设当前教师ID为2
                author_name: '李教授',
                author_avatar: null,
                created_at: new Date(),
                updated_at: null,
                view_count: 0,
                reply_count: 0,
                is_pinned: this.discussionForm.is_pinned,
                is_announcement: this.discussionForm.is_announcement,
                is_answered: false
              }
              
              this.discussions.unshift(newDiscussion)
              this.total++
              
              this.$message.success(this.discussionForm.is_announcement ? '公告发布成功' : '讨论发布成功')
            }
            
            this.dialogVisible = false
          } catch (error) {
            console.error('保存失败:', error)
            this.$message.error('保存失败，请稍后再试')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    async submitReply() {
      this.$refs.replyForm.validate(async valid => {
        if (valid) {
          this.submittingReply = true
          
          try {
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 800))
            
            // 添加新回复
            const newReply = {
              id: this.replies.length + 1,
              discussion_id: this.currentDiscussion.id,
              author_id: 2, // 假设当前教师ID为2
              author_name: '李教授',
              author_avatar: null,
              content: this.replyForm.content,
              created_at: new Date()
            }
            
            this.replies.push(newReply)
            
            // 更新讨论的回复数
            const index = this.discussions.findIndex(d => d.id === this.currentDiscussion.id)
            if (index !== -1) {
              this.discussions[index].reply_count++
              this.currentDiscussion.reply_count++
            }
            
            this.$message.success('回复发表成功')
            this.replyForm.content = ''
          } catch (error) {
            console.error('发表回复失败:', error)
            this.$message.error('发表回复失败，请稍后再试')
          } finally {
            this.submittingReply = false
          }
        }
      })
    },
    async togglePin(discussion) {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 更新置顶状态
        discussion.is_pinned = !discussion.is_pinned
        
        this.$message.success(discussion.is_pinned ? '置顶成功' : '取消置顶成功')
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败，请稍后再试')
      }
    },
    async markAsAnswer(reply) {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 更新讨论的已解决状态
        this.currentDiscussion.is_answered = true
        
        // 更新讨论列表中的状态
        const index = this.discussions.findIndex(d => d.id === this.currentDiscussion.id)
        if (index !== -1) {
          this.discussions[index].is_answered = true
        }
        
        this.$message.success('已标记为答案')
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败，请稍后再试')
      }
    },
    async deleteReply(reply) {
      try {
        await this.$confirm('确定要删除这条回复吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 从回复列表中移除
        const index = this.replies.findIndex(r => r.id === reply.id)
        if (index !== -1) {
          this.replies.splice(index, 1)
        }
        
        // 更新讨论的回复数
        const discussionIndex = this.discussions.findIndex(d => d.id === this.currentDiscussion.id)
        if (discussionIndex !== -1) {
          this.discussions[discussionIndex].reply_count--
          this.currentDiscussion.reply_count--
        }
        
        this.$message.success('回复删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除回复失败:', error)
          this.$message.error('删除回复失败，请稍后再试')
        }
      }
    },
    editReply(reply) {
      this.replyForm.content = reply.content
      this.$message.info('编辑功能暂未实现，请重新输入回复内容')
    }
  },
  created() {
    this.fetchDiscussions()
  }
}
</script>

<style scoped>
.course-discussions {
  padding: 20px;
}

.discussions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  gap: 10px;
}

.loading-container, .empty-data {
  padding: 20px;
  text-align: center;
}

.discussions-list {
  margin-bottom: 20px;
}

.discussion-card {
  margin-bottom: 15px;
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.discussion-author {
  display: flex;
  align-items: center;
}

.author-info {
  margin-left: 10px;
}

.author-name {
  font-weight: bold;
}

.post-time {
  font-size: 12px;
  color: #909399;
}

.discussion-stats {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stat-item {
  font-size: 14px;
  color: #606266;
}

.discussion-title {
  margin: 0 0 10px 0;
  cursor: pointer;
}

.discussion-title:hover {
  color: #409EFF;
}

.discussion-preview {
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.discussion-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.post-item {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 4px;
}

.original-post {
  background-color: #f5f7fa;
}

.reply-post {
  border-bottom: 1px solid #ebeef5;
}

.post-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.post-author {
  display: flex;
  align-items: center;
}

.post-content {
  line-height: 1.6;
}

.replies-section {
  margin-top: 30px;
}

.empty-replies {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.add-reply {
  margin-top: 30px;
}
</style>
