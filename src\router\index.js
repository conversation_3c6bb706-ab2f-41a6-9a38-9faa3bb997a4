import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '../store'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/Home.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'login',
    component: () => import(/* webpackChunkName: "login" */ '../views/auth/Login.vue'),
    meta: { guest: true }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('../views/auth/Register.vue'),
    meta: { guest: true }
  },
  // 管理员路由
  {
    path: '/admin',
    component: () => import('../views/admin/AdminLayout.vue'),
    meta: { requiresAuth: true, requiresAdmin: true },
    children: [
      {
        path: '',
        name: 'admin-dashboard',
        component: () => import('../views/admin/Dashboard.vue')
      },
      {
        path: 'users',
        name: 'admin-users',
        component: () => import('../views/admin/UserManagement.vue')
      },
      {
        path: 'announcements',
        name: 'admin-announcements',
        component: () => import('../views/admin/AnnouncementManagement.vue')
      },
      {
        path: 'comments',
        name: 'admin-comments',
        component: () => import('../views/admin/CommentManagement.vue')
      },
      {
        path: 'discussions',
        name: 'admin-discussions',
        component: () => import('../views/admin/DiscussionManagement.vue')
      }
    ]
  },
  // 教师路由
  {
    path: '/teacher',
    component: () => import('../views/teacher/TeacherLayout.vue'),
    meta: { requiresAuth: true, requiresTeacher: true },
    children: [
      {
        path: '',
        name: 'teacher-dashboard',
        component: () => import('../views/teacher/Dashboard.vue')
      },
      {
        path: 'courses',
        name: 'teacher-courses',
        component: () => import('../views/teacher/CourseManagement.vue')
      },
      {
        path: 'courses/:id',
        name: 'teacher-course-detail',
        component: () => import('../views/teacher/CourseDetail.vue'),
        props: true
      },
      {
        path: 'tests/:id',
        name: 'teacher-test-edit',
        component: () => import('../views/teacher/TestEditor.vue'),
        props: true
      },
      {
        path: 'tests/:id/submissions',
        name: 'teacher-test-submissions',
        component: () => import('../views/teacher/TestSubmissions.vue'),
        props: true
      },
      {
        path: 'submissions/:id',
        name: 'teacher-grade-submission',
        component: () => import('../views/teacher/GradeSubmission.vue'),
        props: true
      },
      {
        path: 'resources',
        name: 'teacher-resources',
        component: () => import('../views/teacher/ResourceManagement.vue')
      },
      {
        path: 'tests',
        name: 'teacher-tests',
        component: () => import('../views/teacher/TestManagement.vue')
      }
      // 暂时注释掉其他缺少的组件
      /*
      {
        path: 'courses/:id/resources',
        name: 'teacher-course-resources',
        component: () => import('../views/teacher/ResourceManagement.vue'),
        props: true
      },
      {
        path: 'courses/:id/students',
        name: 'teacher-course-students',
        component: () => import('../views/teacher/StudentManagement.vue'),
        props: true
      },
      {
        path: 'courses/:id/attendance',
        name: 'teacher-course-attendance',
        component: () => import('../views/teacher/AttendanceManagement.vue'),
        props: true
      },
      {
        path: 'courses/:id/discussions',
        name: 'teacher-course-discussions',
        component: () => import('../views/teacher/DiscussionManagement.vue'),
        props: true
      }
      */
    ]
  },
  // 学生路由
  {
    path: '/student',
    component: () => import('../views/student/StudentLayout.vue'),
    meta: { requiresAuth: true, requiresStudent: true },
    children: [
      {
        path: '',
        name: 'student-dashboard',
        component: () => import('../views/student/Dashboard.vue')
      },
      {
        path: 'courses',
        name: 'student-courses',
        component: () => import('../views/student/CourseList.vue')
      },
      {
        path: 'courses/:id',
        name: 'student-course-detail',
        component: () => import('../views/student/CourseDetail.vue'),
        props: true
      },
      {
        path: 'courses/:id/test/:testId',
        name: 'student-take-test',
        component: () => import('../views/student/TakeTest.vue'),
        props: true
      },
      // 暂时注释掉其他缺少的组件
      /*
      {
        path: 'courses/:id/resources',
        name: 'student-course-resources',
        component: () => import('../views/student/Resources.vue'),
        props: true
      },
      {
        path: 'courses/:id/tests',
        name: 'student-course-tests',
        component: () => import('../views/student/Tests.vue'),
        props: true
      },
      {
        path: 'courses/:id/test/:testId',
        name: 'student-take-test',
        component: () => import('../views/student/TakeTest.vue'),
        props: true
      },
      {
        path: 'courses/:id/discussions',
        name: 'student-course-discussions',
        component: () => import('../views/student/Discussions.vue'),
        props: true
      }
      */
    ]
  },
  // 公共路由
  {
    path: '/announcements',
    name: 'announcements',
    component: () => import('../views/common/Announcements.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'profile',
    component: () => import('../views/NotFound.vue'), // 临时使用NotFound组件
    meta: { requiresAuth: true }
  },
  // 404页面
  {
    path: '*',
    name: 'not-found',
    component: () => import('../views/NotFound.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters.isAuthenticated
  const isAdmin = store.getters.isAdmin
  const isTeacher = store.getters.isTeacher
  const isStudent = store.getters.isStudent

  // 需要登录的路由
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isAuthenticated) {
      next({ name: 'login' })
    } else {
      // 需要管理员权限的路由
      if (to.matched.some(record => record.meta.requiresAdmin)) {
        if (isAdmin) {
          next()
        } else {
          next({ name: 'home' })
        }
      }
      // 需要教师权限的路由
      else if (to.matched.some(record => record.meta.requiresTeacher)) {
        if (isTeacher) {
          next()
        } else {
          next({ name: 'home' })
        }
      }
      // 需要学生权限的路由
      else if (to.matched.some(record => record.meta.requiresStudent)) {
        if (isStudent) {
          next()
        } else {
          next({ name: 'home' })
        }
      } else {
        next()
      }
    }
  }
  // 游客路由（未登录可访问）
  else if (to.matched.some(record => record.meta.guest)) {
    if (isAuthenticated) {
      next({ name: 'home' })
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
