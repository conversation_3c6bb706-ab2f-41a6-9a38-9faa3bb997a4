const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect } = require('../middleware/auth');

// 授权中间件 - 检查用户是否为教师或管理员
const authorizeTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'teacher' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: '权限不足，需要教师权限'
    });
  }
};

// 获取所有课程
router.get('/', protect, async (req, res) => {
  try {
    const { search, category, page = 1, limit = 10 } = req.query;

    // 构建查询条件
    let query = `
      SELECT c.*, u.username as teacher_name
      FROM courses c
      JOIN users u ON c.teacher_id = u.id
      WHERE 1=1
    `;
    const queryParams = [];

    // 添加搜索条件
    if (search) {
      query += ' AND (c.title LIKE ? OR c.description LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加分类过滤
    if (category) {
      query += ' AND c.category = ?';
      queryParams.push(category);
    }

    // 如果是学生，只显示已发布的课程
    if (req.user.role === 'student') {
      query += ' AND c.status = "published"';
    }

    // 如果是教师，只显示自己的课程
    if (req.user.role === 'teacher') {
      query += ' AND c.teacher_id = ?';
      queryParams.push(req.user.id);
    }

    // 添加排序和分页
    const offset = (page - 1) * limit;
    query += ' ORDER BY c.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), offset);

    // 执行查询
    const [courses] = await pool.query(query, queryParams);

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM courses c
      WHERE 1=1
    `;
    const countParams = [];

    // 添加搜索条件
    if (search) {
      countQuery += ' AND (c.title LIKE ? OR c.description LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加分类过滤
    if (category) {
      countQuery += ' AND c.category = ?';
      countParams.push(category);
    }

    // 如果是学生，只显示已发布的课程
    if (req.user.role === 'student') {
      countQuery += ' AND c.status = "published"';
    }

    // 如果是教师，只显示自己的课程
    if (req.user.role === 'teacher') {
      countQuery += ' AND c.teacher_id = ?';
      countParams.push(req.user.id);
    }

    const [countResult] = await pool.query(countQuery, countParams);
    const total = countResult[0].total;

    res.status(200).json({
      success: true,
      count: courses.length,
      total,
      data: courses
    });
  } catch (error) {
    console.error('获取课程失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程失败',
      error: error.message
    });
  }
});

// 获取单个课程详情
router.get('/:id', protect, async (req, res) => {
  try {
    const courseId = req.params.id;

    // 获取课程信息
    const [courses] = await pool.query(
      `SELECT c.*, u.username as teacher_name
       FROM courses c
       JOIN users u ON c.teacher_id = u.id
       WHERE c.id = ?`,
      [courseId]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    const course = courses[0];

    // 检查权限
    if (
      req.user.role === 'student' && course.status !== 'published' ||
      req.user.role === 'teacher' && course.teacher_id !== req.user.id && req.user.role !== 'admin'
    ) {
      return res.status(403).json({
        success: false,
        message: '您无权查看此课程'
      });
    }

    res.status(200).json({
      success: true,
      data: course
    });
  } catch (error) {
    console.error('获取课程详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程详情失败',
      error: error.message
    });
  }
});

// 创建新课程
router.post('/', protect, authorizeTeacher, async (req, res) => {
  try {
    let {
      title,
      description,
      category,
      cover_image,
      start_date,
      end_date,
      status = 'draft'
    } = req.body;

    // 确保日期格式正确
    if (start_date && typeof start_date === 'string') {
      // 如果是ISO格式，转换为YYYY-MM-DD格式
      if (start_date.includes('T')) {
        start_date = start_date.split('T')[0];
      }
    }

    if (end_date && typeof end_date === 'string') {
      // 如果是ISO格式，转换为YYYY-MM-DD格式
      if (end_date.includes('T')) {
        end_date = end_date.split('T')[0];
      }
    }

    // 插入课程记录
    console.log('创建课程数据:', {
      title,
      description,
      category,
      cover_image,
      teacher_id: req.user.id,
      start_date,
      end_date,
      status
    });

    const [result] = await pool.query(
      `INSERT INTO courses (
        title, description, category, cover_image,
        teacher_id, start_date, end_date, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        title,
        description,
        category,
        cover_image,
        req.user.id,
        start_date,
        end_date,
        status
      ]
    );

    // 获取新创建的课程
    const [newCourse] = await pool.query(
      `SELECT c.*, u.username as teacher_name
       FROM courses c
       JOIN users u ON c.teacher_id = u.id
       WHERE c.id = ?`,
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '课程创建成功',
      data: newCourse[0]
    });
  } catch (error) {
    console.error('创建课程失败:', error);
    res.status(500).json({
      success: false,
      message: '创建课程失败',
      error: error.message
    });
  }
});

// 更新课程信息
router.put('/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const courseId = req.params.id;
    let {
      title,
      description,
      category,
      cover_image,
      start_date,
      end_date,
      status
    } = req.body;

    // 确保日期格式正确
    if (start_date && typeof start_date === 'string') {
      // 如果是ISO格式，转换为YYYY-MM-DD格式
      if (start_date.includes('T')) {
        start_date = start_date.split('T')[0];
      }
    }

    if (end_date && typeof end_date === 'string') {
      // 如果是ISO格式，转换为YYYY-MM-DD格式
      if (end_date.includes('T')) {
        end_date = end_date.split('T')[0];
      }
    }

    // 获取课程信息
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    const course = courses[0];

    // 检查权限
    if (req.user.role === 'teacher' && course.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该课程的教师，无权修改'
      });
    }

    // 更新课程
    await pool.query(
      `UPDATE courses
       SET title = ?, description = ?, category = ?,
           cover_image = ?, start_date = ?, end_date = ?,
           status = ?, updated_at = NOW()
       WHERE id = ?`,
      [
        title,
        description,
        category,
        cover_image,
        start_date,
        end_date,
        status,
        courseId
      ]
    );

    // 获取更新后的课程
    const [updatedCourse] = await pool.query(
      `SELECT c.*, u.username as teacher_name
       FROM courses c
       JOIN users u ON c.teacher_id = u.id
       WHERE c.id = ?`,
      [courseId]
    );

    res.status(200).json({
      success: true,
      message: '课程更新成功',
      data: updatedCourse[0]
    });
  } catch (error) {
    console.error('更新课程失败:', error);
    res.status(500).json({
      success: false,
      message: '更新课程失败',
      error: error.message
    });
  }
});

// 删除课程
router.delete('/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const courseId = req.params.id;

    // 获取课程信息
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    const course = courses[0];

    // 检查权限
    if (req.user.role === 'teacher' && course.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该课程的教师，无权删除'
      });
    }

    // 删除课程
    await pool.query('DELETE FROM courses WHERE id = ?', [courseId]);

    res.status(200).json({
      success: true,
      message: '课程删除成功'
    });
  } catch (error) {
    console.error('删除课程失败:', error);
    res.status(500).json({
      success: false,
      message: '删除课程失败',
      error: error.message
    });
  }
});

// 获取课程学生列表
router.get('/:id/students', protect, authorizeTeacher, async (req, res) => {
  try {
    const courseId = req.params.id;

    // 获取课程信息
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    if (courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    const course = courses[0];

    // 检查权限
    if (req.user.role === 'teacher' && course.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该课程的教师，无权查看学生列表'
      });
    }

    // 获取学生列表
    const [students] = await pool.query(
      `SELECT u.id, u.username, u.email, u.avatar, e.enrollment_date
       FROM users u
       JOIN enrollments e ON u.id = e.student_id
       WHERE e.course_id = ? AND u.role = 'student'
       ORDER BY e.enrollment_date DESC`,
      [courseId]
    );

    res.status(200).json({
      success: true,
      count: students.length,
      data: students
    });
  } catch (error) {
    console.error('获取课程学生列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程学生列表失败',
      error: error.message
    });
  }
});

// 学生选课
router.post('/:id/enroll', protect, async (req, res) => {
  try {
    const courseId = req.params.id;
    const studentId = req.user.id;

    console.log('选课请求:', {
      courseId,
      studentId,
      userRole: req.user.role,
      userName: req.user.username
    });

    // 检查用户是否为学生
    if (req.user.role !== 'student') {
      console.log('选课失败: 用户不是学生');
      return res.status(403).json({
        success: false,
        message: '只有学生可以选课'
      });
    }

    // 检查课程是否存在
    const [courses] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    if (courses.length === 0) {
      console.log('选课失败: 课程不存在');
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    const course = courses[0];
    console.log('课程信息:', course);

    // 检查课程是否已发布
    if (course.status !== 'published') {
      console.log('选课失败: 课程未发布');
      return res.status(400).json({
        success: false,
        message: '该课程尚未发布，无法选课'
      });
    }

    // 检查是否已选课
    const [enrollments] = await pool.query(
      'SELECT * FROM enrollments WHERE course_id = ? AND student_id = ?',
      [courseId, studentId]
    );

    console.log('选课记录:', enrollments);

    if (enrollments.length > 0) {
      console.log('选课失败: 已经选修了该课程');
      return res.status(400).json({
        success: false,
        message: '您已经选修了该课程'
      });
    }

    // 创建选课记录
    await pool.query(
      'INSERT INTO enrollments (course_id, student_id, enrollment_date) VALUES (?, ?, NOW())',
      [courseId, studentId]
    );

    res.status(201).json({
      success: true,
      message: '选课成功'
    });
  } catch (error) {
    console.error('选课失败:', error);
    res.status(500).json({
      success: false,
      message: '选课失败',
      error: error.message
    });
  }
});

// 学生退课
router.delete('/:id/enroll', protect, async (req, res) => {
  try {
    const courseId = req.params.id;
    const studentId = req.user.id;

    // 检查用户是否为学生
    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: '只有学生可以退课'
      });
    }

    // 检查是否已选课
    const [enrollments] = await pool.query(
      'SELECT * FROM enrollments WHERE course_id = ? AND student_id = ?',
      [courseId, studentId]
    );

    if (enrollments.length === 0) {
      return res.status(400).json({
        success: false,
        message: '您尚未选修该课程'
      });
    }

    // 删除选课记录
    await pool.query(
      'DELETE FROM enrollments WHERE course_id = ? AND student_id = ?',
      [courseId, studentId]
    );

    res.status(200).json({
      success: true,
      message: '退课成功'
    });
  } catch (error) {
    console.error('退课失败:', error);
    res.status(500).json({
      success: false,
      message: '退课失败',
      error: error.message
    });
  }
});

// 获取学生选课状态
router.get('/:id/enrollment-status', protect, async (req, res) => {
  try {
    const courseId = req.params.id;
    const studentId = req.user.id;

    // 检查用户是否为学生
    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: '只有学生可以查看选课状态'
      });
    }

    // 检查是否已选课
    const [enrollments] = await pool.query(
      'SELECT * FROM enrollments WHERE course_id = ? AND student_id = ?',
      [courseId, studentId]
    );

    res.status(200).json({
      success: true,
      isEnrolled: enrollments.length > 0
    });
  } catch (error) {
    console.error('获取选课状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取选课状态失败',
      error: error.message
    });
  }
});

// 获取学生已选课程
router.get('/enrolled', protect, async (req, res) => {
  try {
    // 只有学生可以访问此接口
    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: '只有学生可以查看已选课程'
      });
    }

    const [rows] = await pool.query(
      `SELECT c.*, e.enrolled_at
       FROM courses c
       JOIN enrollments e ON c.id = e.course_id
       WHERE e.student_id = ?
       ORDER BY e.enrolled_at DESC`,
      [req.user.id]
    );

    res.status(200).json({
      success: true,
      count: rows.length,
      data: rows
    });
  } catch (error) {
    console.error('获取已选课程失败:', error);
    res.status(500).json({
      success: false,
      message: '获取已选课程失败',
      error: error.message
    });
  }
});

module.exports = router;
