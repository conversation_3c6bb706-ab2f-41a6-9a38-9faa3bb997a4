<template>
  <div class="course-detail">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else>
      <!-- 课程头部信息 -->
      <div class="course-header">
        <div class="course-cover" v-if="course.cover_image">
          <img :src="course.cover_image" alt="课程封面">
        </div>
        <div class="course-info">
          <div class="course-title-row">
            <h1>{{ course.title }}</h1>
            <el-tag :type="getCourseStatusType(course.status)">
              {{ getCourseStatusText(course.status) }}
            </el-tag>
          </div>
          <p class="course-description">{{ course.description }}</p>
          <div class="course-meta">
            <span><i class="el-icon-date"></i> {{ formatDate(course.start_date) }} - {{ formatDate(course.end_date) }}</span>
            <span><i class="el-icon-user"></i> {{ course.student_count || 0 }} 名学生</span>
            <span><i class="el-icon-collection-tag"></i> {{ course.category }}</span>
            <span v-if="course.course_code"><i class="el-icon-key"></i> 课程代码: {{ course.course_code }}</span>
          </div>
        </div>
        <div class="course-actions">
          <el-button type="primary" @click="editCourse">编辑课程</el-button>
          <el-dropdown trigger="click" @command="handleCommand">
            <el-button type="default">
              更多操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="publish" v-if="course.status === 'draft'">发布课程</el-dropdown-item>
              <el-dropdown-item command="archive" v-if="course.status === 'published'">归档课程</el-dropdown-item>
              <el-dropdown-item command="reactivate" v-if="course.status === 'archived'">重新激活</el-dropdown-item>
              <el-dropdown-item command="generateCode" v-if="!course.course_code">生成课程代码</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除课程</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <!-- 课程内容导航 -->
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="概览" name="overview">
          <course-overview :course="course" @navigate="handleTabChange" />
        </el-tab-pane>
        <el-tab-pane label="学生" name="students">
          <course-students :course-id="courseId" />
        </el-tab-pane>
        <el-tab-pane label="资源" name="resources">
          <resource-management :course-id="courseId" />
        </el-tab-pane>
        <el-tab-pane label="测试" name="tests">
          <test-management :course-id="courseId" />
        </el-tab-pane>
        <el-tab-pane label="讨论" name="discussions">
          <course-discussions :course-id="courseId" />
        </el-tab-pane>
        <el-tab-pane label="出勤" name="attendance">
          <course-attendance :course-id="courseId" />
        </el-tab-pane>
      </el-tabs>

      <!-- 编辑课程对话框 -->
      <el-dialog title="编辑课程" :visible.sync="dialogVisible" width="50%">
        <el-form :model="courseForm" :rules="courseRules" ref="courseForm" label-width="100px">
          <el-form-item label="课程名称" prop="title">
            <el-input v-model="courseForm.title"></el-input>
          </el-form-item>
          <el-form-item label="课程描述" prop="description">
            <el-input type="textarea" v-model="courseForm.description" rows="4"></el-input>
          </el-form-item>
          <el-form-item label="课程类别" prop="category">
            <el-select v-model="courseForm.category" placeholder="请选择课程类别" style="width: 100%">
              <el-option label="计算机科学" value="计算机科学"></el-option>
              <el-option label="软件工程" value="软件工程"></el-option>
              <el-option label="数据科学" value="数据科学"></el-option>
              <el-option label="人工智能" value="人工智能"></el-option>
              <el-option label="网络安全" value="网络安全"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker v-model="courseForm.startDate" type="date" placeholder="选择开始日期" style="width: 100%"></el-date-picker>
          </el-form-item>
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker v-model="courseForm.endDate" type="date" placeholder="选择结束日期" style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCourse" :loading="saving">保存</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// 导入子组件
import CourseOverview from '@/components/teacher/CourseOverview.vue'
import ResourceManagement from '@/components/teacher/ResourceManagement.vue'
import TestManagement from '@/components/teacher/TestManagement.vue'

// 这些组件将在后续步骤中创建
const CourseStudents = {
  props: ['courseId'],
  template: '<div>学生管理组件</div>'
}

const CourseDiscussions = {
  props: ['courseId'],
  template: '<div>讨论管理组件</div>'
}

const CourseAttendance = {
  props: ['courseId'],
  template: '<div>出勤管理组件</div>'
}

export default {
  name: 'CourseDetail',
  components: {
    CourseOverview,
    CourseStudents,
    ResourceManagement,
    TestManagement,
    CourseDiscussions,
    CourseAttendance
  },
  props: {
    id: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      courseId: parseInt(this.id),
      course: {},
      loading: true,
      activeTab: 'overview',
      dialogVisible: false,
      courseForm: {
        title: '',
        description: '',
        category: '',
        startDate: '',
        endDate: ''
      },
      courseRules: {
        title: [
          { required: true, message: '请输入课程名称', trigger: 'blur' },
          { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入课程描述', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择课程类别', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ]
      },
      saving: false
    }
  },
  methods: {
    async fetchCourseDetails() {
      this.loading = true
      try {
        // 实际应该从API获取数据
        // const response = await this.$http.get(`/teacher/courses/${this.courseId}`)
        // this.course = response.data.data

        // 使用模拟数据
        setTimeout(() => {
          this.course = {
            id: this.courseId,
            title: 'Web开发基础',
            description: '学习HTML、CSS和JavaScript的基础知识，构建响应式网站。',
            cover_image: null,
            teacher_id: 2,
            status: 'published',
            category: '计算机科学',
            start_date: '2023-09-01',
            end_date: '2024-01-15',
            course_code: 'ABC123',
            student_count: 25,
            resource_count: 12,
            test_count: 3,
            created_at: '2023-08-15T10:30:00Z',
            updated_at: '2023-08-20T14:15:00Z'
          }
          this.loading = false
        }, 1000)
      } catch (error) {
        console.error('获取课程详情失败:', error)
        this.$message.error('获取课程详情失败，请稍后再试')
        this.loading = false
      }
    },
    formatDate(date) {
      return this.$moment(date).format('YYYY-MM-DD')
    },
    getCourseStatusType(status) {
      const types = {
        'draft': 'info',
        'published': 'success',
        'archived': 'warning'
      }
      return types[status] || 'info'
    },
    getCourseStatusText(status) {
      const texts = {
        'draft': '草稿',
        'published': '已发布',
        'archived': '已归档'
      }
      return texts[status] || status
    },
    editCourse() {
      this.courseForm = {
        title: this.course.title,
        description: this.course.description,
        category: this.course.category,
        startDate: this.course.start_date,
        endDate: this.course.end_date
      }
      this.dialogVisible = true
    },
    async saveCourse() {
      this.$refs.courseForm.validate(async valid => {
        if (valid) {
          this.saving = true
          try {
            // 实际应该调用API
            // await this.$http.put(`/teacher/courses/${this.courseId}`, this.courseForm)

            // 模拟API调用
            setTimeout(() => {
              this.$message.success('课程更新成功')

              // 更新本地数据
              this.course = {
                ...this.course,
                title: this.courseForm.title,
                description: this.courseForm.description,
                category: this.courseForm.category,
                start_date: this.courseForm.startDate,
                end_date: this.courseForm.endDate
              }

              this.dialogVisible = false
              this.saving = false
            }, 1000)
          } catch (error) {
            console.error('保存课程失败:', error)
            this.$message.error('保存课程失败，请稍后再试')
            this.saving = false
          }
        }
      })
    },
    async handleCommand(command) {
      try {
        if (command === 'publish') {
          await this.changeCourseStatus('published', '发布')
        } else if (command === 'archive') {
          await this.changeCourseStatus('archived', '归档')
        } else if (command === 'reactivate') {
          await this.changeCourseStatus('published', '重新激活')
        } else if (command === 'generateCode') {
          await this.generateCourseCode()
        } else if (command === 'delete') {
          await this.deleteCourse()
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败，请稍后再试')
      }
    },
    async changeCourseStatus(status, actionText) {
      try {
        await this.$confirm(`确定要${actionText}课程 "${this.course.title}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 实际应该调用API
        // await this.$http.patch(`/teacher/courses/${this.courseId}/status`, { status })

        // 模拟API调用
        this.$message.success(`课程${actionText}成功`)

        // 更新本地数据
        this.course.status = status
      } catch (error) {
        if (error !== 'cancel') {
          throw error
        }
      }
    },
    async generateCourseCode() {
      try {
        // 实际应该调用API
        // const response = await this.$http.post(`/teacher/courses/${this.courseId}/generate-code`)
        // const courseCode = response.data.courseCode

        // 模拟API调用
        const courseCode = Math.random().toString(36).substring(2, 8).toUpperCase()
        this.$message.success(`课程代码生成成功: ${courseCode}`)

        // 更新本地数据
        this.course.course_code = courseCode
      } catch (error) {
        throw error
      }
    },
    async deleteCourse() {
      try {
        await this.$confirm(`确定要删除课程 "${this.course.title}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 实际应该调用API
        // await this.$http.delete(`/teacher/courses/${this.courseId}`)

        // 模拟API调用
        this.$message.success('课程删除成功')

        // 返回课程列表页面
        this.$router.push('/teacher/courses')
      } catch (error) {
        if (error !== 'cancel') {
          throw error
        }
      }
    },

    // 处理标签页切换
    handleTabChange(tab) {
      this.activeTab = tab
    }
  },
  created() {
    this.fetchCourseDetails()
  }
}
</script>

<style scoped>
.course-detail {
  padding: 20px;
}

.loading-container {
  padding: 20px;
}

.course-header {
  display: flex;
  margin-bottom: 30px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
}

.course-cover {
  width: 200px;
  height: 150px;
  overflow: hidden;
  margin-right: 20px;
  border-radius: 4px;
}

.course-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-info {
  flex: 1;
}

.course-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.course-title-row h1 {
  margin: 0;
  margin-right: 10px;
}

.course-description {
  margin-bottom: 15px;
  color: #606266;
}

.course-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #909399;
}

.course-actions {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 10px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .course-header {
    flex-direction: column;
  }

  .course-cover {
    width: 100%;
    margin-right: 0;
    margin-bottom: 15px;
  }

  .course-actions {
    margin-top: 15px;
    flex-direction: row;
  }
}
</style>
