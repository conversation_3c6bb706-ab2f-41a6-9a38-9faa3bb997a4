const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const { protect } = require('../middleware/auth');

// 授权中间件 - 检查用户是否为教师或管理员
const authorizeTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'teacher' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: '权限不足，需要教师权限'
    });
  }
};

// 获取课程测试列表
router.get('/courses/:id/tests', protect, async (req, res) => {
  try {
    const courseId = req.params.id;
    const { page = 1, limit = 10 } = req.query;
    
    // 构建查询
    let query = `
      SELECT t.*, u.username as creator_name 
      FROM tests t 
      JOIN users u ON t.creator_id = u.id 
      WHERE t.course_id = ?
    `;
    
    // 如果是学生，只显示已发布的测试
    if (req.user.role === 'student') {
      query += ' AND t.status = "published"';
    }
    
    query += ' ORDER BY t.created_at DESC LIMIT ? OFFSET ?';
    
    // 执行查询
    const offset = (page - 1) * limit;
    const [tests] = await pool.query(query, [courseId, parseInt(limit), offset]);
    
    // 获取总数
    const [countResult] = await pool.query(
      'SELECT COUNT(*) as total FROM tests WHERE course_id = ?',
      [courseId]
    );
    const total = countResult[0].total;
    
    res.status(200).json({
      success: true,
      count: tests.length,
      total,
      data: tests
    });
  } catch (error) {
    console.error('获取课程测试失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程测试失败',
      error: error.message
    });
  }
});

// 创建新测试
router.post('/courses/:id/tests', protect, authorizeTeacher, async (req, res) => {
  try {
    const courseId = req.params.id;
    const { 
      title, 
      description, 
      timeLimit, 
      startTime, 
      endTime, 
      randomizeQuestions, 
      showResults 
    } = req.body;
    
    // 检查课程是否存在
    const [courseRows] = await pool.query(
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );
    
    if (courseRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }
    
    // 检查当前用户是否为课程教师
    if (req.user.role === 'teacher' && courseRows[0].teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该课程的教师，无权创建测试'
      });
    }
    
    // 插入测试记录
    const [result] = await pool.query(
      `INSERT INTO tests (
        title, description, course_id, creator_id, time_limit, 
        start_time, end_time, randomize_questions, show_results, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        title, 
        description, 
        courseId, 
        req.user.id, 
        timeLimit, 
        startTime, 
        endTime, 
        randomizeQuestions ? 1 : 0, 
        showResults ? 1 : 0, 
        'draft'
      ]
    );
    
    // 获取新创建的测试
    const [newTest] = await pool.query(
      `SELECT t.*, u.username as creator_name 
       FROM tests t 
       JOIN users u ON t.creator_id = u.id 
       WHERE t.id = ?`,
      [result.insertId]
    );
    
    res.status(201).json({
      success: true,
      message: '测试创建成功',
      data: newTest[0]
    });
  } catch (error) {
    console.error('创建测试失败:', error);
    res.status(500).json({
      success: false,
      message: '创建测试失败',
      error: error.message
    });
  }
});

// 获取单个测试详情
router.get('/:id', protect, async (req, res) => {
  try {
    const testId = req.params.id;
    
    // 获取测试信息
    const [tests] = await pool.query(
      `SELECT t.*, u.username as creator_name, c.title as course_title
       FROM tests t 
       JOIN users u ON t.creator_id = u.id 
       JOIN courses c ON t.course_id = c.id
       WHERE t.id = ?`,
      [testId]
    );
    
    if (tests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }
    
    const test = tests[0];
    
    // 检查权限
    if (req.user.role === 'student' && test.status !== 'published') {
      return res.status(403).json({
        success: false,
        message: '该测试尚未发布'
      });
    }
    
    // 获取测试问题
    const [questions] = await pool.query(
      `SELECT * FROM test_questions WHERE test_id = ? ORDER BY sort_order`,
      [testId]
    );
    
    // 如果是学生，不返回答案
    if (req.user.role === 'student') {
      questions.forEach(q => {
        delete q.answer;
      });
    }
    
    test.questions = questions;
    
    res.status(200).json({
      success: true,
      data: test
    });
  } catch (error) {
    console.error('获取测试详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取测试详情失败',
      error: error.message
    });
  }
});

// 更新测试信息
router.put('/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const testId = req.params.id;
    const { 
      title, 
      description, 
      timeLimit, 
      startTime, 
      endTime, 
      randomizeQuestions, 
      showResults,
      status
    } = req.body;
    
    // 获取测试信息
    const [tests] = await pool.query(
      'SELECT * FROM tests WHERE id = ?',
      [testId]
    );
    
    if (tests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }
    
    const test = tests[0];
    
    // 检查权限
    if (req.user.role === 'teacher' && test.creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权修改'
      });
    }
    
    // 更新测试
    await pool.query(
      `UPDATE tests 
       SET title = ?, description = ?, time_limit = ?, 
           start_time = ?, end_time = ?, randomize_questions = ?, 
           show_results = ?, status = ?, updated_at = NOW()
       WHERE id = ?`,
      [
        title, 
        description, 
        timeLimit, 
        startTime, 
        endTime, 
        randomizeQuestions ? 1 : 0, 
        showResults ? 1 : 0, 
        status,
        testId
      ]
    );
    
    // 获取更新后的测试
    const [updatedTest] = await pool.query(
      `SELECT t.*, u.username as creator_name 
       FROM tests t 
       JOIN users u ON t.creator_id = u.id 
       WHERE t.id = ?`,
      [testId]
    );
    
    res.status(200).json({
      success: true,
      message: '测试更新成功',
      data: updatedTest[0]
    });
  } catch (error) {
    console.error('更新测试失败:', error);
    res.status(500).json({
      success: false,
      message: '更新测试失败',
      error: error.message
    });
  }
});

// 删除测试
router.delete('/:id', protect, authorizeTeacher, async (req, res) => {
  try {
    const testId = req.params.id;
    
    // 获取测试信息
    const [tests] = await pool.query(
      'SELECT * FROM tests WHERE id = ?',
      [testId]
    );
    
    if (tests.length === 0) {
      return res.status(404).json({
        success: false,
        message: '测试不存在'
      });
    }
    
    const test = tests[0];
    
    // 检查权限
    if (req.user.role === 'teacher' && test.creator_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '您不是该测试的创建者，无权删除'
      });
    }
    
    // 删除测试
    await pool.query('DELETE FROM tests WHERE id = ?', [testId]);
    
    res.status(200).json({
      success: true,
      message: '测试删除成功'
    });
  } catch (error) {
    console.error('删除测试失败:', error);
    res.status(500).json({
      success: false,
      message: '删除测试失败',
      error: error.message
    });
  }
});

module.exports = router;
