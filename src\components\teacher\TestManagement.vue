<template>
  <div class="test-management">
    <div class="management-header">
      <div class="filter-bar">
        <el-input
          placeholder="搜索测试"
          v-model="searchQuery"
          prefix-icon="el-icon-search"
          clearable
          @clear="fetchTests">
        </el-input>
        <el-select v-model="statusFilter" placeholder="测试状态" clearable @change="fetchTests">
          <el-option label="草稿" value="draft"></el-option>
          <el-option label="已发布" value="published"></el-option>
          <el-option label="已关闭" value="closed"></el-option>
        </el-select>
        <el-button type="primary" @click="fetchTests">搜索</el-button>
      </div>
      <el-button type="success" @click="showCreateTestDialog">创建新测试</el-button>
    </div>
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated />
    </div>
    
    <div v-else-if="tests.length === 0" class="empty-data">
      <el-empty description="暂无测试">
        <el-button type="primary" @click="showCreateTestDialog">创建新测试</el-button>
      </el-empty>
    </div>
    
    <div v-else>
      <el-table :data="tests" style="width: 100%" border>
        <el-table-column prop="title" label="测试标题" min-width="200">
          <template slot-scope="scope">
            <el-link type="primary" @click="navigateToTest(scope.row.id)">{{ scope.row.title }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_score" label="总分" width="80" align="center"></el-table-column>
        <el-table-column prop="time_limit" label="时间限制" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.time_limit ? `${scope.row.time_limit}分钟` : '无限制' }}
          </template>
        </el-table-column>
        <el-table-column label="开始时间" width="170">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.start_time) }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" width="170">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.end_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template slot-scope="scope">
            <el-button 
              type="text" 
              size="small" 
              @click="navigateToTest(scope.row.id)">
              管理
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="editTest(scope.row)">
              编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="viewSubmissions(scope.row.id)">
              查看提交
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              class="danger-button"
              @click="deleteTest(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          @current-change="handlePageChange"
          :current-page.sync="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="total">
        </el-pagination>
      </div>
    </div>
    
    <!-- 创建/编辑测试对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
      <el-form :model="testForm" :rules="testRules" ref="testForm" label-width="100px">
        <el-form-item label="测试标题" prop="title">
          <el-input v-model="testForm.title" placeholder="请输入测试标题"></el-input>
        </el-form-item>
        <el-form-item label="测试描述" prop="description">
          <el-input 
            type="textarea" 
            v-model="testForm.description" 
            :rows="3"
            placeholder="请输入测试描述">
          </el-input>
        </el-form-item>
        <el-form-item label="时间限制" prop="timeLimit">
          <el-input-number 
            v-model="testForm.timeLimit" 
            :min="0" 
            :step="5"
            placeholder="分钟，0表示无限制">
          </el-input-number>
          <span class="form-tip">分钟，0表示无限制</span>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            v-model="testForm.startTime"
            type="datetime"
            placeholder="选择开始时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="testForm.endTime"
            type="datetime"
            placeholder="选择结束时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="随机排序" prop="randomizeQuestions">
          <el-switch v-model="testForm.randomizeQuestions"></el-switch>
          <span class="form-tip">开启后，每个学生看到的题目顺序将随机排列</span>
        </el-form-item>
        <el-form-item label="显示结果" prop="showResults">
          <el-switch v-model="testForm.showResults"></el-switch>
          <span class="form-tip">开启后，学生提交后可以查看自己的得分</span>
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="testForm.id">
          <el-select v-model="testForm.status" placeholder="请选择测试状态" style="width: 100%">
            <el-option label="草稿" value="draft"></el-option>
            <el-option label="已发布" value="published"></el-option>
            <el-option label="已关闭" value="closed"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTest" :loading="saving">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TestManagement',
  props: {
    courseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      tests: [],
      loading: false,
      searchQuery: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '创建新测试',
      testForm: {
        id: null,
        title: '',
        description: '',
        timeLimit: 30,
        startTime: '',
        endTime: '',
        randomizeQuestions: false,
        showResults: true,
        status: 'draft'
      },
      testRules: {
        title: [
          { required: true, message: '请输入测试标题', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ]
      },
      saving: false
    }
  },
  methods: {
    async fetchTests() {
      this.loading = true
      try {
        const response = await this.$http.get(`/api/tests/courses/${this.courseId}/tests`, {
          params: {
            search: this.searchQuery,
            status: this.statusFilter,
            page: this.currentPage,
            limit: this.pageSize
          }
        })
        
        if (response.data.success) {
          this.tests = response.data.data
          this.total = response.data.total
        } else {
          throw new Error(response.data.message || '获取测试失败')
        }
      } catch (error) {
        console.error('获取测试失败:', error)
        this.$message.error('获取测试失败，请稍后再试')
        
        // 使用模拟数据（仅在开发环境或API失败时）
        this.tests = [
          {
            id: 1,
            title: 'HTML和CSS基础测验',
            description: 'HTML标签和CSS选择器的基础知识测试。',
            status: 'published',
            total_score: 20,
            time_limit: 30,
            start_time: '2023-09-15 10:00:00',
            end_time: '2023-09-15 11:00:00',
            randomize_questions: false,
            show_results: true,
            created_at: '2023-09-01T10:30:00Z'
          },
          {
            id: 2,
            title: 'JavaScript基础测试',
            description: 'JavaScript语法、变量、函数和对象的基础知识测试。',
            status: 'draft',
            total_score: 30,
            time_limit: 45,
            start_time: '2023-10-01 14:00:00',
            end_time: '2023-10-01 15:30:00',
            randomize_questions: true,
            show_results: true,
            created_at: '2023-09-10T14:20:00Z'
          }
        ]
        this.total = 2
      } finally {
        this.loading = false
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchTests()
    },
    formatDateTime(dateTime) {
      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm')
    },
    getStatusType(status) {
      const types = {
        'draft': 'info',
        'published': 'success',
        'closed': 'warning'
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        'draft': '草稿',
        'published': '已发布',
        'closed': '已关闭'
      }
      return texts[status] || status
    },
    navigateToTest(testId) {
      this.$router.push(`/teacher/tests/${testId}`)
    },
    viewSubmissions(testId) {
      this.$router.push(`/teacher/tests/${testId}/submissions`)
    },
    showCreateTestDialog() {
      this.dialogTitle = '创建新测试'
      this.testForm = {
        id: null,
        title: '',
        description: '',
        timeLimit: 30,
        startTime: '',
        endTime: '',
        randomizeQuestions: false,
        showResults: true,
        status: 'draft'
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.testForm.clearValidate()
      })
    },
    editTest(test) {
      this.dialogTitle = '编辑测试'
      this.testForm = {
        id: test.id,
        title: test.title,
        description: test.description,
        timeLimit: test.time_limit,
        startTime: test.start_time,
        endTime: test.end_time,
        randomizeQuestions: Boolean(test.randomize_questions),
        showResults: Boolean(test.show_results),
        status: test.status
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.testForm.clearValidate()
      })
    },
    async deleteTest(test) {
      try {
        await this.$confirm(`确定要删除测试 "${test.title}" 吗？此操作不可逆。`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await this.$http.delete(`/api/tests/${test.id}`)
        
        if (response.data.success) {
          this.$message.success('测试删除成功')
          
          // 更新测试列表
          this.tests = this.tests.filter(t => t.id !== test.id)
          this.total--
        } else {
          throw new Error(response.data.message || '删除测试失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除测试失败:', error)
          this.$message.error(error.message || '删除测试失败，请稍后再试')
        }
      }
    },
    async saveTest() {
      this.$refs.testForm.validate(async valid => {
        if (valid) {
          // 检查开始时间和结束时间
          if (new Date(this.testForm.endTime) <= new Date(this.testForm.startTime)) {
            this.$message.error('结束时间必须晚于开始时间')
            return
          }
          
          this.saving = true
          
          try {
            const testData = {
              title: this.testForm.title,
              description: this.testForm.description,
              timeLimit: this.testForm.timeLimit,
              startTime: this.testForm.startTime,
              endTime: this.testForm.endTime,
              randomizeQuestions: this.testForm.randomizeQuestions,
              showResults: this.testForm.showResults
            }
            
            if (this.testForm.id) {
              // 更新现有测试
              testData.status = this.testForm.status
              
              const response = await this.$http.put(`/api/tests/${this.testForm.id}`, testData)
              
              if (response.data.success) {
                // 更新本地测试列表
                const index = this.tests.findIndex(t => t.id === this.testForm.id)
                if (index !== -1) {
                  this.tests[index] = response.data.data
                }
                
                this.$message.success('测试更新成功')
                this.dialogVisible = false
              } else {
                throw new Error(response.data.message || '更新测试失败')
              }
            } else {
              // 创建新测试
              const response = await this.$http.post(`/api/tests/courses/${this.courseId}/tests`, testData)
              
              if (response.data.success) {
                // 添加新测试到列表
                this.tests.unshift(response.data.data)
                this.total++
                
                this.$message.success('测试创建成功')
                this.dialogVisible = false
              } else {
                throw new Error(response.data.message || '创建测试失败')
              }
            }
          } catch (error) {
            console.error('保存测试失败:', error)
            this.$message.error(error.message || '保存测试失败，请稍后再试')
          } finally {
            this.saving = false
          }
        }
      })
    }
  },
  created() {
    this.fetchTests()
  }
}
</script>

<style scoped>
.test-management {
  padding: 10px 0;
}

.management-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  gap: 10px;
}

.filter-bar .el-input {
  width: 300px;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.danger-button {
  color: #F56C6C;
}

.danger-button:hover {
  color: #F78989;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    gap: 10px;
  }

  .filter-bar {
    flex-direction: column;
    width: 100%;
  }

  .filter-bar .el-input {
    width: 100%;
  }
}
</style>
