{"name": "elearning-backend", "version": "1.0.0", "description": "Backend for E-learning platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1"}, "devDependencies": {"nodemon": "^2.0.22"}}